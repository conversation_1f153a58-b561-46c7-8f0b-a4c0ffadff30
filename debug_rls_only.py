#!/usr/bin/env python3
"""
专门测试RLS策略的脚本，不依赖FastAPI应用
"""
import os
from sqlmodel import Session, text, create_engine

# 设置测试环境变量
os.environ["TESTING"] = "true"

def test_rls_policies_in_test_db():
    """在测试数据库中测试RLS策略"""
    print("=== 测试数据库中的RLS策略 ===")
    
    TEST_DATABASE_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
    test_engine = create_engine(TEST_DATABASE_URL, echo=False)
    
    with Session(test_engine) as session:
        # 1. 检查表是否存在
        tables = session.exec(text("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'member_fixed_slot_locks'
        """)).all()
        
        if not tables:
            print("❌ 表 member_fixed_slot_locks 不存在")
            return False
        
        print("✅ 表 member_fixed_slot_locks 存在")
        
        # 2. 检查RLS是否启用
        rls_status = session.exec(text("""
            SELECT relrowsecurity 
            FROM pg_class 
            WHERE relname = 'member_fixed_slot_locks'
        """)).first()
        
        if rls_status:
            print(f"RLS状态: {'启用' if rls_status else '禁用'}")
        else:
            print("❌ 无法获取RLS状态")
            return False
        
        # 3. 检查RLS策略
        policies = session.exec(text("""
            SELECT policyname, qual 
            FROM pg_policies 
            WHERE tablename = 'member_fixed_slot_locks'
        """)).all()
        
        print(f"RLS策略数量: {len(policies)}")
        for policy_name, qual in policies:
            print(f"  策略: {policy_name}")
            print(f"  条件: {qual[:100]}...")  # 只显示前100个字符
        
        # 4. 清理并插入测试数据
        try:
            # 全局模式清理数据
            session.exec(text("RESET app.current_tenant_id"))
            session.exec(text("DELETE FROM member_fixed_slot_locks"))
            session.commit()
            
            # 插入测试数据
            session.exec(text("""
                INSERT INTO member_fixed_slot_locks 
                (tenant_id, member_id, teacher_fixed_slot_id, teacher_id, weekday, start_time, end_time, status, locked_at, created_by)
                VALUES 
                (1, 1, 1, 1, 1, '09:00:00', '09:25:00', 'active', NOW(), 1),
                (1, 2, 2, 1, 2, '10:00:00', '10:25:00', 'active', NOW(), 1),
                (2, 3, 3, 2, 3, '11:00:00', '11:25:00', 'active', NOW(), 2)
            """))
            session.commit()
            print("✅ 测试数据插入成功")
        except Exception as e:
            print(f"❌ 测试数据插入失败: {e}")
            return False
        
        # 5. 测试RLS隔离效果
        print("\n测试RLS隔离效果:")
        
        # 全局模式
        session.exec(text("RESET app.current_tenant_id"))
        global_count = session.exec(text("SELECT COUNT(*) FROM member_fixed_slot_locks")).first()
        global_records = session.exec(text("SELECT tenant_id FROM member_fixed_slot_locks ORDER BY tenant_id")).all()
        print(f"全局模式: {global_count} 条记录, 租户分布: {list(global_records)}")
        
        # 租户1模式
        session.exec(text("SET app.current_tenant_id = '1'"))
        tenant1_count = session.exec(text("SELECT COUNT(*) FROM member_fixed_slot_locks")).first()
        tenant1_records = session.exec(text("SELECT tenant_id FROM member_fixed_slot_locks")).all()
        print(f"租户1模式: {tenant1_count} 条记录, 租户分布: {list(tenant1_records)}")
        
        # 租户2模式
        session.exec(text("SET app.current_tenant_id = '2'"))
        tenant2_count = session.exec(text("SELECT COUNT(*) FROM member_fixed_slot_locks")).first()
        tenant2_records = session.exec(text("SELECT tenant_id FROM member_fixed_slot_locks")).all()
        print(f"租户2模式: {tenant2_count} 条记录, 租户分布: {list(tenant2_records)}")
        
        # 租户3模式（应该看不到任何数据）
        session.exec(text("SET app.current_tenant_id = '3'"))
        tenant3_count = session.exec(text("SELECT COUNT(*) FROM member_fixed_slot_locks")).first()
        print(f"租户3模式: {tenant3_count} 条记录")
        
        # 6. 验证隔离效果
        expected_global = 3
        expected_tenant1 = 2
        expected_tenant2 = 1
        expected_tenant3 = 0
        
        success = (
            global_count == expected_global and
            tenant1_count == expected_tenant1 and
            tenant2_count == expected_tenant2 and
            tenant3_count == expected_tenant3
        )
        
        if success:
            print("✅ RLS隔离完全正常")
        else:
            print("❌ RLS隔离有问题")
            print(f"期望: 全局={expected_global}, 租户1={expected_tenant1}, 租户2={expected_tenant2}, 租户3={expected_tenant3}")
            print(f"实际: 全局={global_count}, 租户1={tenant1_count}, 租户2={tenant2_count}, 租户3={tenant3_count}")
        
        return success


def test_rls_policies_in_prod_db():
    """在生产数据库中测试RLS策略"""
    print("\n=== 生产数据库中的RLS策略 ===")
    
    PROD_DATABASE_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_db"
    prod_engine = create_engine(PROD_DATABASE_URL, echo=False)
    
    with Session(prod_engine) as session:
        # 检查RLS策略
        policies = session.exec(text("""
            SELECT policyname, qual 
            FROM pg_policies 
            WHERE tablename = 'member_fixed_slot_locks'
        """)).all()
        
        print(f"生产数据库RLS策略数量: {len(policies)}")
        for policy_name, qual in policies:
            print(f"  策略: {policy_name}")
        
        # 检查数据
        session.exec(text("RESET app.current_tenant_id"))
        prod_count = session.exec(text("SELECT COUNT(*) FROM member_fixed_slot_locks")).first()
        print(f"生产数据库记录数: {prod_count}")
        
        return len(policies) > 0


def compare_databases():
    """比较测试数据库和生产数据库"""
    print("\n=== 比较测试数据库和生产数据库 ===")
    
    TEST_DATABASE_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
    PROD_DATABASE_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_db"
    
    test_engine = create_engine(TEST_DATABASE_URL, echo=False)
    prod_engine = create_engine(PROD_DATABASE_URL, echo=False)
    
    # 比较表结构
    with Session(test_engine) as test_session, Session(prod_engine) as prod_session:
        test_tables = set(test_session.exec(text("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)).all())
        
        prod_tables = set(prod_session.exec(text("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)).all())
        
        print(f"测试数据库表数量: {len(test_tables)}")
        print(f"生产数据库表数量: {len(prod_tables)}")
        
        missing_in_test = prod_tables - test_tables
        missing_in_prod = test_tables - prod_tables
        
        if missing_in_test:
            print(f"测试数据库缺少的表: {missing_in_test}")
        if missing_in_prod:
            print(f"生产数据库缺少的表: {missing_in_prod}")
        
        if not missing_in_test and not missing_in_prod:
            print("✅ 两个数据库的表结构一致")
        else:
            print("❌ 两个数据库的表结构不一致")


def main():
    print("🔍 开始专门测试RLS策略...\n")
    
    # 1. 测试测试数据库中的RLS
    test_rls_ok = test_rls_policies_in_test_db()
    
    # 2. 测试生产数据库中的RLS
    prod_rls_ok = test_rls_policies_in_prod_db()
    
    # 3. 比较两个数据库
    compare_databases()
    
    print(f"\n📊 测试结果总结:")
    print(f"  测试数据库RLS: {'✅' if test_rls_ok else '❌'}")
    print(f"  生产数据库RLS: {'✅' if prod_rls_ok else '❌'}")
    
    if test_rls_ok:
        print("\n🎉 测试数据库RLS策略工作正常")
        print("💡 API测试失败的原因可能是:")
        print("   1. 依赖覆盖没有生效")
        print("   2. 认证token问题")
        print("   3. 业务逻辑问题")
    else:
        print("\n⚠️  测试数据库RLS策略有问题，需要修复")
    
    print("\n🏁 RLS测试完成")


if __name__ == "__main__":
    main()
