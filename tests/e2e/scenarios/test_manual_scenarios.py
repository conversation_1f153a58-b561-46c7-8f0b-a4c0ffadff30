import pytest
import requests
import time
from fastapi.testclient import TestClient


@pytest.mark.manual
class TestManualScenarios:
    """手动测试场景 - 端到端测试"""
    
    def test_complete_tenant_workflow(self, client: TestClient):
        """完整的租户工作流测试"""
        print("\n🔍 完整租户工作流测试")
        
        # 1. 创建租户
        tenant_data = {
            "name": "完整测试教育机构",
            "code": f"complete_test_{int(time.time())}",
            "contact_name": "张三",
            "contact_phone": "13800138000",
            "contact_email": "<EMAIL>",
            "address": "北京市朝阳区测试街道123号",
            "description": "完整流程测试机构"
        }
        
        response = client.post("/api/v1/tenants/", json=tenant_data)
        assert response.status_code == 201
        tenant = response.json()
        print(f"✅ 租户创建成功: {tenant['name']} (ID: {tenant['id']})")
        
        # 2. 获取租户详情
        response = client.get(f"/api/v1/tenants/{tenant['id']}")
        assert response.status_code == 200
        print(f"✅ 租户详情获取成功")
        
        # 3. 更新租户信息
        update_data = {"description": "更新后的描述信息"}
        response = client.post(f"/api/v1/tenants/{tenant['id']}", json=update_data)
        assert response.status_code == 200
        print(f"✅ 租户信息更新成功")
        
        # 4. 激活租户
        response = client.post(f"/api/v1/tenants/{tenant['id']}/activate")
        assert response.status_code == 200
        print(f"✅ 租户激活成功")
        
        # 5. 应用套餐模板（如果存在）
        response = client.get("/api/v1/tenants/plans/templates")
        if response.status_code == 200 and response.json():
            templates = response.json()
            if templates:
                plan_code = templates[0]["plan_code"]
                response = client.post(f"/api/v1/tenants/{tenant['id']}/apply-plan/{plan_code}")
                if response.status_code == 200:
                    print(f"✅ 套餐模板应用成功: {plan_code}")
        
        print(f"🎉 完整租户工作流测试完成")
    
    def test_complete_user_workflow(self, client: TestClient, created_tenant):
        """完整的用户管理工作流测试"""
        print("\n🔍 完整用户管理工作流测试")
        
        client.headers.update({"X-Tenant-ID": str(created_tenant["id"])})
        
        # 1. 创建用户
        user_data = {
            "username": f"workflow_user_{int(time.time())}",
            "password": "password123",
            "full_name": "工作流测试用户",
            "role": "TEACHER",
            "email": "<EMAIL>"
        }
        
        response = client.post("/api/v1/users/", json=user_data)
        assert response.status_code == 201
        user = response.json()
        print(f"✅ 用户创建成功: {user['username']} (ID: {user['id']})")
        
        # 2. 获取用户列表
        response = client.get("/api/v1/users/")
        assert response.status_code == 200
        users = response.json()
        print(f"✅ 用户列表获取成功，共 {len(users)} 个用户")
        
        # 3. 获取用户详情
        response = client.get(f"/api/v1/users/{user['id']}")
        assert response.status_code == 200
        print(f"✅ 用户详情获取成功")
        
        # 4. 更新用户信息
        update_data = {"full_name": "更新后的用户姓名"}
        response = client.put(f"/api/v1/users/{user['id']}", json=update_data)
        assert response.status_code == 200
        print(f"✅ 用户信息更新成功")
        
        # 5. 停用用户
        response = client.post(f"/api/v1/users/{user['id']}/deactivate")
        if response.status_code == 200:
            print(f"✅ 用户停用成功")
            
            # 6. 重新激活用户
            response = client.post(f"/api/v1/users/{user['id']}/activate")
            if response.status_code == 200:
                print(f"✅ 用户激活成功")
        
        print(f"🎉 完整用户管理工作流测试完成")
    
    def test_complete_member_workflow(self, client: TestClient, created_tenant):
        """完整的会员管理工作流测试"""
        print("\n🔍 完整会员管理工作流测试")
        
        client.headers.update({"X-Tenant-ID": str(created_tenant["id"])})
        
        # 1. 创建会员
        member_data = {
            "name": "工作流测试会员",
            "phone": f"138{int(time.time()) % 100000000:08d}",
            "email": "<EMAIL>",
            "member_type": "trial",
            "gender": "female"
        }
        
        response = client.post("/api/v1/members/", json=member_data)
        assert response.status_code == 201
        member = response.json()
        print(f"✅ 会员创建成功: {member['name']} (ID: {member['id']})")
        
        # 2. 获取会员列表
        response = client.get("/api/v1/members/")
        assert response.status_code == 200
        members = response.json()
        print(f"✅ 会员列表获取成功，共 {len(members)} 个会员")
        
        # 3. 获取会员详情
        response = client.get(f"/api/v1/members/{member['id']}")
        assert response.status_code == 200
        print(f"✅ 会员详情获取成功")
        
        # 4. 更新会员信息
        update_data = {"notes": "工作流测试更新备注"}
        response = client.put(f"/api/v1/members/{member['id']}", json=update_data)
        assert response.status_code == 200
        print(f"✅ 会员信息更新成功")
        
        # 5. 更新会员统计
        response = client.post(
            f"/api/v1/members/{member['id']}/update-stats",
            params={"class_completed": True, "amount_spent": 100.0}
        )
        if response.status_code == 200:
            print(f"✅ 会员统计更新成功")
        
        print(f"🎉 完整会员管理工作流测试完成")
    
    def test_authentication_workflow(self, client: TestClient, created_tenant):
        """认证工作流测试"""
        print("\n🔍 认证工作流测试")
        
        client.headers.update({"X-Tenant-ID": str(created_tenant["id"])})
        
        # 1. 创建用户用于登录测试
        user_data = {
            "username": f"auth_user_{int(time.time())}",
            "password": "password123",
            "full_name": "认证测试用户",
            "role": "TEACHER"
        }
        
        response = client.post("/api/v1/users/", json=user_data)
        assert response.status_code == 201
        user = response.json()
        print(f"✅ 测试用户创建成功: {user['username']}")
        
        # 2. 用户登录测试
        form_data = {
            "username": f"{created_tenant['code']}:{user['username']}",
            "password": "password123"
        }
        
        response = client.post("/api/v1/auth/user/login", data=form_data)
        assert response.status_code == 200
        login_data = response.json()
        print(f"✅ 用户登录成功，获得token")
        
        # 3. 使用token访问受保护的端点
        headers = {"Authorization": f"Bearer {login_data['access_token']}"}
        response = client.get("/api/v1/users/", headers=headers)
        assert response.status_code == 200
        print(f"✅ Token验证成功，可以访问受保护端点")
        
        # 4. 创建会员用于登录测试
        member_data = {
            "name": "认证测试会员",
            "phone": f"139{int(time.time()) % 100000000:08d}",
            "member_type": "trial"
        }
        
        response = client.post("/api/v1/members/", json=member_data)
        assert response.status_code == 201
        member = response.json()
        print(f"✅ 测试会员创建成功: {member['name']}")
        
        # 5. 会员登录测试
        login_data = {
            "tenant_code": created_tenant["code"],
            "phone": member["phone"],
            "verification_code": "1234"
        }
        
        response = client.post("/api/v1/auth/member/login", json=login_data)
        assert response.status_code == 200
        member_login_data = response.json()
        print(f"✅ 会员登录成功，获得token")
        
        print(f"🎉 认证工作流测试完成")
    
    @pytest.mark.slow
    def test_performance_scenario(self, client: TestClient, created_tenant):
        """性能测试场景"""
        print("\n🔍 性能测试场景")
        
        client.headers.update({"X-Tenant-ID": str(created_tenant["id"])})
        
        # 批量创建会员测试
        start_time = time.time()
        created_members = []
        
        for i in range(10):  # 创建10个会员
            member_data = {
                "name": f"性能测试会员{i}",
                "phone": f"150{i:08d}",
                "member_type": "trial"
            }
            
            response = client.post("/api/v1/members/", json=member_data)
            if response.status_code == 201:
                created_members.append(response.json())
        
        creation_time = time.time() - start_time
        print(f"✅ 批量创建 {len(created_members)} 个会员，耗时 {creation_time:.2f} 秒")
        
        # 批量查询测试
        start_time = time.time()
        response = client.get("/api/v1/members/")
        query_time = time.time() - start_time
        
        assert response.status_code == 200
        members = response.json()
        print(f"✅ 查询会员列表（{len(members)} 条记录），耗时 {query_time:.2f} 秒")
        
        # 性能断言
        assert creation_time < 5.0, f"批量创建耗时过长: {creation_time:.2f}s"
        assert query_time < 1.0, f"查询耗时过长: {query_time:.2f}s"
        
        print(f"🎉 性能测试场景完成")
    
    def test_error_handling_scenarios(self, client: TestClient):
        """错误处理场景测试"""
        print("\n🔍 错误处理场景测试")
        
        # 1. 访问不存在的资源
        response = client.get("/api/v1/tenants/99999")
        assert response.status_code == 404
        print("✅ 404错误处理正常")
        
        # 2. 发送无效数据
        invalid_data = {"name": "", "code": ""}
        response = client.post("/api/v1/tenants/", json=invalid_data)
        assert response.status_code == 422
        print("✅ 数据验证错误处理正常")
        
        # 3. 重复创建资源
        tenant_data = {
            "name": "错误测试机构",
            "code": "error_test_org",
            "contact_name": "张三",
            "contact_phone": "13800138000",
            "contact_email": "<EMAIL>",
            "address": "测试地址"
        }
        
        # 第一次创建
        response = client.post("/api/v1/tenants/", json=tenant_data)
        if response.status_code == 201:
            # 第二次创建相同的
            response = client.post("/api/v1/tenants/", json=tenant_data)
            assert response.status_code == 400
            print("✅ 重复资源错误处理正常")
        
        # 4. 无效认证
        response = client.get("/api/v1/users/")
        assert response.status_code == 401
        print("✅ 认证错误处理正常")
        
        print(f"🎉 错误处理场景测试完成") 