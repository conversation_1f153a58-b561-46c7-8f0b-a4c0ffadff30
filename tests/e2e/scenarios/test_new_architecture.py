"""
测试新的共享Schema + tenant_id + RLS架构
"""

import pytest
from sqlmodel import Session, select, text
from app.features.users.models import User, UserRole
from app.features.users.schemas import UserCreate
from app.features.users.service import UserService, get_user_service
from app.features.tenants.models import Tenant
from app.features.members.service import get_member_service
from app.features.members.models import Member, MemberType
from app.features.members.schemas import MemberCreate
from app.utils.security import get_password_hash
import random


class TestNewArchitecture:
    """测试新架构的多租户功能"""

    def test_tenant_creation(self, test_session: Session):
        """测试租户创建"""
        # 生成随机的租户代码避免冲突
        tenant_code = f"test_org_{random.randint(1000, 9999)}"
        
        tenant = Tenant(
            name="测试机构",
            code=tenant_code,
            description="用于测试的机构",
            contact_email="<EMAIL>",
            contact_phone="13800138000",
            plan_code="basic"
        )
        test_session.add(tenant)
        test_session.commit()
        test_session.refresh(tenant)
        
        assert tenant.id is not None
        assert tenant.name == "测试机构"
        assert tenant.code == tenant_code

    def test_user_creation_with_tenant(self, test_session: Session):
        """测试在租户中创建用户"""
        # 创建租户
        tenant = Tenant(
            name="用户测试机构",
            code=f"user_test_{random.randint(1000, 9999)}",
            description="用于用户测试的机构",
            contact_email="<EMAIL>",
            contact_phone="13800138001",
            plan_code="basic"
        )
        test_session.add(tenant)
        test_session.commit()
        test_session.refresh(tenant)
        
        # 创建用户服务
        user_service = UserService(test_session, tenant.id)
        
        # 创建用户
        admin_data = UserCreate(
            username=f"admin_{random.randint(100, 999)}",
            email=f"admin_{random.randint(100, 999)}@test.com",
            password="123456",
            real_name="租户管理员",
            role=UserRole.ADMIN
        )
        admin_user = user_service.create_user(admin_data)
        
        assert admin_user.id is not None
        assert admin_user.tenant_id == tenant.id
        assert admin_user.role == UserRole.ADMIN

    def test_member_creation_with_tenant(self, test_session: Session):
        """测试在租户中创建会员"""
        # 创建租户
        tenant = Tenant(
            name="会员测试机构",
            code=f"member_test_{random.randint(1000, 9999)}",
            description="用于会员测试的机构",
            contact_email="<EMAIL>",
            contact_phone="13800138002",
            plan_code="basic"
        )
        test_session.add(tenant)
        test_session.commit()
        test_session.refresh(tenant)
        
        # 创建管理员用户
        user_service = UserService(test_session, tenant.id)
        admin_data = UserCreate(
            username=f"admin_{random.randint(100, 999)}",
            email=f"admin_{random.randint(100, 999)}@test.com",
            password="123456",
            real_name="租户管理员",
            role=UserRole.ADMIN
        )
        admin_user = user_service.create_user(admin_data)
        
        # 创建会员服务
        member_service = get_member_service(test_session, tenant.id)
        
        # 创建会员
        member_data = MemberCreate(
            name="张三",
            phone=f"138{random.randint(10000000, 99999999)}",
            email=f"zhangsan_{random.randint(100, 999)}@test.com",
            member_type=MemberType.TRIAL,
            sales_id=admin_user.id
        )
        member = member_service.create_member(member_data, created_by=admin_user.id)
        
        assert member.id is not None
        assert member.tenant_id == tenant.id
        assert member.member_type == MemberType.TRIAL

    def test_rls_tenant_isolation(self, test_session: Session):
        """测试RLS租户隔离"""
        # 创建两个租户
        tenant1 = Tenant(
            name="租户1",
            code=f"tenant1_{random.randint(1000, 9999)}",
            description="租户1",
            contact_email="<EMAIL>",
            contact_phone="13800138001",
            plan_code="basic"
        )
        tenant2 = Tenant(
            name="租户2",
            code=f"tenant2_{random.randint(1000, 9999)}",
            description="租户2",
            contact_email="<EMAIL>",
            contact_phone="13800138002",
            plan_code="basic"
        )
        test_session.add_all([tenant1, tenant2])
        test_session.commit()
        test_session.refresh(tenant1)
        test_session.refresh(tenant2)
        print(f"tenant1: {tenant1.id} , {tenant1.name} , {tenant1.code}")
        print(f"tenant2: {tenant2.id} , {tenant2.name} , {tenant2.code}")
        
        # 在租户1中创建用户
        user_service1 = UserService(test_session, tenant1.id)
        admin_data1 = UserCreate(
            username=f"admin1_{random.randint(100, 999)}",
            email=f"admin1_{random.randint(100, 999)}@test.com",
            password="123456",
            real_name="租户1管理员",
            role=UserRole.ADMIN
        )
        admin_user1 = user_service1.create_user(admin_data1)
        print(f"admin_user1: {admin_user1.id} , {admin_user1.username} , {admin_user1.tenant_id}")
        
        # 在租户2中创建用户
        user_service2 = UserService(test_session, tenant2.id)
        admin_data2 = UserCreate(
            username=f"admin2_{random.randint(100, 999)}",
            email=f"admin2_{random.randint(100, 999)}@test.com",
            password="123456",
            real_name="租户2管理员",
            role=UserRole.ADMIN
        )
        admin_user2 = user_service2.create_user(admin_data2)
        print(f"admin_user2: {admin_user2.id} , {admin_user2.username} , {admin_user2.tenant_id}")
        
        # 测试租户1的视图（应该只看到租户1的用户）
        test_session.execute(text(f"SET app.current_tenant_id = '{tenant1.id}'"))
        tenant1_users = test_session.exec(
            select(User)
            # select(User).where(User.tenant_id.isnot(None))
        ).all()
        tenant1_user_ids = {u.id for u in tenant1_users}
        print(f"tenant1_user_ids: {tenant1_user_ids}")
        
        # 测试租户2的视图（应该只看到租户2的用户）
        test_session.execute(text(f"SET app.current_tenant_id = '{tenant2.id}'"))
        tenant2_users = test_session.exec(
            select(User)
            # select(User).where(User.tenant_id.isnot(None))
        ).all()
        tenant2_user_ids = {u.id for u in tenant2_users}
        print(f"tenant2_user_ids: {tenant2_user_ids}")
        
        # 验证隔离性：两个租户的用户集合应该不相交
        assert tenant1_user_ids.isdisjoint(tenant2_user_ids), "租户间数据隔离失败"
        assert admin_user1.id in tenant1_user_ids, "租户1应该能看到自己的用户"
        assert admin_user2.id in tenant2_user_ids, "租户2应该能看到自己的用户"
        assert admin_user1.id not in tenant2_user_ids, "租户2不应该看到租户1的用户"
        assert admin_user2.id not in tenant1_user_ids, "租户1不应该看到租户2的用户"

    def test_super_admin_global_access(self, test_session: Session):
        """测试超级管理员全局访问权限"""
        # 创建租户
        tenant = Tenant(
            name="超管测试机构",
            code=f"super_test_{random.randint(1000, 9999)}",
            description="用于超管测试的机构",
            contact_email="<EMAIL>",
            contact_phone="13800138003",
            plan_code="basic"
        )
        test_session.add(tenant)
        test_session.commit()
        test_session.refresh(tenant)
        print(f"测试超级管理员全局访问权限 tenant: {tenant.id} , {tenant.name} , {tenant.code}")
        
        # 在租户中创建用户
        user_service = get_user_service(test_session, tenant.id)
        admin_data = UserCreate(
            username=f"tenant_admin_{random.randint(100, 999)}",
            email=f"tenant_admin_{random.randint(100, 999)}@test.com",
            password="123456",
            real_name="租户管理员",
            role=UserRole.ADMIN
        )
        tenant_admin = user_service.create_user(admin_data)
        print(f"测试超级管理员全局访问权限 tenant_admin: {tenant_admin.id} , {tenant_admin.username} , {tenant_admin.tenant_id}")
        
        # 清除租户上下文（模拟超级管理员）
        test_session.execute(text("RESET app.current_tenant_id"))
        print(f"测试超级管理员全局访问权限 清除租户上下文")
        
        # 超级管理员应该能看到所有用户（包括租户用户和超级管理员自己）
        all_users = test_session.exec(select(User)).all()
        
        user_ids = {u.id for u in all_users}
        print(f"测试超级管理员全局访问权限 user_ids: {user_ids}")
        # 应该包含租户用户
        assert tenant_admin.id in user_ids, "超级管理员应该能看到租户用户"
        
        # 应该包含超级管理员（如果存在）
        super_admins = [u for u in all_users if u.role == UserRole.SUPER_ADMIN]
        assert len(super_admins) >= 0, "应该能看到超级管理员用户"

    def test_member_tenant_isolation(self, test_session: Session):
        """测试会员租户隔离"""
        # 创建两个租户
        tenant1 = Tenant(
            name="会员租户1",
            code=f"member_tenant1_{random.randint(1000, 9999)}",
            description="会员租户1",
            contact_email="<EMAIL>",
            contact_phone="13800138004",
            plan_code="basic"
        )
        tenant2 = Tenant(
            name="会员租户2",
            code=f"member_tenant2_{random.randint(1000, 9999)}",
            description="会员租户2",
            contact_email="<EMAIL>",
            contact_phone="13800138005",
            plan_code="basic"
        )
        test_session.add_all([tenant1, tenant2])
        test_session.commit()
        test_session.refresh(tenant1)
        test_session.refresh(tenant2)
        
        # 在两个租户中分别创建会员
        member_service1 = get_member_service(test_session, tenant1.id)
        member_data1 = MemberCreate(
            name="租户1会员",
            phone=f"138{random.randint(10000000, 99999999)}",
            email=f"member1_{random.randint(100, 999)}@test.com",
            member_type=MemberType.TRIAL
        )
        member1 = member_service1.create_member(member_data1, created_by=None)
        
        member_service2 = get_member_service(test_session, tenant2.id)
        member_data2 = MemberCreate(
            name="租户2会员",
            phone=f"138{random.randint(10000000, 99999999)}",
            email=f"member2_{random.randint(100, 999)}@test.com",
            member_type=MemberType.TRIAL
        )
        member2 = member_service2.create_member(member_data2, created_by=None)
        
        # 测试租户1的会员视图
        test_session.execute(text(f"SET app.current_tenant_id = '{tenant1.id}'"))
        tenant1_members = test_session.exec(select(Member)).all()
        tenant1_member_ids = {m.id for m in tenant1_members}
        
        # 测试租户2的会员视图
        test_session.execute(text(f"SET app.current_tenant_id = '{tenant2.id}'"))
        tenant2_members = test_session.exec(select(Member)).all()
        tenant2_member_ids = {m.id for m in tenant2_members}
        
        # 验证隔离性
        assert tenant1_member_ids.isdisjoint(tenant2_member_ids), "会员租户间数据隔离失败"
        assert member1.id in tenant1_member_ids, "租户1应该能看到自己的会员"
        assert member2.id in tenant2_member_ids, "租户2应该能看到自己的会员"
        assert member1.id not in tenant2_member_ids, "租户2不应该看到租户1的会员"
        assert member2.id not in tenant1_member_ids, "租户1不应该看到租户2的会员"

    def test_user_service_operations(self, test_session: Session):
        """测试用户服务的各种操作"""
        # 创建租户
        tenant = Tenant(
            name="用户服务测试机构",
            code=f"user_service_test_{random.randint(1000, 9999)}",
            description="用于用户服务测试的机构",
            contact_email="<EMAIL>",
            contact_phone="13800138006",
            plan_code="basic"
        )
        test_session.add(tenant)
        test_session.commit()
        test_session.refresh(tenant)
        
        user_service = get_user_service(test_session, tenant.id)
        
        # 创建管理员
        admin_data = UserCreate(
            username=f"service_admin_{random.randint(100, 999)}",
            email=f"service_admin_{random.randint(100, 999)}@test.com",
            password="123456",
            real_name="服务测试管理员",
            role=UserRole.ADMIN
        )
        admin_user = user_service.create_user(admin_data)
        
        # 创建代理人
        agent_data = UserCreate(
            username=f"service_agent_{random.randint(100, 999)}",
            email=f"service_agent_{random.randint(100, 999)}@test.com",
            password="123456",
            real_name="服务测试代理",
            role=UserRole.AGENT
        )
        agent_user = user_service.create_user(agent_data)
        
        # 测试获取用户列表
        users = user_service.get_users()
        user_ids = {u.id for u in users}
        
        assert admin_user.id in user_ids
        assert agent_user.id in user_ids
        assert len(users) >= 2
        
        # 测试按角色过滤
        admins = user_service.get_users(role=UserRole.ADMIN)
        admin_ids = {u.id for u in admins}
        assert admin_user.id in admin_ids
        assert agent_user.id not in admin_ids
        
        agents = user_service.get_users(role=UserRole.AGENT)
        agent_ids = {u.id for u in agents}
        assert agent_user.id in agent_ids
        assert admin_user.id not in agent_ids

    def test_member_service_operations(self, test_session: Session):
        """测试会员服务的各种操作"""
        # 创建租户
        tenant = Tenant(
            name="会员服务测试机构",
            code=f"member_service_test_{random.randint(1000, 9999)}",
            description="用于会员服务测试的机构",
            contact_email="<EMAIL>",
            contact_phone="13800138007",
            plan_code="basic"
        )
        test_session.add(tenant)
        test_session.commit()
        test_session.refresh(tenant)
        
        member_service = get_member_service(test_session, tenant.id)
        
        # 创建试用会员
        trial_member_data = MemberCreate(
            name="试用会员",
            phone=f"138{random.randint(10000000, 99999999)}",
            email=f"trial_member_{random.randint(100, 999)}@test.com",
            member_type=MemberType.TRIAL
        )
        trial_member = member_service.create_member(trial_member_data, created_by=None)
        
        # 创建正式会员
        formal_member_data = MemberCreate(
            name="正式会员",
            phone=f"138{random.randint(10000000, 99999999)}",
            email=f"formal_member_{random.randint(100, 999)}@test.com",
            member_type=MemberType.FORMAL
        )
        formal_member = member_service.create_member(formal_member_data, created_by=None)
        
        # 测试获取会员列表
        members = member_service.get_members()
        member_ids = {m.id for m in members}
        
        assert trial_member.id in member_ids
        assert formal_member.id in member_ids
        assert len(members) >= 2
        
        # 测试按类型过滤
        trial_members = member_service.get_members(member_type=MemberType.TRIAL)
        trial_ids = {m.id for m in trial_members}
        assert trial_member.id in trial_ids
        assert formal_member.id not in trial_ids
        
        formal_members = member_service.get_members(member_type=MemberType.FORMAL)
        formal_ids = {m.id for m in formal_members}
        assert formal_member.id in formal_ids
        assert trial_member.id not in formal_ids 