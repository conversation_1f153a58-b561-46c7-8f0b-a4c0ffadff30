"""
测试新版本的RLS策略

验证修复后的RLS策略能正确工作：
1. 全局模式下可以看到所有数据
2. 租户模式下只能看到本租户数据
3. 超级管理员在任何租户上下文中都可见（仅在全局模式）
4. 普通租户用户不能看到超级管理员
"""

import pytest
from sqlmodel import Session, select, text
from app.features.users.models import User, UserRole, UserStatus
from app.features.tenants.models import Tenant
from app.utils.security import get_password_hash
from app.db.rls_v2 import setup_rls_policies_v2
import uuid


class TestRLSPoliciesV2:
    """测试新版本的RLS策略"""

    def test_rls_v2_with_null_super_admin(self, test_session: Session):
        """测试使用NULL标识超级管理员的RLS策略v2（修复版）"""
        # 设置新的RLS策略（使用NULL标识超级管理员）
        setup_rls_policies_v2(super_admin_mode="null")
        
        # 创建租户
        tenant = Tenant(
            name="测试租户",
            code=f"tenant_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000001",
            address="地址"
        )
        test_session.add(tenant)
        test_session.commit()
        test_session.refresh(tenant)
        
        # 在全局上下文中创建超级管理员
        test_session.execute(text("RESET app.current_tenant_id"))
        super_admin = User(
            username=f"superadmin_{uuid.uuid4().hex[:8]}",
            email=f"superadmin_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="超级管理员",
            role=UserRole.SUPER_ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=None  # 超级管理员使用NULL
        )
        test_session.add(super_admin)
        test_session.commit()
        test_session.refresh(super_admin)
        
        # 在租户上下文中创建租户用户
        test_session.execute(text(f"SET app.current_tenant_id = '{tenant.id}'"))
        tenant_user = User(
            username=f"user_{uuid.uuid4().hex[:8]}",
            email=f"user_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="租户用户",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=tenant.id
        )
        test_session.add(tenant_user)
        test_session.commit()
        test_session.refresh(tenant_user)
        
        # 测试全局上下文（应该能看到所有用户）
        test_session.execute(text("RESET app.current_tenant_id"))
        global_users = test_session.exec(select(User)).all()
        global_user_ids = {u.id for u in global_users}
        
        # 测试租户上下文（应该只能看到租户用户，不能看到超级管理员）
        test_session.execute(text(f"SET app.current_tenant_id = '{tenant.id}'"))
        tenant_users = test_session.exec(select(User)).all()
        tenant_user_ids = {u.id for u in tenant_users}
        
        # 断言
        assert super_admin.id in global_user_ids, "全局上下文应该能看到超级管理员"
        assert tenant_user.id in global_user_ids, "全局上下文应该能看到租户用户"
        assert tenant_user.id in tenant_user_ids, "租户上下文应该能看到租户用户"
        assert super_admin.id not in tenant_user_ids, "租户上下文不应该看到超级管理员"
        
        print(f"✅ RLS v2 (NULL模式) 测试通过: 全局用户数={len(global_users)}, 租户用户数={len(tenant_users)}")
        print(f"✅ 租户用户无法看到超级管理员")

    def test_rls_v2_with_zero_super_admin(self, test_session: Session):
        """测试使用tenant_id=0标识超级管理员的RLS策略v2"""
        # 设置新的RLS策略（使用tenant_id=0标识超级管理员）
        setup_rls_policies_v2(super_admin_mode="zero")
        
        # 创建租户
        tenant = Tenant(
            name="测试租户",
            code=f"tenant_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000001",
            address="地址"
        )
        test_session.add(tenant)
        test_session.commit()
        test_session.refresh(tenant)
        
        # 在全局上下文中创建超级管理员
        test_session.execute(text("RESET app.current_tenant_id"))
        super_admin = User(
            username=f"superadmin_{uuid.uuid4().hex[:8]}",
            email=f"superadmin_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="超级管理员",
            role=UserRole.SUPER_ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=0  # 超级管理员使用0
        )
        test_session.add(super_admin)
        test_session.commit()
        test_session.refresh(super_admin)
        
        # 在租户上下文中创建租户用户
        test_session.execute(text(f"SET app.current_tenant_id = '{tenant.id}'"))
        tenant_user = User(
            username=f"user_{uuid.uuid4().hex[:8]}",
            email=f"user_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="租户用户",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=tenant.id
        )
        test_session.add(tenant_user)
        test_session.commit()
        test_session.refresh(tenant_user)
        
        # 测试全局上下文（应该能看到所有用户）
        test_session.execute(text("RESET app.current_tenant_id"))
        global_users = test_session.exec(select(User)).all()
        global_user_ids = {u.id for u in global_users}
        
        # 测试租户上下文（应该只能看到租户用户，不能看到超级管理员）
        test_session.execute(text(f"SET app.current_tenant_id = '{tenant.id}'"))
        tenant_users = test_session.exec(select(User)).all()
        tenant_user_ids = {u.id for u in tenant_users}
        
        # 断言
        assert super_admin.id in global_user_ids, "全局上下文应该能看到超级管理员"
        assert tenant_user.id in global_user_ids, "全局上下文应该能看到租户用户"
        assert tenant_user.id in tenant_user_ids, "租户上下文应该能看到租户用户"
        assert super_admin.id not in tenant_user_ids, "租户上下文不应该看到超级管理员"
        
        print(f"✅ RLS v2 (ZERO模式) 测试通过: 全局用户数={len(global_users)}, 租户用户数={len(tenant_users)}")
        print(f"✅ 租户用户无法看到超级管理员")

    def test_multi_tenant_isolation_v2(self, test_session: Session):
        """测试多租户隔离（使用RLS v2）"""
        # 设置新的RLS策略
        setup_rls_policies_v2(super_admin_mode="zero")
        
        # 创建两个租户
        tenant1 = Tenant(
            name="租户1",
            code=f"tenant1_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000001",
            address="地址1"
        )
        tenant2 = Tenant(
            name="租户2",
            code=f"tenant2_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000002",
            address="地址2"
        )
        test_session.add_all([tenant1, tenant2])
        test_session.commit()
        test_session.refresh(tenant1)
        test_session.refresh(tenant2)
        
        # 创建超级管理员
        test_session.execute(text("RESET app.current_tenant_id"))
        super_admin = User(
            username=f"superadmin_{uuid.uuid4().hex[:8]}",
            email=f"superadmin_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="超级管理员",
            role=UserRole.SUPER_ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=0
        )
        test_session.add(super_admin)
        test_session.commit()
        test_session.refresh(super_admin)
        
        # 为租户1创建用户
        test_session.execute(text(f"SET app.current_tenant_id = '{tenant1.id}'"))
        user1 = User(
            username=f"user1_{uuid.uuid4().hex[:8]}",
            email=f"user1_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="租户1用户",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=tenant1.id
        )
        test_session.add(user1)
        test_session.commit()
        test_session.refresh(user1)
        
        # 为租户2创建用户
        test_session.execute(text(f"SET app.current_tenant_id = '{tenant2.id}'"))
        user2 = User(
            username=f"user2_{uuid.uuid4().hex[:8]}",
            email=f"user2_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="租户2用户",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=tenant2.id
        )
        test_session.add(user2)
        test_session.commit()
        test_session.refresh(user2)
        
        # 验证全局上下文能看到所有用户
        test_session.execute(text("RESET app.current_tenant_id"))
        global_users = test_session.exec(select(User)).all()
        global_user_ids = {u.id for u in global_users}
        
        # 验证租户1只能看到自己的用户
        test_session.execute(text(f"SET app.current_tenant_id = '{tenant1.id}'"))
        tenant1_users = test_session.exec(select(User)).all()
        tenant1_user_ids = {u.id for u in tenant1_users}
        
        # 验证租户2只能看到自己的用户
        test_session.execute(text(f"SET app.current_tenant_id = '{tenant2.id}'"))
        tenant2_users = test_session.exec(select(User)).all()
        tenant2_user_ids = {u.id for u in tenant2_users}
        
        # 断言
        assert super_admin.id in global_user_ids, "全局上下文应该能看到超级管理员"
        assert user1.id in global_user_ids, "全局上下文应该能看到租户1用户"
        assert user2.id in global_user_ids, "全局上下文应该能看到租户2用户"
        
        assert user1.id in tenant1_user_ids, "租户1应该能看到自己的用户"
        assert user2.id in tenant2_user_ids, "租户2应该能看到自己的用户"
        
        assert super_admin.id not in tenant1_user_ids, "租户1不应该看到超级管理员"
        assert super_admin.id not in tenant2_user_ids, "租户2不应该看到超级管理员"
        assert user1.id not in tenant2_user_ids, "租户2不应该看到租户1的用户"
        assert user2.id not in tenant1_user_ids, "租户1不应该看到租户2的用户"
        
        assert tenant1_user_ids.isdisjoint(tenant2_user_ids), "两个租户的用户集合应该完全分离"
        
        print(f"✅ 多租户隔离测试通过: 全局用户数={len(global_users)}, 租户1用户数={len(tenant1_users)}, 租户2用户数={len(tenant2_users)}")
        print(f"✅ 所有租户都无法看到超级管理员")
        print(f"✅ 租户间数据完全隔离")
