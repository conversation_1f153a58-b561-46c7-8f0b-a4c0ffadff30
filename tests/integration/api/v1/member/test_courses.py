"""会员端课程API集成测试"""
import pytest
from fastapi.testclient import TestClient


class TestMemberCourseAPI:
    """会员端课程API测试"""
    
    def test_get_available_courses(self, client: TestClient, member_token):
        """测试获取可预约课程"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get(
            "/api/v1/member/courses/available?page=1&size=10",
            headers=headers
        )
        
        print(f"test_get_available_courses response: {response.json()}")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
    
    def test_get_my_courses(self, client: TestClient, member_token):
        """测试获取我的课程"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get(
            "/api/v1/member/courses/my?page=1&size=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
