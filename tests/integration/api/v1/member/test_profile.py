"""会员端个人信息API集成测试"""
import pytest
from fastapi.testclient import TestClient
from tests.utils.logging_helper import LoggedTestCase


class TestMemberProfileAPI(LoggedTestCase):
    """会员端个人信息API测试"""

    def test_get_my_profile(self, client: TestClient, member_token):
        """测试获取我的个人信息"""
        self.log_step("开始测试获取个人信息")

        # 准备请求头
        headers = {"Authorization": f"Bearer {member_token}"}
        self.log_data("request_headers", headers)

        # 发送请求
        self.log_step("发送GET请求获取个人信息")
        response = client.get("/api/v1/member/profile/me", headers=headers)

        # 记录API调用
        self.log_api("GET", "/api/v1/member/profile/me", None, response.json())

        # 验证响应
        self.assert_and_log(
            response.status_code == 200,
            "应该返回200状态码",
            expected=200,
            actual=response.status_code
        )

        data = response.json()
        self.assert_and_log(
            data["success"] is True,
            "响应应该标记为成功",
            expected=True,
            actual=data["success"]
        )

        self.assert_and_log(
            "data" in data,
            "响应应该包含data字段"
        )

        self.assert_and_log(
            "name" in data["data"],
            "用户数据应该包含name字段"
        )

        self.log_step("个人信息获取测试完成")

    def test_update_my_profile(self, client: TestClient, member_token):
        """测试更新我的个人信息"""
        self.log_step("开始测试更新个人信息")

        # 准备测试数据
        headers = {"Authorization": f"Bearer {member_token}"}
        update_data = {
            "name": "更新后的姓名",
            "address": "更新后的地址"
        }

        self.log_data("request_headers", headers)
        self.log_data("update_data", update_data)

        # 发送更新请求
        self.log_step("发送PUT请求更新个人信息")
        response = client.put(
            "/api/v1/member/profile/me",
            json=update_data,
            headers=headers
        )

        # 记录API调用
        self.log_api("PUT", "/api/v1/member/profile/me", update_data, response.json())

        # 验证响应状态
        self.assert_and_log(
            response.status_code == 200,
            "更新请求应该返回200状态码",
            expected=200,
            actual=response.status_code
        )

        data = response.json()
        self.assert_and_log(
            data["success"] is True,
            "更新响应应该标记为成功",
            expected=True,
            actual=data["success"]
        )

        # 验证更新结果
        self.assert_and_log(
            data["data"]["name"] == update_data["name"],
            "姓名应该已更新",
            expected=update_data["name"],
            actual=data["data"]["name"]
        )

        self.log_step("个人信息更新测试完成")


class TestMemberProfileAPISimple:
    """简单版本的测试类 - 不使用日志助手"""

    def test_get_my_profile_simple(self, client: TestClient, member_token):
        """简单测试获取个人信息 - 仅依赖输出重定向"""
        print("🧪 开始测试获取个人信息")

        headers = {"Authorization": f"Bearer {member_token}"}
        print(f"📤 请求头: {headers}")

        response = client.get("/api/v1/member/profile/me", headers=headers)
        print(f"📥 响应状态: {response.status_code}")
        print(f"📥 响应数据: {response.json()}")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "name" in data["data"]

        print("✅ 个人信息获取测试通过")
