import pytest
from fastapi.testclient import TestClient
from app.features.users.models import UserRole


class TestAdminUserAPI:
    """用户API集成测试"""
    
    def test_create_user_success(self, client: TestClient, sample_user_data, created_tenant, admin_token):
        """测试成功创建用户"""
        response = client.post(
            "/api/v1/admin/users/", 
            json=sample_user_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 201
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "用户创建成功"
        assert "data" in data
        
        user_data = data["data"]
        assert user_data["username"] == sample_user_data["username"]
        assert user_data["real_name"] == sample_user_data["real_name"]
        assert user_data["role"] == sample_user_data["role"]
        assert "password_hash" not in user_data  # 密码哈希不应该返回
    
    def test_create_super_admin_user(self, client: TestClient, super_admin_user_data, super_admin_token):
        """测试创建超级管理员用户"""
        response = client.post(
            "/api/v1/admin/users/", 
            json=super_admin_user_data,
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert data["data"]["username"] == super_admin_user_data["username"]
    
    def test_create_user_duplicate_username(self, client: TestClient, sample_user_data, created_tenant, admin_token):
        """测试创建重复用户名"""
        # 先创建一个用户
        client.post(
            "/api/v1/admin/users/", 
            json=sample_user_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        # 尝试创建相同用户名的用户
        response = client.post(
            "/api/v1/admin/users/", 
            json=sample_user_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "用户名" in data["message"] or "已存在" in data["message"]
    
    def test_create_user_invalid_data(self, client: TestClient, created_tenant, admin_token):
        """测试创建用户时数据验证"""
        invalid_data = {
            "username": "",  # 空用户名
            "password": "123",  # 密码太短
            "role": "invalid_role"  # 无效角色
        }
        
        response = client.post(
            "/api/v1/admin/users/", 
            json=invalid_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 422  # 验证错误
    
    def test_get_users_list(self, client: TestClient, created_tenant, admin_token):
        """测试获取用户列表"""
        # 先创建几个用户
        for i in range(3):
            user_data = {
                "username": f"testuser{i}",
                "password": "password123",
                "real_name": f"测试用户{i}",
                "role": UserRole.AGENT,
                "email": f"user{i}@test.com"
            }
            client.post(
                "/api/v1/admin/users/", 
                json=user_data,
                headers={"Authorization": f"Bearer {admin_token}"}
            )
        
        response = client.get(
            "/api/v1/admin/users/", 
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        print(f"test_get_users_list data: {data}")
        assert data["success"] is True
        assert data["message"] == "获取用户列表成功"
        assert "data" in data
        assert "total" in data
        assert isinstance(data["data"], list)
        assert isinstance(data["total"], int)
    
    def test_get_users_pagination(self, client: TestClient, created_tenant, admin_token):
        """测试用户列表分页"""
        # 创建5个用户
        for i in range(5):
            user_data = {
                "username": f"testuser{i}",
                "password": "password123",
                "real_name": f"测试用户{i}",
                "role": UserRole.AGENT
            }
            client.post(
                "/api/v1/admin/users/", 
                json=user_data,
                headers={"Authorization": f"Bearer {admin_token}"}
            )
        
        # 测试分页
        response = client.get(
            "/api/v1/admin/users/", 
            params={"skip": 0, "limit": 3, "role": UserRole.AGENT.value},
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["data"]) <= 3
        assert data["total"] >= 5
    
    def test_get_user_by_id(self, client: TestClient, created_tenant, admin_token):
        """测试根据ID获取用户"""
        # 先创建一个用户
        user_data = {
            "username": "testuser",
            "password": "password123",
            "real_name": "测试用户",
            "role": UserRole.AGENT,
            "email": "<EMAIL>"
        }
        create_response = client.post(
            "/api/v1/admin/users/", 
            json=user_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        created_user = create_response.json()
        user_id = created_user["data"]["id"]
        
        response = client.get(
            f"/api/v1/admin/users/{user_id}", 
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "获取用户详情成功"
        assert data["data"]["id"] == user_id
        assert data["data"]["username"] == user_data["username"]
        assert "password_hash" not in data["data"]  # 密码哈希不应该返回
    
    def test_get_nonexistent_user(self, client: TestClient, created_tenant, admin_token):
        """测试获取不存在的用户"""
        response = client.get(
            "/api/v1/admin/users/99999", 
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
    
    def test_update_user(self, client: TestClient, created_tenant, admin_token):
        """测试更新用户"""
        # 先创建一个用户
        user_data = {
            "username": "testuser",
            "password": "password123",
            "real_name": "测试用户",
            "role": UserRole.AGENT,
            "email": "<EMAIL>"
        }
        create_response = client.post(
            "/api/v1/admin/users/", 
            json=user_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        created_user = create_response.json()
        user_id = created_user["data"]["id"]
        
        update_data = {
            "real_name": "更新后的用户姓名",
            "email": "<EMAIL>",
            "status": "inactive"
        }
        
        response = client.post(
            f"/api/v1/admin/users/{user_id}", 
            json=update_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "用户信息更新成功"
        assert data["data"]["real_name"] == "更新后的用户姓名"
        assert data["data"]["email"] == "<EMAIL>"
        assert data["data"]["status"] == "inactive"
    
    def test_update_nonexistent_user(self, client: TestClient, created_tenant, admin_token):
        """测试更新不存在的用户"""
        update_data = {"real_name": "新名称"}
        
        response = client.post(
            "/api/v1/admin/users/99999", 
            json=update_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        print(f"test_update_nonexistent_user response: {response.json()}")
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
    
    def test_delete_user(self, client: TestClient, created_tenant, admin_token):
        """测试删除用户"""
        # 先创建一个用户
        user_data = {
            "username": "testuser",
            "password": "password123",
            "real_name": "测试用户",
            "role": UserRole.AGENT,
            "email": "<EMAIL>"
        }
        create_response = client.post(
            "/api/v1/admin/users/", 
            json=user_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        created_user = create_response.json()
        user_id = created_user["data"]["id"]
        
        response = client.post(
            f"/api/v1/admin/users/{user_id}/delete",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert "删除成功" in data["message"]
        
        # 验证用户已被删除
        get_response = client.get(
            f"/api/v1/admin/users/{user_id}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert get_response.status_code == 404
    
    def test_user_role_filtering(self, client: TestClient, created_tenant, admin_token):
        """测试用户角色筛选"""
        # 创建不同角色的用户
        users_data = [
            {"username": "admin1", "password": "password123", "real_name": "管理员1", "role": UserRole.ADMIN},
            {"username": "agent1", "password": "password123", "real_name": "代理1", "role": UserRole.AGENT},
            {"username": "agent2", "password": "password123", "real_name": "代理2", "role": UserRole.AGENT}
        ]
        
        for user_data in users_data:
            client.post(
                "/api/v1/admin/users/", 
                json=user_data,
                headers={"Authorization": f"Bearer {admin_token}"}
            )
        
        # 测试按角色筛选
        response = client.get(
            "/api/v1/admin/users/", 
            params={"role": UserRole.AGENT.value},
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 200
        data = response.json()
        print(f"test_user_role_filtering data: {data}")
        
        # 验证新的响应格式
        assert data["success"] is True
        assert len(data["data"]) == 2
        assert all(user["role"] == UserRole.AGENT for user in data["data"])
        
        response = client.get(
            "/api/v1/admin/users/", 
            params={"role": UserRole.ADMIN.value},
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["data"]) == 1+1 # 1个本测试的管理员 + 1个fixture中的超级管理员
        assert data["data"][0]["role"] == UserRole.ADMIN
    
    def test_change_user_password(self, client: TestClient, created_tenant, admin_token):
        """测试修改用户密码"""
        # 先创建一个用户
        user_data = {
            "username": "testuser",
            "password": "password123",
            "real_name": "测试用户",
            "role": UserRole.AGENT,
            "email": "<EMAIL>"
        }
        create_response = client.post(
            "/api/v1/admin/users/", 
            json=user_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        created_user = create_response.json()
        user_id = created_user["data"]["id"]
        
        password_data = {
            "old_password": "password123",
            "new_password": "newpassword123"
        }
        
        response = client.post(
            f"/api/v1/admin/users/{user_id}/change-password",
            json=password_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
    
    def test_activate_deactivate_user(self, client: TestClient, created_tenant, admin_token):
        """测试激活/停用用户"""
        # 先创建一个用户
        user_data = {
            "username": "testuser",
            "password": "password123",
            "real_name": "测试用户",
            "role": UserRole.AGENT,
            "email": "<EMAIL>"
        }
        create_response = client.post(
            "/api/v1/admin/users/", 
            json=user_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        created_user = create_response.json()
        user_id = created_user["data"]["id"]
        
        # 停用用户
        response = client.post(
            f"/api/v1/admin/users/{user_id}/deactivate",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["data"]["status"] == "inactive"
        
        # 激活用户
        response = client.post(
            f"/api/v1/admin/users/{user_id}/activate",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["data"]["status"] == "active" 