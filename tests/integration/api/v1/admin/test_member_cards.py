"""管理端会员卡API集成测试"""
import pytest
from fastapi.testclient import TestClient


class TestAdminMemberCardAPI:
    """管理端会员卡API测试"""
    
    def test_get_card_templates(self, client: TestClient, admin_token):
        """测试获取会员卡模板列表"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get(
            "/api/v1/admin/member-cards/templates?page=1&size=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_create_card_template(self, client: TestClient, admin_token):
        """测试创建会员卡模板"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        template_data = {
            "name": "测试储值卡模板",
            "card_type": "value_limited",
            "description": "测试用储值卡模板",
            "validity_days": 365,
            "sale_price": 100,
            "is_active": True
        }
        
        response = client.post(
            "/api/v1/admin/member-cards/templates",
            json=template_data,
            headers=headers
        )
        
        print("test_create_card_template response:", response.json())
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert data["data"]["name"] == template_data["name"]
