"""教师管理API集成测试"""
import pytest
from fastapi.testclient import TestClient
from app.api.common.exceptions import GlobalErrorCode
from app.features.teachers.exceptions import TeacherErrorCode


class TestAdminTeacherAPI:
    """教师管理API集成测试"""
    
    def test_create_teacher_success(self, client: TestClient, admin_token):
        """测试管理员成功创建教师"""
        teacher_data = {
            "name": "测试教师",
            "gender": "female",
            "phone": "13800138001",
            "email": "<EMAIL>",
            "price_per_class": 100.0,
            "teacher_category": "european",
            "region": "europe",
            "introduction": "经验丰富的英语教师",
            "wechat_openid": "wx_test_001",
            "show_to_members": True
        }
        
        response = client.post(
            "/api/v1/admin/teachers/",
            json=teacher_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 201
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "教师创建成功"
        assert "data" in data
        
        teacher = data["data"]
        assert teacher["name"] == teacher_data["name"]
        assert teacher["email"] == teacher_data["email"]
        assert teacher["phone"] == teacher_data["phone"]
        assert teacher["teacher_category"] == teacher_data["teacher_category"]
        assert teacher["region"] == teacher_data["region"]
        assert teacher["status"] == "pending"  # 默认状态
        assert "id" in teacher
        assert "created_at" in teacher
    
    def test_create_teacher_unauthorized(self, client: TestClient):
        """测试未认证用户创建教师"""
        teacher_data = {
            "name": "测试教师",
            "phone": "13800138002",
            "email": "<EMAIL>",
            "teacher_category": "european",
            "region": "europe"
        }
        
        response = client.post("/api/v1/admin/teachers/", json=teacher_data)
        
        assert response.status_code == 401
    
    def test_create_teacher_duplicate_email(self, client: TestClient, admin_token):
        """测试创建重复邮箱的教师"""
        teacher_data = {
            "name": "教师1",
            "phone": "13800138003",
            "email": "<EMAIL>",
            "teacher_category": "european",
            "region": "europe"
        }
        
        # 创建第一个教师
        response1 = client.post(
            "/api/v1/admin/teachers/",
            json=teacher_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response1.status_code == 201
        
        # 尝试创建重复邮箱的教师
        teacher_data2 = teacher_data.copy()
        teacher_data2["name"] = "教师2"
        teacher_data2["phone"] = "***********"
        
        response2 = client.post(
            "/api/v1/admin/teachers/",
            json=teacher_data2,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response2.status_code == 400
        data = response2.json()
        assert data["success"] is False
        assert TeacherErrorCode.EMAIL_EXISTS.value in data["business_code"]
    
    def test_create_teacher_invalid_data(self, client: TestClient, admin_token):
        """测试创建教师时数据验证失败"""
        teacher_data = {
            "name": "",  # 空名称
            "phone": "invalid_phone",  # 无效手机号
            "email": "invalid_email",  # 无效邮箱
            "teacher_category": "invalid_category",  # 无效分类
            "region": "invalid_region"  # 无效区域
        }
        
        response = client.post(
            "/api/v1/admin/teachers/",
            json=teacher_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 422
        data = response.json()
        assert data["success"] is False
        assert data["business_code"] == GlobalErrorCode.VALIDATION_ERROR.value
    
    def test_get_teachers_success(self, client: TestClient, admin_token):
        """测试获取教师列表"""
        response = client.get(
            "/api/v1/admin/teachers/",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "获取教师列表成功"
        assert "data" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert isinstance(data["data"], list)
        assert isinstance(data["total"], int)
    
    def test_get_teachers_with_filters(self, client: TestClient, admin_token):
        """测试带筛选条件的教师列表"""
        # 先创建一些教师
        teacher_data1 = {
            "name": "欧洲教师",
            "phone": "13800138011",
            "email": "<EMAIL>",
            "teacher_category": "european",
            "region": "europe",
            "price_per_class": 120.0
        }
        teacher_data2 = {
            "name": "菲律宾教师",
            "phone": "13800138012",
            "email": "<EMAIL>",
            "teacher_category": "filipino",
            "region": "philippines",
            "price_per_class": 80.0
        }
        
        client.post(
            "/api/v1/admin/teachers/",
            json=teacher_data1,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        client.post(
            "/api/v1/admin/teachers/",
            json=teacher_data2,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        # 测试按分类筛选
        response = client.get(
            "/api/v1/admin/teachers/",
            params={"teacher_category": "european"},
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        
        # 验证筛选结果
        teachers = data["data"]
        if teachers:
            assert all(teacher["teacher_category"] == "european" for teacher in teachers)
    
    def test_get_teachers_pagination(self, client: TestClient, admin_token):
        """测试教师列表分页"""
        # 创建多个教师
        for i in range(5):
            teacher_data = {
                "name": f"教师{i}",
                "phone": f"1380013801{i}",
                "email": f"teacher{i}@example.com",
                "teacher_category": "european",
                "region": "europe"
            }
            client.post(
                "/api/v1/admin/teachers/",
                json=teacher_data,
                headers={"Authorization": f"Bearer {admin_token}"}
            )
        
        # 测试分页
        response = client.get(
            "/api/v1/admin/teachers/",
            params={"page": 1, "size": 3},
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["data"]) <= 3
        assert data["page"] == 1
        assert data["size"] == 3
    
    def test_get_teachers_with_search(self, client: TestClient, admin_token):
        """测试搜索教师"""
        # 创建一些教师
        teacher_data1 = {
            "name": "张老师",
            "phone": "13800138021",
            "email": "<EMAIL>",
            "teacher_category": "european",
            "region": "europe",
            "introduction": "专业英语教师"
        }
        teacher_data2 = {
            "name": "李老师",
            "phone": "13800138022",
            "email": "<EMAIL>",
            "teacher_category": "filipino",
            "region": "philippines",
            "introduction": "口语专家"
        }
        
        client.post(
            "/api/v1/admin/teachers/",
            json=teacher_data1,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        client.post(
            "/api/v1/admin/teachers/",
            json=teacher_data2,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        # 按姓名搜索
        response = client.get(
            "/api/v1/admin/teachers/",
            params={"name": "张老师"},
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        
        # 验证搜索结果
        teachers = data["data"]
        if teachers:
            found_zhang = any(teacher["name"] == "张老师" for teacher in teachers)
            assert found_zhang
    
    def test_get_teacher_by_id_success(self, client: TestClient, admin_token, created_teacher):
        """测试根据ID获取教师详情"""
        response = client.get(
            f"/api/v1/admin/teachers/{created_teacher['id']}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "获取教师详情成功"
        assert data["data"]["id"] == created_teacher["id"]
        assert data["data"]["name"] == created_teacher["name"]
        assert "tags" in data["data"]  # 教师详情应包含标签信息
    
    def test_get_teacher_unauthorized(self, client: TestClient, created_teacher):
        """测试未认证用户获取教师详情"""
        response = client.get(f"/api/v1/admin/teachers/{created_teacher['id']}")
        
        assert response.status_code == 401
    
    def test_get_nonexistent_teacher(self, client: TestClient, admin_token):
        """测试获取不存在的教师"""
        response = client.get(
            "/api/v1/admin/teachers/99999",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False

    def test_update_teacher_success(self, client: TestClient, admin_token, created_teacher):
        """测试更新教师信息"""
        update_data = {
            "name": "更新后的教师",
            "introduction": "更新后的介绍",
            "price_per_class": 150.0,
            "show_to_members": False
        }

        response = client.post(
            f"/api/v1/admin/teachers/{created_teacher['id']}/update",
            json=update_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "教师信息更新成功"
        assert data["data"]["name"] == update_data["name"]
        assert data["data"]["introduction"] == update_data["introduction"]
        assert data["data"]["price_per_class"] == update_data["price_per_class"]
        assert data["data"]["show_to_members"] == update_data["show_to_members"]

    def test_update_nonexistent_teacher(self, client: TestClient, admin_token):
        """测试更新不存在的教师"""
        update_data = {
            "name": "不存在的教师"
        }

        response = client.post(
            "/api/v1/admin/teachers/99999/update",
            json=update_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False

    def test_delete_teacher_success(self, client: TestClient, admin_token):
        """测试删除教师"""
        # 先创建一个教师
        teacher_data = {
            "name": "待删除教师",
            "phone": "13800138099",
            "email": "<EMAIL>",
            "teacher_category": "european",
            "region": "europe"
        }

        create_response = client.post(
            "/api/v1/admin/teachers/",
            json=teacher_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        teacher_id = create_response.json()["data"]["id"]

        # 删除教师
        response = client.post(
            f"/api/v1/admin/teachers/{teacher_id}/delete",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "教师删除成功"

        # 验证教师已被删除
        get_response = client.get(
            f"/api/v1/admin/teachers/{teacher_id}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert get_response.status_code == 404

    def test_delete_teacher_not_found(self, client: TestClient, admin_token):
        """测试删除不存在的教师"""
        response = client.post(
            "/api/v1/admin/teachers/99999/delete",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False


class TestTeacherStatusAPI:
    """教师状态管理API集成测试"""

    def test_activate_teacher_success(self, client: TestClient, admin_token, created_teacher):
        """测试激活教师"""
        response = client.post(
            f"/api/v1/admin/teachers/{created_teacher['id']}/activate",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "教师激活成功"
        assert data["data"]["status"] == "active"

    def test_deactivate_teacher_success(self, client: TestClient, admin_token, created_teacher):
        """测试停用教师"""
        # 先激活教师
        client.post(
            f"/api/v1/admin/teachers/{created_teacher['id']}/activate",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        # 然后停用
        response = client.post(
            f"/api/v1/admin/teachers/{created_teacher['id']}/deactivate",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "教师停用成功"
        assert data["data"]["status"] == "inactive"

    def test_update_teacher_status_success(self, client: TestClient, admin_token, created_teacher):
        """测试更新教师状态"""
        status_data = {
            "status": "active",
            "reason": "通过审核"
        }

        response = client.post(
            f"/api/v1/admin/teachers/{created_teacher['id']}/status",
            json=status_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "教师状态更新成功"
        assert data["data"]["status"] == status_data["status"]

    def test_status_management_nonexistent_teacher(self, client: TestClient, admin_token):
        """测试管理不存在教师的状态"""
        response = client.post(
            "/api/v1/admin/teachers/99999/activate",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False


class TestTeacherTagAPI:
    """教师标签管理API集成测试"""

    def test_assign_tags_to_teacher_success(self, client: TestClient, admin_token, created_teacher, multiple_tags):
        """测试为教师分配标签"""
        tag_data = {
            "tag_ids": [multiple_tags[0]["id"], multiple_tags[1]["id"]]
        }

        response = client.post(
            f"/api/v1/admin/teachers/{created_teacher['id']}/tags",
            json=tag_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "教师标签分配成功"

    def test_get_teacher_tags_success(self, client: TestClient, admin_token, created_teacher, multiple_tags):
        """测试获取教师标签列表"""
        # 先分配标签
        tag_data = {
            "tag_ids": [multiple_tags[0]["id"]]
        }
        client.post(
            f"/api/v1/admin/teachers/{created_teacher['id']}/tags",
            json=tag_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        # 获取标签列表
        response = client.get(
            f"/api/v1/admin/teachers/{created_teacher['id']}/tags",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "获取教师标签列表成功"
        assert "data" in data
        assert "total" in data
        assert isinstance(data["data"], list)

        print("data", data)
        # 验证标签数据
        if data["data"]:
            tag = data["data"][0]
            assert "id" in tag
            assert "name" in tag
            assert "category_id" in tag

    def test_remove_tags_from_teacher_success(self, client: TestClient, admin_token, created_teacher, multiple_tags):
        """测试移除教师标签"""
        # 先分配标签
        tag_data = {
            "tag_ids": [multiple_tags[0]["id"], multiple_tags[1]["id"]]
        }
        client.post(
            f"/api/v1/admin/teachers/{created_teacher['id']}/tags",
            json=tag_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        # 移除标签
        response = client.post(
            f"/api/v1/admin/teachers/{created_teacher['id']}/tags/remove",
            params={"tag_ids": str(multiple_tags[0]["id"])},
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "教师标签移除成功"

    def test_batch_manage_teacher_tags_success(self, client: TestClient, admin_token, multiple_teachers, multiple_tags):
        """测试批量管理教师标签"""
        # 批量分配标签
        batch_data = {
            "operation": "add",
            "teacher_ids": [multiple_teachers[0]["id"], multiple_teachers[1]["id"]],
            "tag_ids": [multiple_tags[0]["id"]]
        }

        response = client.post(
            "/api/v1/admin/teachers/tags/batch",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "批量分配标签成功"

        # 批量移除标签
        batch_data["operation"] = "remove"
        response = client.post(
            "/api/v1/admin/teachers/tags/batch",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "批量移除标签成功"

    def test_assign_tags_to_nonexistent_teacher(self, client: TestClient, admin_token, multiple_tags):
        """测试为不存在的教师分配标签"""
        tag_data = {
            "tag_ids": [multiple_tags[0]["id"]]
        }

        response = client.post(
            "/api/v1/admin/teachers/99999/tags",
            json=tag_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False


class TestTeacherAdvancedQueryAPI:
    """教师高级查询API集成测试"""

    def test_get_teachers_by_priority_success(self, client: TestClient, admin_token, multiple_teachers):
        """测试按优先级获取教师"""
        # 先激活一些教师
        for teacher in multiple_teachers[:2]:
            client.post(
                f"/api/v1/admin/teachers/{teacher['id']}/activate",
                headers={"Authorization": f"Bearer {admin_token}"}
            )

        response = client.get(
            "/api/v1/admin/teachers/priority",
            params={"limit": 5},
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "获取优先级教师列表成功"
        assert "data" in data
        assert "total" in data
        assert isinstance(data["data"], list)

        # 验证所有教师都是激活状态
        teachers = data["data"]
        if teachers:
            assert all(teacher["status"] == "active" for teacher in teachers)

    def test_get_available_teachers_for_members_success(self, client: TestClient, admin_token, multiple_teachers):
        """测试获取对会员可见的教师"""
        # 先激活一些教师并设置为对会员可见
        for teacher in multiple_teachers[:2]:
            client.post(
                f"/api/v1/admin/teachers/{teacher['id']}/activate",
                headers={"Authorization": f"Bearer {admin_token}"}
            )
            # 更新为对会员可见
            client.post(
                f"/api/v1/admin/teachers/{teacher['id']}/update",
                json={"show_to_members": True},
                headers={"Authorization": f"Bearer {admin_token}"}
            )

        response = client.get(
            "/api/v1/admin/teachers/available",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "获取可用教师列表成功"
        assert "data" in data
        assert "total" in data

        # 验证所有教师都是激活且对会员可见
        teachers = data["data"]
        if teachers:
            assert all(teacher["status"] == "active" for teacher in teachers)
            assert all(teacher["show_to_members"] is True for teacher in teachers)

    def test_search_teachers_success(self, client: TestClient, admin_token):
        """测试搜索教师"""
        # 先创建一些教师
        teacher_data = {
            "name": "专业英语教师",
            "phone": "13800138031",
            "email": "<EMAIL>",
            "teacher_category": "european",
            "region": "europe",
            "introduction": "专业的英语教学经验"
        }

        create_response = client.post(
            "/api/v1/admin/teachers/",
            json=teacher_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        teacher_id = create_response.json()["data"]["id"]

        # 激活教师
        client.post(
            f"/api/v1/admin/teachers/{teacher_id}/activate",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        # 搜索教师
        response = client.get(
            "/api/v1/admin/teachers/search",
            params={"keyword": "专业", "limit": 10},
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "搜索教师成功"
        assert "data" in data
        assert "total" in data

        # 验证搜索结果
        teachers = data["data"]
        if teachers:
            found_professional = any("专业" in teacher["name"] or
                                   (teacher.get("introduction") and "专业" in teacher["introduction"])
                                   for teacher in teachers)
            assert found_professional

    def test_get_teacher_statistics_success(self, client: TestClient, admin_token):
        """测试获取教师统计信息"""
        response = client.get(
            "/api/v1/admin/teachers/statistics",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "获取教师统计信息成功"
        assert "data" in data

        stats = data["data"]
        assert "total_teachers" in stats
        assert "active_teachers" in stats
        assert "category_distribution" in stats
        assert "region_distribution" in stats
        assert isinstance(stats["total_teachers"], int)
        assert isinstance(stats["active_teachers"], int)
        assert isinstance(stats["category_distribution"], dict)
        assert isinstance(stats["region_distribution"], dict)


class TestAdminTeacherAPIAuthentication:
    """教师API认证测试"""

    def test_create_teacher_without_token_should_fail(self, client: TestClient):
        """测试未认证创建教师应该失败"""
        teacher_data = {
            "name": "测试教师",
            "phone": "13800138001",
            "email": "<EMAIL>",
            "teacher_category": "european",
            "region": "europe"
        }

        response = client.post("/api/v1/admin/teachers/", json=teacher_data)

        assert response.status_code == 401

    def test_get_teachers_without_token_should_fail(self, client: TestClient):
        """测试未认证获取教师列表应该失败"""
        response = client.get("/api/v1/admin/teachers/")

        assert response.status_code == 401

    def test_update_teacher_without_token_should_fail(self, client: TestClient):
        """测试未认证更新教师应该失败"""
        update_data = {"name": "更新教师"}

        response = client.post("/api/v1/admin/teachers/1/update", json=update_data)

        assert response.status_code == 401


class TestAdminTeacherAPIValidation:
    """教师API数据验证测试"""

    def test_create_teacher_with_invalid_data_should_fail(self, client: TestClient, admin_token):
        """测试使用无效数据创建教师应该失败"""
        invalid_data = {
            "name": "",  # 空名称
            "phone": "123",  # 无效手机号
            "email": "invalid-email",  # 无效邮箱
            "price_per_class": -10,  # 负价格
            "teacher_category": "invalid",  # 无效分类
            "region": "invalid"  # 无效区域
        }

        response = client.post(
            "/api/v1/admin/teachers/",
            json=invalid_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 422
        data = response.json()
        assert data["success"] is False
        assert data["business_code"] == GlobalErrorCode.VALIDATION_ERROR.value
