"""标签管理API集成测试"""
import pytest
from fastapi.testclient import TestClient
from app.api.common.exceptions import GlobalErrorCode
from app.features.tags.exceptions import TagErrorCode


class TestAdminTagCategoryAPI:
    """标签分类管理API集成测试"""
    
    def test_create_tag_category_success(self, client: TestClient, admin_token):
        """测试管理员成功创建标签分类"""
        category_data = {
            "name": "教材类标签",
            "description": "用于标记教材相关的标签",
            "sort_order": 1
        }
        
        response = client.post(
            "/api/v1/admin/tags/categories",
            json=category_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 201
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "标签分类创建成功"
        assert "data" in data
        
        category_data_response = data["data"]
        assert category_data_response["name"] == category_data["name"]
        assert category_data_response["description"] == category_data["description"]
        assert category_data_response["sort_order"] == category_data["sort_order"]
        assert "id" in category_data_response
        assert "created_at" in category_data_response
    
    def test_create_tag_category_with_duplicate_name_should_fail(
        self, client: TestClient, admin_token, created_tag_category
    ):
        """测试创建重复名称的标签分类应该失败"""
        category_data = {
            "name": created_tag_category["name"],
            "description": "重复名称测试",
            "sort_order": 2
        }
        
        response = client.post(
            "/api/v1/admin/tags/categories",
            json=category_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert TagErrorCode.CATEGORY_NAME_EXISTS in data["business_code"]
    
    def test_get_tag_categories_success(self, client: TestClient, admin_token, multiple_tag_categories):
        """测试获取标签分类列表成功"""
        response = client.get(
            "/api/v1/admin/tags/categories",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "获取标签分类列表成功"
        assert "data" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data

        # 验证分页信息
        assert data["total"] >= len(multiple_tag_categories)
        assert data["page"] == 1
        assert data["size"] == 20
    
    def test_get_tag_categories_with_search_success(
        self, client: TestClient, admin_token, created_tag_category
    ):
        """测试搜索标签分类成功"""
        search_name = created_tag_category["name"][:3]  # 取前3个字符
        
        response = client.get(
            f"/api/v1/admin/tags/categories?name={search_name}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["total"] >= 1
    
    def test_get_tag_category_by_id_success(
        self, client: TestClient, admin_token, created_tag_category
    ):
        """测试根据ID获取标签分类成功"""
        response = client.get(
            f"/api/v1/admin/tags/categories/{created_tag_category['id']}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "获取标签分类详情成功"
        
        category_data = data["data"]
        assert category_data["id"] == created_tag_category["id"]
        assert category_data["name"] == created_tag_category["name"]
    
    def test_get_tag_category_with_invalid_id_should_fail(
        self, client: TestClient, admin_token
    ):
        """测试获取不存在的标签分类应该失败"""
        response = client.get(
            "/api/v1/admin/tags/categories/99999",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
        assert GlobalErrorCode.BUSINESS_ERROR in data["business_code"]
    
    def test_update_tag_category_success(
        self, client: TestClient, admin_token, created_tag_category
    ):
        """测试更新标签分类成功"""
        update_data = {
            "name": "更新后的分类名称",
            "description": "更新后的描述",
            "sort_order": 10
        }
        
        response = client.post(
            f"/api/v1/admin/tags/categories/{created_tag_category['id']}",
            json=update_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "标签分类更新成功"
        
        category_data = data["data"]
        assert category_data["name"] == update_data["name"]
        assert category_data["description"] == update_data["description"]
        assert category_data["sort_order"] == update_data["sort_order"]
    
    def test_delete_tag_category_success(
        self, client: TestClient, admin_token, created_tag_category
    ):
        """测试删除标签分类成功"""
        response = client.delete(
            f"/api/v1/admin/tags/categories/{created_tag_category['id']}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "标签分类删除成功"
    
    def test_delete_tag_category_with_tags_should_fail(
        self, client: TestClient, admin_token, created_tag
    ):
        """测试删除有标签的分类应该失败"""
        response = client.delete(
            f"/api/v1/admin/tags/categories/{created_tag['category_id']}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert TagErrorCode.CATEGORY_HAS_TAGS in data["business_code"]


class TestTagAPI:
    """标签管理API集成测试"""
    
    def test_create_tag_success(self, client: TestClient, admin_token, created_tag_category):
        """测试管理员成功创建标签"""
        tag_data = {
            "name": "新概念英语",
            "description": "新概念英语教材标签",
            "category_id": created_tag_category["id"],
            "status": "active"
        }
        
        response = client.post(
            "/api/v1/admin/tags/",
            json=tag_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 201
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "标签创建成功"
        assert "data" in data
        
        tag_data_response = data["data"]
        assert tag_data_response["name"] == tag_data["name"]
        assert tag_data_response["description"] == tag_data["description"]
        assert tag_data_response["category_id"] == tag_data["category_id"]
        assert tag_data_response["status"] == tag_data["status"]
        assert "id" in tag_data_response
    
    def test_create_tag_with_invalid_category_should_fail(
        self, client: TestClient, admin_token
    ):
        """测试使用无效分类ID创建标签应该失败"""
        tag_data = {
            "name": "测试标签",
            "description": "测试描述",
            "category_id": 99999,
            "status": "active"
        }
        
        response = client.post(
            "/api/v1/admin/tags/",
            json=tag_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
        assert GlobalErrorCode.BUSINESS_ERROR in data["business_code"]
    
    def test_create_tag_with_duplicate_name_should_fail(
        self, client: TestClient, admin_token, created_tag
    ):
        """测试创建重复名称的标签应该失败"""
        tag_data = {
            "name": created_tag["name"],
            "description": "重复名称测试",
            "category_id": created_tag["category_id"],
            "status": "active"
        }
        
        response = client.post(
            "/api/v1/admin/tags/",
            json=tag_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert TagErrorCode.TAG_NAME_EXISTS in data["business_code"]
    
    def test_get_tags_success(self, client: TestClient, admin_token, multiple_tags):
        """测试获取标签列表成功"""
        response = client.get(
            "/api/v1/admin/tags/",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "获取标签列表成功"
        assert "data" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data

        # 验证分页信息
        assert data["total"] >= len(multiple_tags)
        
        # 验证标签数据包含分类信息
        if data["data"]:
            tag_data = data["data"][0]
            assert "category_name" in tag_data
    
    def test_get_tags_with_filters_success(
        self, client: TestClient, admin_token, created_tag
    ):
        """测试使用筛选条件获取标签成功"""
        response = client.get(
            f"/api/v1/admin/tags/?category_id={created_tag['category_id']}&status=active",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["total"] >= 1
    
    def test_get_tag_by_id_success(self, client: TestClient, admin_token, created_tag):
        """测试根据ID获取标签成功"""
        response = client.get(
            f"/api/v1/admin/tags/{created_tag['id']}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "获取标签详情成功"
        
        tag_data = data["data"]
        assert tag_data["id"] == created_tag["id"]
        assert tag_data["name"] == created_tag["name"]
    
    def test_update_tag_success(self, client: TestClient, admin_token, created_tag):
        """测试更新标签成功"""
        update_data = {
            "name": "更新后的标签名称",
            "description": "更新后的描述",
            "status": "inactive"
        }
        
        response = client.post(
            f"/api/v1/admin/tags/{created_tag['id']}",
            json=update_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "标签更新成功"
        
        tag_data = data["data"]
        assert tag_data["name"] == update_data["name"]
        assert tag_data["description"] == update_data["description"]
        assert tag_data["status"] == update_data["status"]
    
    def test_delete_tag_success(self, client: TestClient, admin_token, created_tag):
        """测试删除标签成功"""
        response = client.delete(
            f"/api/v1/admin/tags/{created_tag['id']}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "标签删除成功"


class TestTagBatchAPI:
    """标签批量操作API集成测试"""

    def test_batch_create_tags_success(
        self, client: TestClient, admin_token, created_tag_category, batch_tag_names
    ):
        """测试批量创建标签成功"""
        batch_data = {
            "category_id": created_tag_category["id"],
            "tags": batch_tag_names
        }

        response = client.post(
            "/api/v1/admin/tags/batch",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 201
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "批量创建标签成功" in data["message"]
        assert "data" in data

        created_tags = data["data"]
        assert len(created_tags) == len(batch_tag_names)
        assert all(tag["category_id"] == created_tag_category["id"] for tag in created_tags)
        assert all(tag["name"] in batch_tag_names for tag in created_tags)

    def test_batch_create_tags_with_invalid_category_should_fail(
        self, client: TestClient, admin_token, batch_tag_names
    ):
        """测试使用无效分类ID批量创建标签应该失败"""
        batch_data = {
            "category_id": 99999,
            "tags": batch_tag_names
        }

        response = client.post(
            "/api/v1/admin/tags/batch",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
        assert GlobalErrorCode.BUSINESS_ERROR in data["business_code"]

    def test_batch_update_tags_success(
        self, client: TestClient, admin_token, multiple_tags
    ):
        """测试批量更新标签成功"""
        tag_ids = [tag["id"] for tag in multiple_tags]
        batch_data = {
            "tag_ids": tag_ids,
            "status": "inactive"
        }

        response = client.post(
            "/api/v1/admin/tags/batch/update",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "批量更新标签成功" in data["message"]
        assert "data" in data

        updated_tags = data["data"]
        assert len(updated_tags) == len(multiple_tags)
        assert all(tag["status"] == "inactive" for tag in updated_tags)

    def test_batch_update_tags_with_invalid_ids_should_fail(
        self, client: TestClient, admin_token
    ):
        """测试使用无效ID批量更新标签应该失败"""
        batch_data = {
            "tag_ids": [99999, 99998],
            "status": "inactive"
        }

        response = client.post(
            "/api/v1/admin/tags/batch/update",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        print("test_batch_update_tags_with_invalid_ids_should_fail:", response.json())
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "部分标签不存在" in data["message"]


class TestTagConvenienceAPI:
    """标签便捷查询API集成测试"""

    def test_get_tags_by_category_success(
        self, client: TestClient, admin_token, created_tag
    ):
        """测试获取指定分类下的标签成功"""
        response = client.get(
            f"/api/v1/admin/tags/categories/{created_tag['category_id']}/tags",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "获取分类标签列表成功"
        assert "data" in data
        assert "total" in data

        # 验证所有标签都属于指定分类
        tags = data["data"]
        if tags:
            assert all(tag["category_id"] == created_tag["category_id"] for tag in tags)
            # 验证包含分类名称
            assert all("category_name" in tag for tag in tags)

    def test_get_tags_by_category_with_status_filter_success(
        self, client: TestClient, admin_token, created_tag
    ):
        """测试按状态筛选分类下的标签成功"""
        response = client.get(
            f"/api/v1/admin/tags/categories/{created_tag['category_id']}/tags?status=active",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

        # 验证筛选结果
        tags = data["data"]
        if tags:
            assert all(tag["status"] == "active" for tag in tags)

    def test_get_active_tags_success(self, client: TestClient, admin_token, multiple_tags):
        """测试获取所有激活标签成功"""
        response = client.get(
            "/api/v1/admin/tags/active",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        if response.status_code != 200:
            print(f"Error response: {response.json()}")
        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "获取激活标签列表成功"
        assert "data" in data
        assert "total" in data

        # 验证所有标签都是激活状态
        tags = data["data"]
        if tags:
            assert all(tag["status"] == "active" for tag in tags)
            # 验证包含分类信息
            assert all("category_name" in tag for tag in tags)


class TestTagAPIAuthentication:
    """标签API认证测试"""

    def test_create_tag_category_without_token_should_fail(self, client: TestClient):
        """测试未认证创建标签分类应该失败"""
        category_data = {
            "name": "测试分类",
            "description": "测试描述",
            "sort_order": 1
        }

        response = client.post("/api/v1/admin/tags/categories", json=category_data)

        assert response.status_code == 401
        data = response.json()
        assert data["success"] is False
        assert GlobalErrorCode.AUTHENTICATION_FAILED in data["business_code"]

    def test_create_tag_without_token_should_fail(self, client: TestClient):
        """测试未认证创建标签应该失败"""
        tag_data = {
            "name": "测试标签",
            "description": "测试描述",
            "category_id": 1,
            "status": "active"
        }

        response = client.post("/api/v1/admin/tags/", json=tag_data)

        assert response.status_code == 401
        data = response.json()
        assert data["success"] is False
        assert GlobalErrorCode.AUTHENTICATION_FAILED in data["business_code"]

    def test_get_tags_without_token_should_fail(self, client: TestClient):
        """测试未认证获取标签列表应该失败"""
        response = client.get("/api/v1/admin/tags/")

        assert response.status_code == 401
        data = response.json()
        assert data["success"] is False
        assert GlobalErrorCode.AUTHENTICATION_FAILED in data["business_code"]


class TestTagAPIValidation:
    """标签API数据验证测试"""

    def test_create_tag_category_with_invalid_data_should_fail(
        self, client: TestClient, admin_token
    ):
        """测试使用无效数据创建标签分类应该失败"""
        invalid_data = {
            "name": "",  # 空名称
            "sort_order": -1  # 负数排序
        }

        response = client.post(
            "/api/v1/admin/tags/categories",
            json=invalid_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 422
        data = response.json()
        assert data["success"] is False
        assert GlobalErrorCode.VALIDATION_ERROR in data["business_code"]

    def test_create_tag_with_invalid_data_should_fail(
        self, client: TestClient, admin_token
    ):
        """测试使用无效数据创建标签应该失败"""
        invalid_data = {
            "name": "",  # 空名称
            "category_id": "invalid",  # 无效类型
            "status": "invalid_status"  # 无效状态
        }

        response = client.post(
            "/api/v1/admin/tags/",
            json=invalid_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 422
        data = response.json()
        print("test_create_tag_with_invalid_data_should_fail data", data)
        assert data["success"] is False
        assert GlobalErrorCode.VALIDATION_ERROR in data["business_code"]
