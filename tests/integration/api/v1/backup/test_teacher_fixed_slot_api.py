"""教师固定时间段API集成测试"""
import pytest
from fastapi.testclient import TestClient
from app.api.common.exceptions import GlobalErrorCode
from app.features.teachers.fixed_slots_exceptions import TeacherFixedSlotErrorCode


class TestTeacherFixedSlotBasicAPI:
    """教师固定时间段基础API测试"""

    def test_create_fixed_slot_success(self, client: TestClient, admin_token, created_teacher):
        """测试成功创建固定时间段"""
        slot_data = {
            "teacher_id": created_teacher["id"],
            "weekday": 1,  # 星期一
            "start_time": "08:30",
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": True,
            "created_by": created_teacher["created_by"]
        }
        
        response = client.post(
            "/api/v1/teachers/fixed-slots/",
            json=slot_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 201
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "固定时间段创建成功"
        assert "data" in data
        
        # 验证返回的数据
        slot = data["data"]
        assert slot["teacher_id"] == created_teacher["id"]
        assert slot["weekday"] == 1
        assert slot["weekday_name"] == "星期一"
        assert slot["start_time"] == "08:30:00"
        assert slot["duration_minutes"] == 25
        assert slot["is_available"] is True
        assert slot["is_visible_to_members"] is True

    def test_create_fixed_slot_unauthorized(self, client: TestClient, created_teacher):
        """测试未认证用户创建固定时间段"""
        slot_data = {
            "teacher_id": created_teacher["id"],
            "weekday": 1,
            "start_time": "08:30",
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": True,
            "created_by": created_teacher["created_by"]
        }
        
        response = client.post("/api/v1/teachers/fixed-slots/", json=slot_data)
        
        assert response.status_code == 401

    def test_create_fixed_slot_teacher_not_found(self, client: TestClient, admin_token):
        """测试创建固定时间段时教师不存在"""
        slot_data = {
            "teacher_id": 99999,  # 不存在的教师ID
            "weekday": 1,
            "start_time": "08:30",
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": True,
            "created_by": 1
        }
        
        response = client.post(
            "/api/v1/teachers/fixed-slots/",
            json=slot_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert TeacherFixedSlotErrorCode.TEACHER_NOT_FOUND.value in data["business_code"]

    def test_create_fixed_slot_invalid_data(self, client: TestClient, admin_token, created_teacher):
        """测试创建固定时间段时数据验证失败"""
        slot_data = {
            "teacher_id": created_teacher["id"],
            "weekday": 8,  # 无效的星期
            "start_time": "25:00",  # 无效的时间
            "duration_minutes": 200,  # 超出范围的时长
            "is_available": True,
            "is_visible_to_members": True,
            "created_by": created_teacher["created_by"]
        }
        
        response = client.post(
            "/api/v1/teachers/fixed-slots/",
            json=slot_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 422
        data = response.json()
        assert data["success"] is False
        assert data["business_code"] == GlobalErrorCode.VALIDATION_ERROR.value

    def test_get_fixed_slots_success(self, client: TestClient, admin_token, created_fixed_slot):
        """测试获取固定时间段列表"""
        response = client.get(
            "/api/v1/teachers/fixed-slots/",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "获取固定时间段列表成功"
        assert "data" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert isinstance(data["data"], list)
        assert isinstance(data["total"], int)

    def test_get_fixed_slots_with_filters(self, client: TestClient, admin_token, created_multiple_fixed_slots):
        """测试带筛选条件的查询"""
        teacher_id = created_multiple_fixed_slots[0]["teacher_id"]
        
        response = client.get(
            "/api/v1/teachers/fixed-slots/",
            params={
                "teacher_id": teacher_id,
                "weekday": 1,  # 星期一
                "is_available": True,
                "page": 1,
                "size": 10
            },
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证筛选结果
        assert data["success"] is True
        for slot in data["data"]:
            assert slot["teacher_id"] == teacher_id
            assert slot["weekday"] == 1
            assert slot["is_available"] is True

    def test_get_fixed_slot_by_id_success(self, client: TestClient, admin_token, created_fixed_slot):
        """测试根据ID获取固定时间段详情"""
        response = client.get(
            f"/api/v1/teachers/fixed-slots/{created_fixed_slot['id']}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "获取固定时间段详情成功"
        assert data["data"]["id"] == created_fixed_slot["id"]
        assert data["data"]["teacher_id"] == created_fixed_slot["teacher_id"]

    def test_get_fixed_slot_not_found(self, client: TestClient, admin_token):
        """测试获取不存在的固定时间段"""
        response = client.get(
            "/api/v1/teachers/fixed-slots/99999",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 404

    def test_update_fixed_slot_success(self, client: TestClient, admin_token, created_fixed_slot):
        """测试更新固定时间段"""
        update_data = {
            "duration_minutes": 30,
            "is_available": False
        }
        
        response = client.post(
            f"/api/v1/teachers/fixed-slots/{created_fixed_slot['id']}/update",
            json=update_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "固定时间段更新成功"
        assert data["data"]["id"] == created_fixed_slot["id"]
        assert data["data"]["duration_minutes"] == 30
        assert data["data"]["is_available"] is False

    def test_update_fixed_slot_not_found(self, client: TestClient, admin_token):
        """测试更新不存在的固定时间段"""
        update_data = {"is_available": False}
        
        response = client.post(
            "/api/v1/teachers/fixed-slots/99999/update",
            json=update_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 404

    def test_delete_fixed_slot_success(self, client: TestClient, admin_token, created_fixed_slot):
        """测试删除固定时间段"""
        response = client.post(
            f"/api/v1/teachers/fixed-slots/{created_fixed_slot['id']}/delete",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "固定时间段删除成功"
        
        # 验证已删除
        get_response = client.get(
            f"/api/v1/teachers/fixed-slots/{created_fixed_slot['id']}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert get_response.status_code == 404

    def test_delete_fixed_slot_not_found(self, client: TestClient, admin_token):
        """测试删除不存在的固定时间段"""
        response = client.post(
            "/api/v1/teachers/fixed-slots/99999/delete",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 404


class TestTeacherFixedSlotQueryAPI:
    """教师固定时间段查询API测试"""

    def test_check_time_conflict_success(self, client: TestClient, admin_token, created_fixed_slot):
        """测试时间冲突检测"""
        conflict_data = {
            "teacher_id": created_fixed_slot["teacher_id"],
            "weekday": created_fixed_slot["weekday"],
            "start_time": "08:30",  # 使用字符串格式
            "duration_minutes": 25
        }
        
        response = client.post(
            "/api/v1/teachers/fixed-slots/check-conflict",
            json=conflict_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["data"] is True  # 应该有冲突
        assert "冲突" in data["message"]

    def test_check_time_no_conflict(self, client: TestClient, admin_token, created_fixed_slot):
        """测试无时间冲突"""
        no_conflict_data = {
            "teacher_id": created_fixed_slot["teacher_id"],
            "weekday": 2,  # 不同星期
            "start_time": "08:30",  # 使用字符串格式
            "duration_minutes": 25
        }
        
        response = client.post(
            "/api/v1/teachers/fixed-slots/check-conflict",
            json=no_conflict_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["data"] is False  # 应该无冲突
        assert "无" in data["message"]

    def test_get_available_times_success(self, client: TestClient, admin_token, created_multiple_fixed_slots):
        """测试获取可用时间段"""
        teacher_id = created_multiple_fixed_slots[0]["teacher_id"]
        
        query_data = {
            "teacher_id": teacher_id,
            "only_available": True,
            "only_visible": True
        }
        
        response = client.post(
            "/api/v1/teachers/fixed-slots/available-times",
            json=query_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert isinstance(data["data"], list)
        
        # 验证筛选结果
        for slot in data["data"]:
            assert slot["teacher_id"] == teacher_id
            assert slot["is_available"] is True
            assert slot["is_visible_to_members"] is True

    def test_get_statistics_success(self, client: TestClient, admin_token, created_multiple_fixed_slots):
        """测试获取统计信息"""
        teacher_id = created_multiple_fixed_slots[0]["teacher_id"]
        
        response = client.get(
            "/api/v1/teachers/fixed-slots/statistics",
            params={"teacher_id": teacher_id},
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert "data" in data
        
        stats = data["data"]
        assert "teacher_id" in stats
        assert "total_slots" in stats
        assert "available_slots" in stats
        assert "visible_slots" in stats
        assert "weekday_distribution" in stats
        assert isinstance(stats["weekday_distribution"], dict)

    def test_get_weekly_schedule_success(self, client: TestClient, admin_token, created_weekly_schedule):
        """测试获取教师周时间安排"""
        # 获取第一个教师的ID
        first_weekday_slots = list(created_weekly_schedule.values())[0]
        teacher_id = first_weekday_slots[0]["teacher_id"]
        
        response = client.get(
            f"/api/v1/teachers/fixed-slots/teachers/{teacher_id}/weekly-schedule",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert isinstance(data["data"], dict)
        
        # 验证一周7天的数据
        weekly_data = data["data"]
        weekdays = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
        for weekday in weekdays:
            assert weekday in weekly_data
            assert isinstance(weekly_data[weekday], list)


class TestTeacherFixedSlotAvailabilityAPI:
    """教师固定时间段可用性管理API测试"""

    def test_batch_update_availability_success(self, client: TestClient, admin_token, created_multiple_fixed_slots):
        """测试批量更新时间段可用性"""
        slot_ids = [slot["id"] for slot in created_multiple_fixed_slots[:2]]

        update_data = {
            "slot_ids": slot_ids,
            "is_available": False,
            "is_visible_to_members": True
        }

        response = client.post(
            "/api/v1/teachers/fixed-slots/availability/batch-update",
            json=update_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data

        result = data["data"]
        assert result["total_requested"] == 2
        assert result["updated_count"] == 2
        assert result["failed_count"] == 0
        assert len(result["updated_slot_ids"]) == 2

    def test_teacher_batch_update_availability_success(self, client: TestClient, admin_token, created_multiple_fixed_slots):
        """测试按教师批量更新可用性"""
        teacher_id = created_multiple_fixed_slots[0]["teacher_id"]

        update_data = {
            "teacher_id": teacher_id,
            "weekdays": [1, 2],  # 星期一、二
            "is_available": False
        }

        response = client.post(
            "/api/v1/teachers/fixed-slots/availability/teacher-batch-update",
            json=update_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data

        result = data["data"]
        assert result["total_found"] >= 0
        assert result["updated_count"] >= 0
        assert result["failed_count"] >= 0

    def test_toggle_slot_availability_success(self, client: TestClient, admin_token, created_fixed_slot):
        """测试切换时间段可用性"""
        original_availability = created_fixed_slot["is_available"]

        response = client.post(
            f"/api/v1/teachers/fixed-slots/{created_fixed_slot['id']}/toggle-availability",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["data"]["id"] == created_fixed_slot["id"]
        assert data["data"]["is_available"] != original_availability

    def test_toggle_slot_visibility_success(self, client: TestClient, admin_token, created_fixed_slot):
        """测试切换时间段可见性"""
        original_visibility = created_fixed_slot["is_visible_to_members"]

        response = client.post(
            f"/api/v1/teachers/fixed-slots/{created_fixed_slot['id']}/toggle-visibility",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert data["data"]["id"] == created_fixed_slot["id"]
        assert data["data"]["is_visible_to_members"] != original_visibility

    def test_get_availability_statistics_success(self, client: TestClient, admin_token, created_multiple_fixed_slots):
        """测试获取可用性统计"""
        teacher_id = created_multiple_fixed_slots[0]["teacher_id"]

        response = client.get(
            "/api/v1/teachers/fixed-slots/availability/statistics",
            params={
                "teacher_id": teacher_id,
                "include_weekday_breakdown": True
            },
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data

        stats = data["data"]
        assert "overall" in stats
        assert "weekday_breakdown" in stats

        overall = stats["overall"]
        assert "total_slots" in overall
        assert "available_slots" in overall
        assert "visible_slots" in overall


class TestTeacherFixedSlotBatchAPI:
    """教师固定时间段批量操作API测试"""

    def test_batch_create_slots_success(self, client: TestClient, admin_token, created_teacher):
        """测试批量创建时间段"""
        batch_data = {
            "teacher_id": created_teacher["id"],
            "slots": [
                {
                    "weekday": 6,  # 星期六
                    "start_time": "09:00",
                    "duration_minutes": 25,
                    "is_available": True,
                    "is_visible_to_members": True
                },
                {
                    "weekday": 6,  # 星期六
                    "start_time": "10:00",
                    "duration_minutes": 25,
                    "is_available": True,
                    "is_visible_to_members": True
                }
            ],
            "created_by": created_teacher["created_by"]
        }

        response = client.post(
            "/api/v1/teachers/fixed-slots/batch/create",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data

        result = data["data"]
        assert result["total_requested"] == 2
        assert result["valid_slots"] == 2
        assert result["created_count"] == 2
        assert result["failed_count"] == 0
        assert len(result["created_slot_ids"]) == 2

    def test_batch_update_slots_success(self, client: TestClient, admin_token, created_multiple_fixed_slots):
        """测试批量更新时间段"""
        updates = [
            {
                "id": created_multiple_fixed_slots[0]["id"],
                "is_available": False
            },
            {
                "id": created_multiple_fixed_slots[1]["id"],
                "duration_minutes": 30
            }
        ]

        batch_data = {"updates": updates}

        response = client.post(
            "/api/v1/teachers/fixed-slots/batch/update",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data

        result = data["data"]
        assert result["total_requested"] == 2
        assert result["valid_updates"] == 2
        assert result["updated_count"] == 2
        assert result["failed_count"] == 0

    def test_batch_delete_slots_success(self, client: TestClient, admin_token, created_multiple_fixed_slots):
        """测试批量删除时间段"""
        slot_ids = [slot["id"] for slot in created_multiple_fixed_slots[:2]]

        batch_data = {"slot_ids": slot_ids}

        response = client.post(
            "/api/v1/teachers/fixed-slots/batch/delete",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data

        result = data["data"]
        assert result["total_requested"] == 2
        assert result["deleted_count"] == 2
        assert result["failed_count"] == 0

    def test_copy_teacher_slots_success(self, client: TestClient, admin_token, created_teacher, created_teacher_2, created_multiple_fixed_slots):
        """测试复制教师时间段"""
        source_teacher_id = created_multiple_fixed_slots[0]["teacher_id"]
        target_teacher_id = created_teacher_2["id"]

        copy_data = {
            "source_teacher_id": source_teacher_id,
            "target_teacher_id": target_teacher_id,
            "weekdays": [1, 2],  # 只复制星期一、二
            "overwrite_existing": False,
            "created_by": created_teacher["created_by"]
        }

        response = client.post(
            "/api/v1/teachers/fixed-slots/batch/copy",
            json=copy_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data

        result = data["data"]
        assert result["source_teacher_id"] == source_teacher_id
        assert result["target_teacher_id"] == target_teacher_id
        assert result["copied_count"] >= 0
        assert isinstance(result["copied_slot_ids"], list)

    def test_clear_teacher_slots_success(self, client: TestClient, admin_token, created_multiple_fixed_slots):
        """测试清空教师时间段"""
        teacher_id = created_multiple_fixed_slots[0]["teacher_id"]

        clear_data = {
            "teacher_id": teacher_id,
            "weekdays": [1],  # 只清空星期一
            "confirm": True
        }

        response = client.post(
            "/api/v1/teachers/fixed-slots/batch/clear",
            json=clear_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data

        result = data["data"]
        assert result["teacher_id"] == teacher_id
        assert result["deleted_count"] >= 0
        assert result["weekdays_cleared"] == [1]


class TestTeacherFixedSlotAPIAuthentication:
    """教师固定时间段API认证测试"""

    def test_create_slot_without_token_should_fail(self, client: TestClient, created_teacher):
        """测试未认证创建时间段应该失败"""
        slot_data = {
            "teacher_id": created_teacher["id"],
            "weekday": 1,
            "start_time": "08:30",
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": True,
            "created_by": created_teacher["created_by"]
        }

        response = client.post("/api/v1/teachers/fixed-slots/", json=slot_data)

        assert response.status_code == 401

    def test_get_slots_without_token_should_fail(self, client: TestClient):
        """测试未认证获取时间段列表应该失败"""
        response = client.get("/api/v1/teachers/fixed-slots/")

        assert response.status_code == 401

    def test_batch_operations_without_token_should_fail(self, client: TestClient):
        """测试未认证批量操作应该失败"""
        batch_data = {"slot_ids": [1, 2, 3]}

        response = client.post("/api/v1/teachers/fixed-slots/batch/delete", json=batch_data)

        assert response.status_code == 401


class TestTeacherFixedSlotAPIValidation:
    """教师固定时间段API数据验证测试"""

    def test_create_slot_with_invalid_time_should_fail(self, client: TestClient, admin_token, created_teacher):
        """测试使用无效时间创建时间段应该失败"""
        invalid_data = {
            "teacher_id": created_teacher["id"],
            "weekday": 1,
            "start_time": "08:33",  # 不是5分钟间隔
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": True,
            "created_by": created_teacher["created_by"]
        }

        response = client.post(
            "/api/v1/teachers/fixed-slots/",
            json=invalid_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 422
        data = response.json()
        assert data["success"] is False
        assert data["business_code"] == GlobalErrorCode.VALIDATION_ERROR.value

    def test_create_slot_with_invalid_duration_should_fail(self, client: TestClient, admin_token, created_teacher):
        """测试使用无效时长创建时间段应该失败"""
        invalid_data = {
            "teacher_id": created_teacher["id"],
            "weekday": 1,
            "start_time": "08:30",
            "duration_minutes": 200,  # 超出范围
            "is_available": True,
            "is_visible_to_members": True,
            "created_by": created_teacher["created_by"]
        }

        response = client.post(
            "/api/v1/teachers/fixed-slots/",
            json=invalid_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 422
        data = response.json()
        assert data["success"] is False
        assert data["business_code"] == GlobalErrorCode.VALIDATION_ERROR.value
