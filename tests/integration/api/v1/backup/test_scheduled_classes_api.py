"""
已排课表API集成测试
"""
import pytest
from fastapi.testclient import TestClient
from datetime import datetime, date, time, timedelta
from typing import Dict, Any

from app.features.courses.scheduled_classes_models import ClassStatus, ClassType


class TestScheduledClassAPI:
    """已排课表API集成测试"""

    # ==================== 课程创建测试 ====================

    def test_create_temp_class_success(self, client: TestClient, admin_token: str, created_admin_user: Dict[str, Any], created_teacher: Dict[str, Any], created_member: Dict[str, Any]):
        """测试管理员创建临时课程成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # 准备测试数据
        class_datetime = datetime.now() + timedelta(days=1)
        class_data = {
            "teacher_id": created_teacher["id"],
            "member_id": created_member["id"],
            "class_datetime": class_datetime.isoformat(),
            "duration_minutes": 25,
            "class_type": ClassType.DIRECT.value,
            "price": 100,
            "material_name": "测试教材",
            "is_visible_to_member": True,
            "operator_name": "管理员"
        }
        
        response = client.post(
            "/api/v1/courses/classes/temp/create",
            json=class_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["teacher_id"] == class_data["teacher_id"]
        assert data["data"]["member_id"] == class_data["member_id"]
        assert data["data"]["class_type"] == class_data["class_type"]
        assert data["data"]["price"] == class_data["price"]

    def test_create_teacher_class_success(self, client: TestClient, admin_token: str, created_teacher: Dict[str, Any]):
        """测试教师创建课程成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        class_datetime = datetime.now() + timedelta(days=2)
        class_data = {
            "class_datetime": class_datetime.isoformat(),
            "duration_minutes": 25,
            "price": 80,
            "material_name": "教师开放课程",
            "is_visible_to_member": True
        }
        
        teacher_id = created_teacher["id"]
        response = client.post(
            f"/api/v1/courses/classes/teacher/create?teacher_id={teacher_id}",
            json=class_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["teacher_id"] == teacher_id
        assert data["data"]["member_id"] is None  # 教师开放的课程初始无会员
        assert data["data"]["status"] == ClassStatus.AVAILABLE.value

    def test_batch_create_teacher_classes_success(self, client: TestClient, admin_token: str, created_teacher: Dict[str, Any]):
        """测试教师批量创建课程成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # 准备多个时间点，确保都在营业时间内（6:00-24:00）
        base_time = datetime.now() + timedelta(days=3)
        # 设置为上午10点开始，避免营业时间限制
        base_time = base_time.replace(hour=10, minute=0, second=0, microsecond=0)
        class_datetimes = [
            (base_time + timedelta(hours=i)).isoformat()
            for i in range(3)  # 10:00, 11:00, 12:00
        ]
        
        teacher_id = created_teacher["id"]
        request_data = {
            "teacher_id": teacher_id,
            "class_datetimes": class_datetimes,
            "duration_minutes": 25,
            "price": 90,
            "material_name": "批量创建课程",
            "is_visible_to_member": True
        }
        
        response = client.post(
            "/api/v1/courses/classes/batch/teacher/create",
            json=request_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        print("test_batch_create_teacher_classes_success:", data)
        assert data["success"] is True
        assert len(data["data"]) == 3
        for class_item in data["data"]:
            assert class_item["teacher_id"] == teacher_id
            assert class_item["status"] == ClassStatus.AVAILABLE.value

    def test_create_temp_class_without_auth(self, client: TestClient):
        """测试未认证创建临时课程"""
        class_data = {
            "teacher_id": 1,
            "class_datetime": datetime.now().isoformat(),
            "duration_minutes": 25
        }
        
        response = client.post(
            "/api/v1/courses/classes/temp/create",
            json=class_data
        )
        
        assert response.status_code == 401

    def test_create_temp_class_invalid_data(self, client: TestClient, admin_token: str, created_teacher: Dict[str, Any]):
        """测试创建临时课程时传入无效数据"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # 缺少必要字段
        invalid_data = {
            "teacher_id": created_teacher["id"]
            # 缺少 class_datetime 等必要字段
        }
        
        response = client.post(
            "/api/v1/courses/classes/temp/create",
            json=invalid_data,
            headers=headers
        )
        
        assert response.status_code == 422  # 数据验证失败

    # ==================== 课程查询测试 ====================

    def test_search_classes_success(self, client: TestClient, admin_token: str, sample_scheduled_class: Dict[str, Any]):
        """测试搜索课程成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get(
            "/api/v1/courses/classes/search?page=1&size=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data

    def test_search_classes_with_filters(self, client: TestClient, admin_token: str, sample_scheduled_class: Dict[str, Any]):
        """测试带筛选条件的课程搜索"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # 按教师ID筛选
        response = client.get(
            f"/api/v1/courses/classes/search?teacher_id={sample_scheduled_class['teacher_id']}&page=1&size=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        # 验证返回的课程都属于指定教师
        for class_item in data["data"]:
            assert class_item["teacher_id"] == sample_scheduled_class["teacher_id"]

    def test_get_available_classes_success(self, client: TestClient, admin_token: str):
        """测试获取可预约课程成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get(
            "/api/v1/courses/classes/available?page=1&size=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        # 验证返回的都是可预约状态的课程
        for class_item in data["data"]:
            assert class_item["status"] == ClassStatus.AVAILABLE.value
            assert class_item["is_visible_to_member"] is True

    def test_get_teacher_classes_success(self, client: TestClient, admin_token: str, sample_scheduled_class: Dict[str, Any]):
        """测试获取教师课程成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        teacher_id = sample_scheduled_class["teacher_id"]
        
        response = client.get(
            f"/api/v1/courses/classes/teacher/{teacher_id}",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        # 验证返回的课程都属于指定教师
        for class_item in data["data"]:
            assert class_item["teacher_id"] == teacher_id

    def test_get_member_classes_success(self, client: TestClient, admin_token: str, sample_booked_class: Dict[str, Any]):
        """测试获取会员课程成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        member_id = sample_booked_class["member_id"]
        
        response = client.get(
            f"/api/v1/courses/classes/member/{member_id}",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        # 验证返回的课程都属于指定会员
        for class_item in data["data"]:
            assert class_item["member_id"] == member_id

    def test_get_class_detail_success(self, client: TestClient, admin_token: str, sample_scheduled_class: Dict[str, Any]):
        """测试获取课程详情成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        class_id = sample_scheduled_class["id"]
        
        response = client.get(
            f"/api/v1/courses/classes/{class_id}",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == class_id

    def test_get_class_detail_not_found(self, client: TestClient, admin_token: str):
        """测试获取不存在的课程详情"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get(
            "/api/v1/courses/classes/99999",
            headers=headers
        )
        
        assert response.status_code == 404

    # ==================== 课程预约测试 ====================

    def test_book_class_success(self, client: TestClient, admin_token: str, sample_available_class: Dict[str, Any], created_member: Dict[str, Any]):
        """测试预约课程成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        class_id = sample_available_class["id"]
        member_id = created_member["id"]
        
        response = client.post(
            f"/api/v1/courses/classes/book/{class_id}?member_id={member_id}",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["member_id"] == member_id
        assert data["data"]["status"] == ClassStatus.BOOKED.value

    def test_book_class_with_booking_data(self, client: TestClient, admin_token: str, sample_available_class: Dict[str, Any], created_member: Dict[str, Any]):
        """测试带预约数据的课程预约"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        class_id = sample_available_class["id"]
        member_id = created_member["id"]
        
        booking_data = {
            "member_card_id": 123,
            "member_card_name": "测试会员卡",
            "booking_remark": "测试预约备注"
        }
        
        response = client.post(
            f"/api/v1/courses/classes/book/{class_id}?member_id={member_id}",
            json=booking_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["member_card_id"] == booking_data["member_card_id"]
        assert data["data"]["booking_remark"] == booking_data["booking_remark"]

    def test_book_class_not_found(self, client: TestClient, admin_token: str, created_member: Dict[str, Any]):
        """测试预约不存在的课程"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        member_id = created_member["id"]
        response = client.post(
            f"/api/v1/courses/classes/book/99999?member_id={member_id}",
            headers=headers
        )
        
        assert response.status_code == 404

    def test_book_class_already_booked(self, client: TestClient, admin_token: str, sample_booked_class: Dict[str, Any]):
        """测试预约已被预约的课程"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        class_id = sample_booked_class["id"]
        
        response = client.post(
            f"/api/v1/courses/classes/book/{class_id}?member_id=2",
            headers=headers
        )
        
        assert response.status_code == 400  # 业务错误

    def test_cancel_booking_success(self, client: TestClient, admin_token: str, sample_booked_class: Dict[str, Any]):
        """测试取消课程预约成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        class_id = sample_booked_class["id"]
        
        response = client.post(
            f"/api/v1/courses/classes/cancel/{class_id}",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["member_id"] is None
        assert data["data"]["status"] == ClassStatus.AVAILABLE.value

    def test_cancel_booking_with_reason(self, client: TestClient, admin_token: str, sample_booked_class: Dict[str, Any]):
        """测试带取消原因的课程预约取消"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        class_id = sample_booked_class["id"]
        
        cancellation_data = {
            "cancellation_reason": "时间冲突，需要取消"
        }
        
        response = client.post(
            f"/api/v1/courses/classes/cancel/{class_id}",
            json=cancellation_data,
            headers=headers
        )
        
        print("test_cancel_booking_with_reason:", response.json())
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["booking_remark"] == cancellation_data["cancellation_reason"]

    def test_batch_book_classes_success(self, client: TestClient, admin_token: str, multiple_available_classes: list, created_member: Dict[str, Any]):
        """测试批量预约课程成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        member_id = created_member["id"]
        bookings = [
            {"class_id": cls["id"], "member_id": member_id}
            for cls in multiple_available_classes[:2]  # 预约前两个课程
        ]
        
        response = client.post(
            "/api/v1/courses/classes/batch/book",
            json=bookings,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        
        # 验证返回结构
        result = data["data"]
        assert result["success_count"] == 2
        assert result["failed_count"] == 0
        assert len(result["booked_classes"]) == 2
        assert len(result["failed_bookings"]) == 0
        
        # 验证预约的课程状态
        for class_item in result["booked_classes"]:
            assert class_item["member_id"] == member_id
            assert class_item["status"] == ClassStatus.BOOKED.value

    def test_batch_cancel_bookings_success(self, client: TestClient, admin_token: str, multiple_booked_classes: list):
        """测试批量取消课程预约成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        class_ids = [cls["id"] for cls in multiple_booked_classes[:2]]
        
        response = client.post(
            "/api/v1/courses/classes/batch/cancel",
            json=class_ids,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        print("test_batch_cancel_bookings_success:", data)
        assert data["success"] is True
        
        # 验证返回结构
        result = data["data"]
        assert result["success_count"] == 2
        assert result["failed_count"] == 0
        assert len(result["cancelled_classes"]) == 2
        assert len(result["failed_cancellations"]) == 0
        
        # 验证取消的课程状态
        for class_item in result["cancelled_classes"]:
            assert class_item["member_id"] is None
            assert class_item["status"] == ClassStatus.AVAILABLE.value

    def test_batch_cancel_bookings_with_failures(self, client: TestClient, admin_token: str, multiple_booked_classes: list):
        """测试批量取消课程预约 - 包含失败情况"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # 包含有效和无效的课程ID
        valid_class_ids = [cls["id"] for cls in multiple_booked_classes[:1]]  # 只取第一个
        invalid_class_ids = [99999, 99998]  # 不存在的课程ID
        all_class_ids = valid_class_ids + invalid_class_ids
        
        response = client.post(
            "/api/v1/courses/classes/batch/cancel",
            json=all_class_ids,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        
        # 验证结果结构
        result = data["data"]
        assert "success_count" in result
        assert "failed_count" in result
        assert "cancelled_classes" in result
        assert "failed_cancellations" in result
        
        # 验证部分成功，部分失败
        assert result["success_count"] == 1  # 1个成功
        assert result["failed_count"] == 2   # 2个失败
        assert len(result["cancelled_classes"]) == 1
        assert len(result["failed_cancellations"]) == 2
        
        # 验证失败记录包含错误信息
        for failed_item in result["failed_cancellations"]:
            assert "class_id" in failed_item
            assert "error" in failed_item
            assert failed_item["error"] != ""  # 错误信息不为空
            assert "不存在" in failed_item["error"]  # 包含具体的错误描述

    # ==================== 课程状态更新测试 ====================

    def test_update_class_status_success(self, client: TestClient, admin_token: str, sample_booked_class: Dict[str, Any]):
        """测试更新课程状态成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        class_id = sample_booked_class["id"]
        
        status_data = {
            "status": ClassStatus.TEACHER_NO_SHOW.value,
            "remark": "课程已完成"
        }
        
        response = client.post(
            f"/api/v1/courses/classes/status/{class_id}",
            json=status_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["status"] == ClassStatus.TEACHER_NO_SHOW.value

    def test_batch_update_status_success(self, client: TestClient, admin_token: str, multiple_booked_classes: list):
        """测试批量更新课程状态成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        class_ids = [cls["id"] for cls in multiple_booked_classes[:2]]
        request_data = {
            "class_ids": class_ids,
            "new_status": ClassStatus.TEACHER_NO_SHOW.value
        }
        
        response = client.post(
            "/api/v1/courses/classes/batch/status",
            json=request_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["data"]) == 2
        for class_item in data["data"]:
            assert class_item["status"] == ClassStatus.TEACHER_NO_SHOW.value

    def test_batch_delete_classes_success(self, client: TestClient, admin_token: str, multiple_available_classes: list):
        """测试批量删除课程成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        class_ids = [cls["id"] for cls in multiple_available_classes[:2]]
        
        response = client.post(
            "/api/v1/courses/classes/batch/delete",
            json=class_ids,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "批量删除" in data["message"]

    # ==================== 时间冲突检查测试 ====================

    def test_check_time_conflict_success(self, client: TestClient, admin_token: str, created_teacher: Dict[str, Any], created_member: Dict[str, Any]):
        """测试时间冲突检查成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        conflict_request = {
            "teacher_id": created_teacher["id"],
            "member_id": created_member["id"],
            "class_datetime": (datetime.now() + timedelta(days=1)).isoformat(),
            "duration_minutes": 25
        }
        
        response = client.post(
            "/api/v1/courses/classes/check-conflict",
            json=conflict_request,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "has_conflict" in data["data"]
        assert "teacher_conflict" in data["data"]
        assert "member_conflict" in data["data"]

    # ==================== 课程统计测试 ====================

    def test_get_class_statistics_success(self, client: TestClient, admin_token: str):
        """测试获取课程统计成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get(
            "/api/v1/courses/classes/stats/count",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert isinstance(data["data"], dict)

    def test_get_class_statistics_with_filters(self, client: TestClient, admin_token: str, created_teacher: Dict[str, Any]):
        """测试带筛选条件的课程统计"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        today = date.today()
        teacher_id = created_teacher["id"]
        response = client.get(
            f"/api/v1/courses/classes/stats/count?teacher_id={teacher_id}&date_from={today}&date_to={today}",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert isinstance(data["data"], dict)

    # ==================== 权限和错误处理测试 ====================

    def test_search_classes_without_auth(self, client: TestClient):
        """测试未认证访问搜索接口"""
        response = client.get("/api/v1/courses/classes/search")
        assert response.status_code == 401

    def test_invalid_pagination_parameters(self, client: TestClient, admin_token: str):
        """测试无效的分页参数"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # 页码为0（无效）
        response = client.get(
            "/api/v1/courses/classes/search?page=0&size=10",
            headers=headers
        )
        assert response.status_code == 422

        # 每页数量超过限制
        response = client.get(
            "/api/v1/courses/classes/search?page=1&size=200",
            headers=headers
        )
        assert response.status_code == 422

    def test_batch_book_invalid_data(self, client: TestClient, admin_token: str, created_member: Dict[str, Any]):
        """测试批量预约时传入无效数据"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        member_id = created_member["id"]
        # 缺少必要字段的预约数据
        invalid_bookings = [
            {"class_id": 1},  # 缺少 member_id
            {"member_id": member_id}  # 缺少 class_id
        ]
        
        response = client.post(
            "/api/v1/courses/classes/batch/book",
            json=invalid_bookings,
            headers=headers
        )
        
        assert response.status_code == 422  # 验证错误

    def test_batch_create_invalid_datetime_format(self, client: TestClient, admin_token: str, created_teacher: Dict[str, Any]):
        """测试批量创建时传入无效的时间格式"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        request_data = {
            "teacher_id": created_teacher["id"],
            "class_datetimes": ["invalid-datetime-format"],
            "duration_minutes": 25
        }
        
        response = client.post(
            "/api/v1/courses/classes/batch/teacher/create",
            json=request_data,
            headers=headers
        )
        
        assert response.status_code == 422  # 验证错误 