"""
课程系统配置API集成测试
"""
import pytest
from fastapi.testclient import TestClient
from sqlmodel import Session
from sqlalchemy import text

from app.features.courses.config_schemas import CourseSystemConfigUpdate


class TestCourseConfigAPI:
    """课程系统配置API集成测试"""

    def test_get_course_config_success(self, client: TestClient, admin_token, created_admin_user):
        """测试获取课程配置成功（如果不存在会自动创建默认配置）"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get("/api/v1/courses/config", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "default_slot_duration_minutes" in data["data"]
        assert "tenant_id" in data["data"]
        assert data["data"]["tenant_id"] == created_admin_user["tenant_id"]

    def test_get_course_config_without_auth(self, client: TestClient):
        """测试未认证访问课程配置"""
        response = client.get("/api/v1/courses/config")
        assert response.status_code == 401

    def test_update_course_config_success(self, client: TestClient, admin_token, created_admin_user, default_course_config):
        """测试更新课程配置成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        update_data = {
            "default_slot_duration_minutes": 45,
            "max_advance_days": 20,
            "booking_deadline_hours": 4,
            "teacher_can_add_slots": False,
            "auto_schedule_enabled": False
        }
        
        response = client.post(
            "/api/v1/courses/config/update",
            json=update_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["default_slot_duration_minutes"] == 45
        assert data["data"]["max_advance_days"] == 20
        assert data["data"]["booking_deadline_hours"] == 4
        assert data["data"]["teacher_can_add_slots"] is False
        assert data["data"]["auto_schedule_enabled"] is False

    def test_update_course_config_invalid_data(self, client: TestClient, admin_token, default_course_config):
        """测试更新课程配置时传入无效数据"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # 测试无效的时长值
        update_data = {
            "default_slot_duration_minutes": 200  # 超过最大值
        }
        
        response = client.post(
            "/api/v1/courses/config/update",
            json=update_data,
            headers=headers
        )
        
        assert response.status_code == 422  # 数据验证失败

    def test_reset_course_config_success(self, client: TestClient, admin_token, default_course_config):
        """测试重置课程配置成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # 先更新配置
        update_data = {
            "default_slot_duration_minutes": 90,
            "auto_schedule_enabled": False
        }
        
        response = client.post(
            "/api/v1/courses/config/update",
            json=update_data,
            headers=headers
        )
        assert response.status_code == 200
        
        # 重置配置
        response = client.post("/api/v1/courses/config/reset", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        # 验证重置为默认值
        assert data["data"]["default_slot_duration_minutes"] == 25  # 默认值
        assert data["data"]["auto_schedule_enabled"] is True  # 默认值

    def test_update_config_field_success(self, client: TestClient, admin_token, default_course_config):
        """测试更新单个配置字段成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.post(
            "/api/v1/courses/config/field",
            json={
                "field_name": "default_slot_duration_minutes",
                "field_value": 75  # 传递整数而不是字符串
            },
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["default_slot_duration_minutes"] == 75

    def test_update_config_field_invalid_field(self, client: TestClient, admin_token, default_course_config):
        """测试更新无效的配置字段"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.post(
            "/api/v1/courses/config/field",
            json={
                "field_name": "invalid_field_name",
                "field_value": "123"
            },
            headers=headers
        )
        
        assert response.status_code == 400  # 业务错误

    def test_get_config_field_success(self, client: TestClient, admin_token):
        """测试获取单个配置字段成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get(
            "/api/v1/courses/config/field/default_slot_duration_minutes",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["field_name"] == "default_slot_duration_minutes"
        assert "field_value" in data["data"]

    def test_get_config_field_invalid_field(self, client: TestClient, admin_token):
        """测试获取无效的配置字段"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get(
            "/api/v1/courses/config/field/invalid_field",
            headers=headers
        )
        
        assert response.status_code == 400  # 业务错误

    def test_get_booking_time_range_success(self, client: TestClient, admin_token):
        """测试获取预约时间范围成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get("/api/v1/courses/config/booking-time-range", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "max_advance_days" in data["data"]
        assert "booking_deadline_hours" in data["data"]
        assert "cancel_deadline_hours" in data["data"]

    def test_get_teacher_permissions_success(self, client: TestClient, admin_token):
        """测试获取教师权限配置成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get("/api/v1/courses/config/teacher-permissions", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "teacher_can_add_slots" in data["data"]
        assert "teacher_can_delete_empty_slots" in data["data"]

    def test_get_schedule_config_success(self, client: TestClient, admin_token):
        """测试获取排课配置成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get("/api/v1/courses/config/schedule-config", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "auto_schedule_enabled" in data["data"]
        assert "auto_schedule_day" in data["data"]
        assert "auto_schedule_time" in data["data"]
        assert "default_schedule_weeks" in data["data"]

    def test_validate_config_consistency_success(self, client: TestClient, admin_token):
        """测试验证配置一致性成功"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.post("/api/v1/courses/config/validate-consistency", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "is_valid" in data["data"]
        assert isinstance(data["data"]["is_valid"], bool)

    def test_multi_tenant_isolation(self, client: TestClient, admin_token, second_tenant_admin_token, default_course_config):
        """测试多租户数据隔离"""
        admin_headers = {"Authorization": f"Bearer {admin_token}"}
        second_tenant_headers = {"Authorization": f"Bearer {second_tenant_admin_token}"}
        
        # 第一个租户更新配置
        update_data = {
            "default_slot_duration_minutes": 35,
            "max_advance_days": 25
        }
        
        response = client.post(
            "/api/v1/courses/config/update",
            json=update_data,
            headers=admin_headers
        )
        assert response.status_code == 200
        
        # 第二个租户获取配置，应该是默认值
        response = client.get("/api/v1/courses/config", headers=second_tenant_headers)
        assert response.status_code == 200
        data = response.json()
        # 第二个租户应该看到默认值，而不是第一个租户的配置
        assert data["data"]["default_slot_duration_minutes"] == 25  # 默认值
        assert data["data"]["max_advance_days"] == 30  # 默认值

    def test_config_update_with_time_validation(self, client: TestClient, admin_token, default_course_config):
        """测试时间字段验证"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # 测试有效的时间更新
        update_data = {
            "booking_time_from": "08:00:00",
            "booking_time_to": "22:00:00",
            "auto_schedule_time": "07:00:00"
        }
        
        response = client.post(
            "/api/v1/courses/config/update",
            json=update_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "08:00:00" in data["data"]["booking_time_from"]
        assert "22:00:00" in data["data"]["booking_time_to"]

    def test_config_update_with_boolean_fields(self, client: TestClient, admin_token, default_course_config):
        """测试布尔字段的更新"""
        headers = {"Authorization": f"Bearer {admin_token}"}

        update_data = {
            "direct_booking_enabled": False,
            "fixed_booking_enabled": True,
            "teacher_can_add_slots": False,
            "teacher_can_delete_empty_slots": True
        }

        response = client.post(
            "/api/v1/courses/config/update",
            json=update_data,
            headers=headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["direct_booking_enabled"] is False
        assert data["data"]["fixed_booking_enabled"] is True
        assert data["data"]["teacher_can_add_slots"] is False
        assert data["data"]["teacher_can_delete_empty_slots"] is True

    def test_config_partial_update(self, client: TestClient, admin_token):
        """测试部分字段更新"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # 先获取当前配置
        response = client.get("/api/v1/courses/config", headers=headers)
        original_config = response.json()["data"]
        
        # 只更新一个字段
        update_data = {
            "max_advance_days": 21
        }
        
        response = client.post(
            "/api/v1/courses/config/update",
            json=update_data,
            headers=headers
        )
        
        assert response.status_code == 200
        updated_config = response.json()["data"]
        
        # 验证只有指定字段被更新
        assert updated_config["max_advance_days"] == 21
        # 其他字段保持不变
        assert updated_config["default_slot_duration_minutes"] == original_config["default_slot_duration_minutes"]
        assert updated_config["auto_schedule_enabled"] == original_config["auto_schedule_enabled"] 