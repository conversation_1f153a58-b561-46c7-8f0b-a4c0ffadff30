import pytest
from fastapi.testclient import TestClient
from app.api.common.exceptions import GlobalErrorCode


class TestMemberAPI:
    """会员管理API集成测试"""
    
    def test_create_member_success(self, client: TestClient, admin_token):
        """测试管理员成功创建会员"""
        member_data = {
            "name": "测试会员",
            "phone": "13800138001",
            "email": "<EMAIL>",
            "gender": "male",
            "birthday": "1990-01-01",
            "member_type": "trial"
        }
        
        response = client.post(
            "/api/v1/members/",
            json=member_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 201
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "会员创建成功"
        assert "data" in data
        
        member_data_resp = data["data"]
        assert member_data_resp["name"] == member_data["name"]
        assert member_data_resp["phone"] == member_data["phone"]
        assert member_data_resp["email"] == member_data["email"]
        assert member_data_resp["member_type"] == member_data["member_type"]
        assert "id" in member_data_resp
    
    def test_create_member_unauthorized(self, client: TestClient):
        """测试未认证用户无法创建会员"""
        member_data = {
            "name": "测试会员",
            "phone": "13800138002",
            "email": "<EMAIL>",
            "gender": "female",
            "birthday": "1995-01-01",
            "member_type": "trial"
        }
        
        response = client.post("/api/v1/members/", json=member_data)
        assert response.status_code == 401
    
    def test_create_member_duplicate_phone(self, client: TestClient, admin_token):
        """测试创建重复手机号会员失败"""
        member_data = {
            "name": "测试会员",
            "phone": "13800138003",
            "email": "<EMAIL>",
            "gender": "male",
            "birthday": "1990-01-01",
            "member_type": "trial"
        }
        
        # 先创建一个会员
        client.post(
            "/api/v1/members/", 
            json=member_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        # 尝试创建相同手机号的会员
        response = client.post(
            "/api/v1/members/", 
            json=member_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "手机号" in data["message"]
    
    def test_create_member_invalid_data(self, client: TestClient, admin_token):
        """测试创建会员时数据验证"""
        invalid_data = {
            "name": "",  # 空名称
            "phone": "invalid-phone",  # 无效手机号
            "email": "invalid-email"  # 无效邮箱
        }
        
        response = client.post(
            "/api/v1/members/", 
            json=invalid_data, 
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 422  # 验证错误
    
    def test_get_members_success(self, client: TestClient, admin_token):
        """测试管理员获取会员列表"""
        response = client.get(
            "/api/v1/members/",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "获取会员列表成功"
        assert "data" in data
        assert "total" in data
        assert isinstance(data["data"], list)
        assert isinstance(data["total"], int)
    
    def test_get_members_with_filters(self, client: TestClient, admin_token):
        """测试带筛选条件的会员列表"""
        response = client.get(
            "/api/v1/members/",
            params={"member_type": "trial", "skip": 0, "limit": 10},
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert isinstance(data["data"], list)
    
    def test_get_members_pagination(self, client: TestClient, admin_token):
        """测试会员列表分页"""
        # 创建5个会员
        for i in range(5):
            member_data = {
                "name": f"测试会员{i}",
                "phone": f"1380013810{i}",
                "email": f"test{i}@example.com",
                "member_type": "trial"
            }
            client.post(
                "/api/v1/members/", 
                json=member_data, 
                headers={"Authorization": f"Bearer {admin_token}"}
            )
        
        # 测试分页
        response = client.get(
            "/api/v1/members/", 
            params={"skip": 0, "limit": 3},
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert len(data["data"]) <= 3  # 可能有其他测试创建的会员
    
    def test_get_members_with_search(self, client: TestClient, admin_token):
        """测试搜索会员"""
        # 创建一些会员
        member_data1 = {
            "name": "张三", 
            "phone": "13800138021", 
            "email": "<EMAIL>",
            "member_type": "trial"
        }
        member_data2 = {
            "name": "李四", 
            "phone": "13900139022", 
            "email": "<EMAIL>",
            "member_type": "trial"
        }
        
        client.post(
            "/api/v1/members/", 
            json=member_data1, 
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        client.post(
            "/api/v1/members/", 
            json=member_data2, 
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        # 按姓名搜索
        response = client.get(
            "/api/v1/members/",
            params={"search_keyword": "张三"},
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        # 验证搜索结果包含张三
        found_zhangsan = any(member["name"] == "张三" for member in data["data"])
        assert found_zhangsan
    
    def test_get_member_by_id_success(self, client: TestClient, admin_token, created_member):
        """测试管理员根据ID获取会员详情"""
        response = client.get(
            f"/api/v1/members/{created_member['id']}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "获取会员详情成功"
        assert data["data"]["id"] == created_member["id"]
        assert data["data"]["name"] == created_member["name"]
    
    def test_get_member_unauthorized(self, client: TestClient, created_member):
        """测试未认证用户无法获取会员详情"""
        response = client.get(f"/api/v1/members/{created_member['id']}")
        assert response.status_code == 401
    
    def test_get_nonexistent_member(self, client: TestClient, admin_token):
        """测试获取不存在的会员"""
        response = client.get(
            "/api/v1/members/99999",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
    
    def test_update_member_success(self, client: TestClient, admin_token, created_member):
        """测试管理员更新会员信息"""
        update_data = {
            "name": "更新后的会员名称",
            "email": "<EMAIL>"
        }
        
        response = client.post(
            f"/api/v1/members/{created_member['id']}/update",
            json=update_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "会员信息更新成功"
        assert data["data"]["name"] == update_data["name"]
        assert data["data"]["email"] == update_data["email"]
        assert data["data"]["id"] == created_member["id"]
    
    def test_update_nonexistent_member(self, client: TestClient, admin_token):
        """测试更新不存在的会员"""
        update_data = {"name": "不存在的会员"}
        
        response = client.post(
            "/api/v1/members/99999/update",
            json=update_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
        assert "会员不存在" in data["message"]
    
    def test_update_member_status_success(self, client: TestClient, admin_token, created_member):
        """测试管理员更新会员状态"""
        response = client.post(
            f"/api/v1/members/{created_member['id']}/update-status",
            params={"status": "frozen"},
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "状态更新成功" in data["message"]
        assert data["data"]["member_status"] == "frozen"
    
    def test_update_member_stats_success(self, client: TestClient, admin_token, created_member):
        """测试管理员更新会员统计信息"""
        response = client.post(
            f"/api/v1/members/{created_member['id']}/update-stats",
            params={
                "class_completed": True,
                "amount_spent": 100.0
            },
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "统计信息更新成功" in data["message"]
        # 使用正确的字段名称，确保将字符串转换为浮点数进行比较
        assert float(data["data"]["completed_classes"]) >= 1
        assert float(data["data"]["total_spent"]) >= 100.0
    
    def test_count_members_success(self, client: TestClient, admin_token):
        """测试管理员统计会员数量"""
        response = client.get(
            "/api/v1/members/count",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()["data"]
        assert isinstance(data, int)
        assert data >= 0
    
    def test_count_members_with_filters(self, client: TestClient, admin_token):
        """测试带筛选条件的会员统计"""
        response = client.get(
            "/api/v1/members/count",
            params={"member_type": "trial"},
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()["data"]
        assert isinstance(data, int)
        assert data >= 0
    
    def test_delete_member_success(self, client: TestClient, admin_token):
        """测试管理员删除会员"""
        # 先创建一个会员
        member_data = {
            "name": "待删除会员",
            "phone": "13800138999",
            "email": "<EMAIL>",
            "gender": "male",
            "birthday": "1990-01-01",
            "member_type": "trial"
        }
        
        create_response = client.post(
            "/api/v1/members/",
            json=member_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        created_member = create_response.json()["data"]
        
        # 删除会员
        response = client.post(
            f"/api/v1/members/{created_member['id']}/delete",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        assert "删除成功" in response.json()["message"]
        
        # 验证会员已被删除
        get_response = client.get(
            f"/api/v1/members/{created_member['id']}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert get_response.status_code == 404
    
    def test_delete_member_not_found(self, client: TestClient, admin_token):
        """测试删除不存在的会员"""
        response = client.post(
            "/api/v1/members/99999/delete",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 404
        assert response.json()["success"] is False
        assert response.json()["business_code"] == GlobalErrorCode.BUSINESS_ERROR.value
        assert "会员不存在" in response.json()["message"] 