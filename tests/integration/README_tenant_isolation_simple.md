# 简单租户隔离测试使用指南

## 概述

`test_tenant_isolation_simple.py` 是一个专门用于快速验证租户隔离功能的测试文件。这些测试确保多租户系统中的数据隔离机制正常工作，防止租户间的数据泄露。

## 测试用例说明

### 1. `test_basic_user_isolation`

**基本用户数据隔离测试**

- 创建两个租户和对应的用户
- 验证每个租户只能看到自己的用户数据
- 确保两个租户的用户数据完全隔离

### 2. `test_member_data_isolation`

**会员数据隔离测试**

- 创建两个租户和对应的会员
- 验证每个租户只能看到自己的会员数据
- 确保会员数据的租户隔离机制正常

### 3. `test_cross_tenant_direct_access_blocked`

**跨租户直接访问阻止测试**

- 验证通过 ID 直接访问其他租户数据被 RLS 阻止
- 确保即使知道其他租户的数据 ID，也无法访问

### 4. `test_super_admin_global_access`

**超级管理员全局访问测试**

- 验证超级管理员能够看到所有租户的数据
- 确保租户用户只能看到自己租户的数据
- 测试超级管理员权限的正确性

### 5. `test_rls_policies_exist`

**RLS 策略存在性检查**

- 检查关键表是否启用了 RLS
- 验证租户隔离策略是否正确创建
- 确保数据库层面的安全策略到位

### 6. `test_empty_tenant_context_behavior`

**空租户上下文行为测试**

- 测试清空租户上下文后的行为（超级管理员模式）
- 验证全局访问和租户访问的区别
- 确保上下文切换的正确性

### 7. `test_member_fixed_slot_locks_isolation`

**会员固定课位锁定隔离测试**

- 测试复杂业务数据的租户隔离
- 验证关联表数据的隔离机制
- 确保业务层面的数据安全

## 快速运行方法

### 方法 1: 使用专用脚本（推荐）

```bash
# 运行所有简单隔离测试，逐个显示结果
python scripts/test_tenant_isolation_simple.py

# 运行所有测试，一次性显示结果
python scripts/test_tenant_isolation_simple.py all
```

### 方法 2: 直接使用 pytest

```bash
# 运行所有简单隔离测试
pytest tests/integration/test_tenant_isolation_simple.py -v -s

# 运行单个测试
pytest tests/integration/test_tenant_isolation_simple.py::TestTenantIsolationSimple::test_basic_user_isolation -v -s

# 运行特定的几个测试
pytest tests/integration/test_tenant_isolation_simple.py -k "user_isolation or member_data" -v -s
```

### 方法 3: 使用现有测试脚本

```bash
# 如果项目有其他测试脚本，可以这样运行
python scripts/test.py integration -k "tenant_isolation_simple"
```

## 预期输出

### 成功时的输出示例

```
🚀 开始运行简单租户隔离测试...
============================================================

📋 [1/7] 运行: 基本用户隔离测试
----------------------------------------
✅ 用户隔离测试通过: 租户1用户数=1, 租户2用户数=1
✅ 基本用户隔离测试 - 通过

📋 [2/7] 运行: 会员数据隔离测试
----------------------------------------
✅ 会员隔离测试通过: 租户1会员数=1, 租户2会员数=1
✅ 会员数据隔离测试 - 通过

📋 [3/7] 运行: 跨租户直接访问阻止测试
----------------------------------------
✅ 跨租户直接访问阻止测试通过: 租户1用户ID=123, 租户2用户ID=456
✅ 跨租户直接访问阻止测试 - 通过

📋 [4/7] 运行: 超级管理员全局访问测试
----------------------------------------
✅ 超级管理员权限测试通过: 全局用户数=2, 租户用户数=1
✅ 超级管理员全局访问测试 - 通过

📋 [5/7] 运行: RLS策略存在性检查
----------------------------------------
✅ users 表RLS策略检查通过: RLS启用=True, 策略数=1
✅ members 表RLS策略检查通过: RLS启用=True, 策略数=1
✅ member_fixed_slot_locks 表RLS策略检查通过: RLS启用=True, 策略数=1
✅ course_system_configs 表RLS策略检查通过: RLS启用=True, 策略数=1
✅ RLS策略存在性检查 - 通过

📋 [6/7] 运行: 空租户上下文行为测试
----------------------------------------
✅ 空租户上下文行为测试通过: 全局用户数=2, 租户用户数=1
✅ 空租户上下文行为测试 - 通过

📋 [7/7] 运行: 会员固定课位锁定隔离测试
----------------------------------------
✅ 会员固定课位锁定隔离测试通过: 租户1锁定数=1, 租户2锁定数=1
✅ 会员固定课位锁定隔离测试 - 通过

============================================================
📊 测试结果汇总
============================================================
总测试数: 7
通过: 7
失败: 0

🎉 所有租户隔离测试都通过了！
✅ 多租户数据隔离机制正常工作。
```

### 失败时的输出示例

```
❌ 基本用户隔离测试 - 失败 (退出码: 1)

============================================================
📊 测试结果汇总
============================================================
总测试数: 7
通过: 6
失败: 1

❌ 失败的测试:
  - 基本用户隔离测试

🚨 租户隔离测试失败！请检查多租户配置。
```

## 故障排除

### 常见问题

1. **RLS 策略不存在**

   ```
   AssertionError: 表 users 的RLS应该被启用
   ```

   **解决方案**: 重新运行数据库初始化脚本，确保 RLS 策略正确创建

2. **数据隔离失效**

   ```
   AssertionError: 租户2不应该看到租户1的用户
   ```

   **解决方案**: 检查 RLS 策略是否正确应用，确认 PostgreSQL 配置

3. **超级管理员权限异常**
   ```
   AssertionError: 超级管理员应该能看到所有数据
   ```
   **解决方案**: 确保超级管理员的 tenant_id 为 NULL，检查 RLS 策略

### 调试方法

1. **查看详细错误信息**

   ```bash
   pytest tests/integration/test_tenant_isolation_simple.py::TestTenantIsolationSimple::test_basic_user_isolation -v -s --tb=long
   ```

2. **手动检查 RLS 状态**

   ```sql
   -- 检查表的RLS状态
   SELECT relname, relrowsecurity FROM pg_class WHERE relname IN ('users', 'members');

   -- 检查RLS策略
   SELECT tablename, policyname FROM pg_policies WHERE tablename = 'users';
   ```

3. **手动测试租户隔离**

   ```sql
   -- 设置租户上下文
   SET app.current_tenant_id = '1';
   SELECT * FROM users;

   -- 切换租户
   SET app.current_tenant_id = '2';
   SELECT * FROM users;
   ```

## 集成到 CI/CD

### GitHub Actions 示例

```yaml
name: 租户隔离测试
on: [push, pull_request]

jobs:
  tenant-isolation:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v2
      - name: 设置Python
        uses: actions/setup-python@v2
        with:
          python-version: "3.9"

      - name: 安装依赖
        run: |
          pip install -r requirements.txt

      - name: 运行租户隔离测试
        run: |
          python scripts/test_tenant_isolation_simple.py
```

### Jenkins Pipeline 示例

```groovy
pipeline {
    agent any
    stages {
        stage('租户隔离测试') {
            steps {
                script {
                    def result = sh(
                        script: 'python scripts/test_tenant_isolation_simple.py',
                        returnStatus: true
                    )
                    if (result != 0) {
                        error('租户隔离测试失败')
                    }
                }
            }
        }
    }
}
```

## 最佳实践

1. **定期运行**: 在每次部署前运行这些测试
2. **CI/CD 集成**: 将测试集成到持续集成流程中
3. **监控告警**: 如果测试失败，立即停止部署
4. **数据备份**: 测试前备份重要数据
5. **环境隔离**: 在测试环境中运行，避免影响生产数据

## 扩展测试

如果需要添加新的租户隔离测试：

1. 在 `test_tenant_isolation_simple.py` 中添加新的测试方法
2. 在 `test_tenant_isolation_simple.py` 脚本中添加对应的测试命令
3. 遵循现有的测试模式和命名约定
4. 确保测试覆盖新的业务表和功能

## 注意事项

- 这些测试会创建临时数据，测试结束后会自动清理
- 测试使用独立的数据库事务，不会影响其他测试
- 每个测试都是独立的，可以单独运行
- 测试数据使用 UUID 确保唯一性，避免冲突
