"""
测试专用日志工具
提供测试期间的日志管理和调试功能
"""
import os
import logging
import pytest
from pathlib import Path
from typing import Optional, Dict, Any
from contextlib import contextmanager

from app.core.logging import setup_logging, get_logger


class TestLogManager:
    """测试日志管理器"""
    
    def __init__(self):
        self.test_log_dir = Path("logs/tests")
        self.test_log_dir.mkdir(parents=True, exist_ok=True)
        self.current_test_logger: Optional[logging.Logger] = None
    
    def setup_test_logging(self, test_name: str = "test"):
        """为测试设置专用日志"""
        
        # 创建测试专用的日志文件
        log_file = self.test_log_dir / f"{test_name}.log"
        
        # 设置日志系统 - 强制重新配置以确保测试配置生效
        setup_logging(
            environment="testing",
            log_level="DEBUG",
            enable_file_logging=True,
            filter_sqlalchemy=True,
            force_reconfigure=True
        )
        
        # 创建测试专用日志器
        test_logger = logging.getLogger(f"test.{test_name}")
        
        # 添加文件处理器
        file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        formatter = logging.Formatter(
            '%(asctime)s | %(name)s | %(levelname)s | %(filename)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        test_logger.addHandler(file_handler)
        
        self.current_test_logger = test_logger
        
        test_logger.info(f"开始测试: {test_name}")
        return test_logger
    
    def log_test_step(self, step: str, details: Optional[Dict[str, Any]] = None):
        """记录测试步骤"""
        if self.current_test_logger:
            msg = f"测试步骤: {step}"
            if details:
                msg += f" | 详情: {details}"
            self.current_test_logger.info(msg)
    
    def log_test_data(self, data_type: str, data: Any):
        """记录测试数据"""
        if self.current_test_logger:
            self.current_test_logger.debug(f"测试数据 [{data_type}]: {data}")
    
    def log_api_call(self, method: str, url: str, data: Optional[Dict] = None, response: Optional[Dict] = None):
        """记录API调用"""
        if self.current_test_logger:
            self.current_test_logger.info(f"API调用: {method} {url}")
            if data:
                self.current_test_logger.debug(f"请求数据: {data}")
            if response:
                self.current_test_logger.debug(f"响应数据: {response}")
    
    def log_assertion(self, assertion: str, result: bool, expected: Any = None, actual: Any = None):
        """记录断言"""
        if self.current_test_logger:
            status = "✅ 通过" if result else "❌ 失败"
            msg = f"断言 {status}: {assertion}"
            if not result and expected is not None and actual is not None:
                msg += f" | 期望: {expected}, 实际: {actual}"
            self.current_test_logger.info(msg)
    
    def log_error(self, error: Exception, context: Optional[str] = None):
        """记录测试错误"""
        if self.current_test_logger:
            msg = f"测试错误: {str(error)}"
            if context:
                msg = f"{context} - {msg}"
            self.current_test_logger.error(msg, exc_info=True)
    
    def finish_test(self, test_name: str, success: bool):
        """完成测试"""
        if self.current_test_logger:
            status = "成功" if success else "失败"
            self.current_test_logger.info(f"测试完成: {test_name} - {status}")


# 全局测试日志管理器
test_log_manager = TestLogManager()


@contextmanager
def test_logging_context(test_name: str):
    """测试日志上下文管理器"""
    logger = test_log_manager.setup_test_logging(test_name)
    try:
        yield logger
    except Exception as e:
        test_log_manager.log_error(e, f"测试 {test_name} 执行过程中")
        raise
    finally:
        test_log_manager.finish_test(test_name, True)


def log_test_info(message: str, **kwargs):
    """记录测试信息"""
    test_log_manager.current_test_logger.info(message, extra=kwargs) if test_log_manager.current_test_logger else None


def log_test_debug(message: str, **kwargs):
    """记录测试调试信息"""
    test_log_manager.current_test_logger.debug(message, extra=kwargs) if test_log_manager.current_test_logger else None


def log_test_error(message: str, **kwargs):
    """记录测试错误"""
    test_log_manager.current_test_logger.error(message, extra=kwargs) if test_log_manager.current_test_logger else None


# Pytest插件钩子
def pytest_runtest_setup(item):
    """测试设置钩子"""
    test_name = f"{item.module.__name__}.{item.cls.__name__ if item.cls else ''}.{item.name}"
    test_log_manager.setup_test_logging(test_name.replace('.', '_'))


def pytest_runtest_call(item):
    """测试调用钩子"""
    test_log_manager.log_test_step("开始执行测试")


def pytest_runtest_teardown(item, nextitem):
    """测试清理钩子"""
    test_log_manager.log_test_step("测试清理完成")


# 装饰器
def log_test_method(func):
    """测试方法日志装饰器"""
    def wrapper(*args, **kwargs):
        test_name = f"{func.__module__}.{func.__qualname__}"
        with test_logging_context(test_name):
            test_log_manager.log_test_step(f"开始执行: {func.__name__}")
            try:
                result = func(*args, **kwargs)
                test_log_manager.log_test_step(f"成功完成: {func.__name__}")
                return result
            except Exception as e:
                test_log_manager.log_error(e, f"执行 {func.__name__} 时")
                raise
    return wrapper


class LoggedTestCase:
    """带日志的测试基类"""
    
    def setup_method(self, method):
        """设置方法级别的日志"""
        test_name = f"{self.__class__.__module__}.{self.__class__.__name__}.{method.__name__}"
        self.logger = test_log_manager.setup_test_logging(test_name)
        self.logger.info(f"开始测试方法: {method.__name__}")
    
    def teardown_method(self, method):
        """清理方法级别的日志"""
        if hasattr(self, 'logger'):
            self.logger.info(f"完成测试方法: {method.__name__}")
    
    def log_step(self, step: str, **kwargs):
        """记录测试步骤"""
        test_log_manager.log_test_step(step, kwargs)
    
    def log_data(self, data_type: str, data: Any):
        """记录测试数据"""
        test_log_manager.log_test_data(data_type, data)
    
    def log_api(self, method: str, url: str, data: Optional[Dict] = None, response: Optional[Dict] = None):
        """记录API调用"""
        test_log_manager.log_api_call(method, url, data, response)
    
    def assert_and_log(self, condition: bool, message: str, expected: Any = None, actual: Any = None):
        """断言并记录"""
        test_log_manager.log_assertion(message, condition, expected, actual)
        assert condition, message


# 便捷函数用于在测试中使用
def setup_test_logging_for_function(test_function):
    """为测试函数设置日志"""
    test_name = f"{test_function.__module__}.{test_function.__qualname__}"
    return test_log_manager.setup_test_logging(test_name)


def get_test_log_file(test_name: str) -> Path:
    """获取测试日志文件路径"""
    return test_log_manager.test_log_dir / f"{test_name}.log"
