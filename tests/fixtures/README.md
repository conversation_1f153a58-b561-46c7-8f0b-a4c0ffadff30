# 测试数据库 Fixtures 使用说明

## 概述

这个模块提供了改进的数据库测试 fixtures，解决了以下问题：

- 数据库名称不一致导致的创建失败
- 缺乏错误处理和日志记录
- 配置不够灵活
- 调试困难

## 主要特性

### 1. 自动数据库管理

- 自动创建和清理测试数据库
- 强制断开现有连接
- 完善的错误处理

### 2. 灵活的配置

支持通过环境变量配置：

```bash
export TEST_DB_NAME="your_test_db"
export TEST_DB_HOST="localhost"
export TEST_DB_PORT="5432"
export TEST_DB_USER="your_user"
export TEST_DB_PASSWORD="your_password"
export TEST_DB_ECHO="true"  # 启用SQL日志
```

### 3. 调试支持

```bash
# 保留测试数据用于调试
pytest --keep-test-data

# 启用RLS调试
pytest --debug-rls

# 启用SQL日志
pytest --test-db-echo
```

## 使用方法

### 基本用法

```python
def test_something(test_session):
    # 使用test_session进行数据库操作
    pass
```

### 租户上下文管理

```python
def test_with_tenant_context(tenant_context_manager):
    with tenant_context_manager as ctx:
        ctx.set_tenant(1)
        # 在租户1的上下文中进行测试
        pass
    # 上下文自动重置
```

### RLS 调试

```python
def test_rls_debugging(test_session, rls_debug_info):
    if rls_debug_info:
        # 获取RLS状态
        info = rls_debug_info["get_rls_info"]("your_table")

        # 设置租户上下文
        rls_debug_info["set_context"](1)

        # 查询所有数据（绕过RLS）
        data = rls_debug_info["query_all_data"]("your_table")
```

### 调试会话

```python
def test_debug_session(debug_session):
    # 使用debug_session，数据不会回滚
    pass
```

## 工具函数

### 数据库状态检查

```python
from tests.fixtures.database import check_test_database_status
check_test_database_status()
```

### RLS 调试信息

```python
from tests.fixtures.database import print_rls_debug_info
print_rls_debug_info(session, "your_table")
```

### 租户上下文

```python
from tests.fixtures.database import print_tenant_context
print_tenant_context(session)
```

## 故障排除

### 常见问题

1. **数据库连接失败**

   - 检查数据库服务是否运行
   - 验证连接参数是否正确
   - 确认用户权限

2. **测试数据库创建失败**

   - 确认用户有创建数据库的权限
   - 检查数据库名称是否合法
   - 查看日志输出的详细错误信息

3. **RLS 策略问题**
   - 使用 `--debug-rls` 选项查看详细信息
   - 检查租户上下文设置
   - 验证 RLS 策略配置

### 调试技巧

1. **保留测试数据**

   ```bash
   pytest --keep-test-data test_your_module.py
   ```

2. **启用详细日志**

   ```bash
   pytest -s --log-cli-level=INFO
   ```

3. **检查数据库状态**
   ```python
   from tests.fixtures.database import check_test_database_status
   check_test_database_status()
   ```

## 配置示例

### 开发环境配置

```bash
# .env.test
TEST_DB_NAME=course_booking_test_dev
TEST_DB_HOST=localhost
TEST_DB_PORT=5432
TEST_DB_USER=dev_user
TEST_DB_PASSWORD=dev_password
TEST_DB_ECHO=true
```

### CI/CD 环境配置

```yaml
# .github/workflows/test.yml
env:
  TEST_DB_NAME: course_booking_test_ci
  TEST_DB_HOST: localhost
  TEST_DB_PORT: 5432
  TEST_DB_USER: ci_user
  TEST_DB_PASSWORD: ci_password
  TEST_DB_ECHO: false
```

## 注意事项

1. 测试数据库会在每次测试运行时重新创建
2. 使用 `--keep-test-data` 选项时数据会保留，注意清理
3. 并行测试时需要使用不同的数据库名称
4. 确保测试用户有足够的权限创建和删除数据库
