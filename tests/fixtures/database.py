"""
改进的数据库测试fixtures
解决重复初始化和调试困难的问题
"""
import pytest
import os
import logging
from typing import Generator
from sqlmodel import SQLModel, create_engine, Session
from sqlalchemy import text
from sqlalchemy.exc import OperationalError, ProgrammingError

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 测试数据库配置
TEST_DB_NAME = os.getenv("TEST_DB_NAME", "course_booking_unit_test_db")
TEST_DB_HOST = os.getenv("TEST_DB_HOST", "localhost")
TEST_DB_PORT = os.getenv("TEST_DB_PORT", "5432")
TEST_DB_USER = os.getenv("TEST_DB_USER", "admin_alice")
TEST_DB_PASSWORD = os.getenv("TEST_DB_PASSWORD", "123qwe1985alice")

TEST_POSTGRES_URL = f"postgresql://{TEST_DB_USER}:{TEST_DB_PASSWORD}@{TEST_DB_HOST}:{TEST_DB_PORT}/{TEST_DB_NAME}"
TEST_DATABASE_URL = TEST_POSTGRES_URL

# 配置选项
TEST_DB_ECHO = os.getenv("TEST_DB_ECHO", "false").lower() == "true"
TEST_DB_POOL_SIZE = int(os.getenv("TEST_DB_POOL_SIZE", "5"))
TEST_DB_MAX_OVERFLOW = int(os.getenv("TEST_DB_MAX_OVERFLOW", "10"))


def create_test_database(db_name: str, base_url: str) -> bool:
    """
    创建测试数据库
    
    Args:
        db_name: 数据库名称
        base_url: 基础连接URL（连接到postgres数据库）
    
    Returns:
        bool: 创建是否成功
    """
    temp_engine = create_engine(base_url)
    try:
        with temp_engine.connect() as conn:
            # 结束当前事务
            conn.execute(text("COMMIT"))
            
            # 检查数据库是否存在
            result = conn.execute(text(
                "SELECT 1 FROM pg_database WHERE datname = :db_name"
            ), {"db_name": db_name})
            
            if result.fetchone():
                logger.info(f"数据库 {db_name} 已存在，先删除")
                # 强制断开所有连接
                conn.execute(text(f"""
                    SELECT pg_terminate_backend(pid)
                    FROM pg_stat_activity
                    WHERE datname = '{db_name}' AND pid <> pg_backend_pid()
                """))
                conn.execute(text(f"DROP DATABASE {db_name}"))
                logger.info(f"已删除现有数据库 {db_name}")
            
            # 创建新数据库
            conn.execute(text(f"CREATE DATABASE {db_name}"))
            logger.info(f"成功创建测试数据库 {db_name}")
            return True
            
    except (OperationalError, ProgrammingError) as e:
        logger.error(f"创建测试数据库失败: {e}")
        return False
    finally:
        temp_engine.dispose()


def test_database_connection(url: str) -> bool:
    """
    测试数据库连接是否正常
    
    Args:
        url: 数据库连接URL
    
    Returns:
        bool: 连接是否成功
    """
    try:
        test_engine = create_engine(url)
        with test_engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        test_engine.dispose()
        return True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return False


def drop_test_database(db_name: str, base_url: str) -> bool:
    """
    删除测试数据库
    
    Args:
        db_name: 数据库名称
        base_url: 基础连接URL（连接到postgres数据库）
    
    Returns:
        bool: 删除是否成功
    """
    temp_engine = create_engine(base_url)
    try:
        with temp_engine.connect() as conn:
            conn.execute(text("COMMIT"))
            
            # 强制断开所有连接
            conn.execute(text(f"""
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = '{db_name}' AND pid <> pg_backend_pid()
            """))
            
            # 删除数据库
            conn.execute(text(f"DROP DATABASE IF EXISTS {db_name}"))
            logger.info(f"已删除测试数据库 {db_name}")
            return True
            
    except (OperationalError, ProgrammingError) as e:
        logger.error(f"删除测试数据库失败: {e}")
        return False
    finally:
        temp_engine.dispose()


@pytest.fixture(scope="session")
def test_engine(request):
    """创建测试数据库引擎（改进版）"""
    # 解析数据库URL
    base_url = TEST_POSTGRES_URL.rsplit('/', 1)[0] + '/postgres'
    
    # 创建测试数据库
    if not create_test_database(TEST_DB_NAME, base_url):
        pytest.fail(f"无法创建测试数据库 {TEST_DB_NAME}")
    
    # 创建引擎连接到测试数据库
    engine = create_engine(
        TEST_DATABASE_URL,
        echo=TEST_DB_ECHO,
        pool_pre_ping=True,  # 添加连接健康检查
        pool_recycle=3600,   # 1小时后回收连接
        pool_size=TEST_DB_POOL_SIZE,
        max_overflow=TEST_DB_MAX_OVERFLOW
    )
    
    # 测试连接
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        logger.info(f"成功连接到测试数据库 {TEST_DB_NAME}")
    except Exception as e:
        logger.error(f"连接测试数据库失败: {e}")
        pytest.fail(f"无法连接到测试数据库: {e}")
    
    # 使用专门的测试初始化函数，避免与生产环境耦合
    try:
        from tests.utils.db_setup import setup_test_database
        setup_test_database(engine)
        logger.info("测试数据库初始化完成")
    except ImportError as e:
        logger.warning(f"无法导入测试数据库设置函数: {e}")
    except Exception as e:
        logger.error(f"测试数据库初始化失败: {e}")
        pytest.fail(f"测试数据库初始化失败: {e}")
    
    yield engine
    
    # 检查是否需要保留测试数据库
    keep_data = request.config.getoption("--keep-test-data")
    if keep_data:
        print(f"\n💾 测试数据库已保留: {TEST_DATABASE_URL}")
        print(f"💡 可以使用以下命令连接到测试数据库:")
        print(f"   psql '{TEST_DATABASE_URL}'")
    else:
        # 清理测试数据库
        try:
            SQLModel.metadata.drop_all(engine)
            logger.info("已清理测试数据库表结构")
        except Exception as e:
            logger.warning(f"清理表结构失败: {e}")
        # 删除测试数据库
        drop_test_database(TEST_DB_NAME, base_url)
    
    engine.dispose()
    
    
@pytest.fixture
def test_session(test_engine, request) -> Generator[Session, None, None]:
    """创建测试数据库会话（改进版，支持调试）"""
    connection = None
    transaction = None
    session = None
    
    try:
        connection = test_engine.connect()
        transaction = connection.begin()
        
        session = Session(bind=connection)
        session.execute(text("SET search_path TO public"))
        
        yield session
        
    except Exception as e:
        logger.error(f"测试会话创建失败: {e}")
        raise
    finally:
        if session:
            session.close()
        
        if transaction:
            try:
                # 检查是否需要保留数据用于调试
                keep_data = getattr(request.config, 'getoption', lambda x: False)("--keep-test-data")
                if keep_data:
                    transaction.commit()
                    logger.info("💾 测试数据已保留在数据库中，可用于调试")
                else:
                    transaction.rollback()
            except Exception as e:
                logger.warning(f"事务处理失败: {e}")
                try:
                    transaction.rollback()
                except:
                    pass
        
        if connection:
            connection.close()


@pytest.fixture
def debug_session(test_engine) -> Generator[Session, None, None]:
    """专门用于调试的数据库会话（不回滚数据）"""
    with Session(test_engine) as session:
        session.execute(text("SET search_path TO public"))
        yield session
        # 不回滚，数据会保留


@pytest.fixture
def rls_debug_info(test_session, request):
    """RLS调试信息fixture"""
    debug_rls = request.config.getoption("--debug-rls")
    if not debug_rls:
        yield None
        return
    
    from tests.utils.db_setup import check_rls_status, reset_tenant_context
    
    def get_rls_info(table_name: str):
        """获取指定表的RLS信息"""
        return check_rls_status(test_session, table_name)
    
    def reset_context():
        """重置租户上下文"""
        reset_tenant_context(test_session)
    
    def set_context(tenant_id: int):
        """设置租户上下文"""
        from tests.utils.db_setup import set_tenant_context
        set_tenant_context(test_session, tenant_id)
    
    def query_all_data(table_name: str):
        """查询表中的所有数据（绕过RLS）"""
        reset_context()
        result = test_session.exec(text(f"SELECT * FROM {table_name}")).all()
        return result
    
    yield {
        "get_rls_info": get_rls_info,
        "reset_context": reset_context,
        "set_context": set_context,
        "query_all_data": query_all_data
    }


@pytest.fixture
def tenant_context_manager(test_session):
    """租户上下文管理器"""
    from tests.utils.db_setup import set_tenant_context, reset_tenant_context
    
    class TenantContextManager:
        def __init__(self, session):
            self.session = session
            self.original_context = None
        
        def set_tenant(self, tenant_id: int):
            """设置租户上下文"""
            set_tenant_context(self.session, tenant_id)
        
        def reset(self):
            """重置租户上下文"""
            reset_tenant_context(self.session)
        
        def __enter__(self):
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            self.reset()
    
    return TenantContextManager(test_session)


# 向后兼容的fixture别名
@pytest.fixture
def test_session_legacy(test_engine) -> Generator[Session, None, None]:
    """向后兼容的测试会话（保持原有行为）"""
    connection = test_engine.connect()
    transaction = connection.begin()
    
    session = Session(bind=connection)
    session.execute(text("SET search_path TO public"))
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()


# 测试工具函数
def print_rls_debug_info(session: Session, table_name: str):
    """打印RLS调试信息"""
    from tests.utils.db_setup import check_rls_status
    info = check_rls_status(session, table_name)
    print(f"\n🔍 RLS Debug Info for {table_name}:")
    print(f"  RLS Enabled: {info['rls_enabled']}")
    print(f"  Policies: {len(info['policies'])}")
    for policy in info['policies']:
        print(f"    - {policy['name']}: {policy['condition'][:100]}...")


def print_tenant_context(session: Session):
    """打印当前租户上下文"""
    try:
        current_tenant = session.exec(text(
            "SELECT current_setting('app.current_tenant_id', true)"
        )).first()
        print(f"\n🏢 Current Tenant Context: {current_tenant}")
    except Exception as e:
        print(f"\n❌ Failed to get tenant context: {e}")


def count_records_by_tenant(session: Session, table_name: str) -> dict:
    """按租户统计记录数（调试用）"""
    from tests.utils.db_setup import reset_tenant_context
    
    # 重置上下文以绕过RLS
    reset_tenant_context(session)
    
    try:
        # 查询所有记录的租户分布
        result = session.exec(text(f"""
            SELECT tenant_id, COUNT(*) as count 
            FROM {table_name} 
            GROUP BY tenant_id
        """)).all()
        
        return {str(tenant_id): count for tenant_id, count in result}
    except Exception as e:
        print(f"Failed to count records for {table_name}: {e}")
        return {}


# 使用示例的测试类
class TestRLSDebugging:
    """RLS调试测试示例"""
    
    def test_rls_status_check(self, test_session, rls_debug_info):
        """测试RLS状态检查"""
        if rls_debug_info:
            info = rls_debug_info["get_rls_info"]("member_fixed_slot_locks")
            print(f"RLS Info: {info}")
            assert info["rls_enabled"] is True
    
    def test_tenant_context_management(self, tenant_context_manager):
        """测试租户上下文管理"""
        with tenant_context_manager as ctx:
            ctx.set_tenant(1)
            # 在这个上下文中进行测试
            pass
        # 上下文自动重置

# 测试数据库状态检查
def check_test_database_status():
    """检查测试数据库状态"""
    logger.info("=== 测试数据库配置信息 ===")
    logger.info(f"数据库名称: {TEST_DB_NAME}")
    logger.info(f"数据库主机: {TEST_DB_HOST}:{TEST_DB_PORT}")
    logger.info(f"数据库用户: {TEST_DB_USER}")
    logger.info(f"连接池大小: {TEST_DB_POOL_SIZE}")
    logger.info(f"SQL日志: {'启用' if TEST_DB_ECHO else '禁用'}")
    
    # 测试连接
    base_url = f"postgresql://{TEST_DB_USER}:{TEST_DB_PASSWORD}@{TEST_DB_HOST}:{TEST_DB_PORT}/postgres"
    if test_database_connection(base_url):
        logger.info("✅ 数据库服务器连接正常")
    else:
        logger.error("❌ 数据库服务器连接失败")
    
    if test_database_connection(TEST_DATABASE_URL):
        logger.info("✅ 测试数据库连接正常")
    else:
        logger.warning("⚠️ 测试数据库不存在或连接失败")
    
    logger.info("=========================")
