"""API客户端测试fixtures"""
import pytest
from fastapi.testclient import TestClient
from sqlmodel import Session

from app.main import app
from app.db.session import get_global_session


@pytest.fixture
def client(test_session: Session) -> TestClient:
    """创建测试客户端"""
    def get_test_session():
        yield test_session
    
    # 使用同一个session替换所有数据库依赖
    app.dependency_overrides[get_global_session] = get_test_session
    from app.db.session import get_session, get_tenant_session
    app.dependency_overrides[get_session] = get_test_session
    app.dependency_overrides[get_tenant_session] = lambda tenant_id: get_test_session()
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear() 