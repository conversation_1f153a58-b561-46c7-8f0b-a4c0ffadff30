"""API客户端测试fixtures"""
import pytest
from typing import Generator
from contextlib import asynccontextmanager
from fastapi.testclient import TestClient
from sqlmodel import Session

from app.main import app
from app.db.session import get_global_session


@pytest.fixture
def client(test_session: Session) -> Generator[TestClient, None, None]:
    """创建测试客户端"""
    def get_test_session():
        yield test_session

    # 使用同一个session替换所有数据库依赖
    app.dependency_overrides[get_global_session] = get_test_session
    from app.db.session import get_session, get_tenant_session
    app.dependency_overrides[get_session] = get_test_session
    app.dependency_overrides[get_tenant_session] = lambda _tenant_id: get_test_session()

    # 创建一个空的lifespan上下文管理器
    @asynccontextmanager
    async def empty_lifespan(app):
        # 不执行任何数据库初始化
        yield

    # 临时替换lifespan事件
    original_lifespan = app.router.lifespan_context
    app.router.lifespan_context = empty_lifespan

    try:
        with TestClient(app) as test_client:
            yield test_client
    finally:
        # 恢复原始lifespan
        app.router.lifespan_context = original_lifespan
        app.dependency_overrides.clear()