"""会员固定课位锁定相关测试fixtures"""
import pytest
from datetime import time
from sqlmodel import Session
from sqlalchemy import text

from app.features.members.fixed_lock_models import MemberFixedSlotLockStatus


@pytest.fixture
def sample_lock_data():
    """基础锁定数据"""
    return {
        "member_id": 1,  # 将在fixture中替换为实际的member_id
        "teacher_fixed_slot_id": 1,  # 将在fixture中替换为实际的slot_id
        "status": MemberFixedSlotLockStatus.ACTIVE,
        "created_by": 1  # 将在fixture中替换为实际的created_by
    }


@pytest.fixture
def sample_lock_data_list():
    """多个锁定数据"""
    return [
        {
            "status": MemberFixedSlotLockStatus.ACTIVE
        },
        {
            "status": MemberFixedSlotLockStatus.PAUSED
        },
        {
            "status": MemberFixedSlotLockStatus.CANCELLED
        }
    ]


@pytest.fixture
def batch_lock_data():
    """批量锁定测试数据"""
    return {
        "valid_slots": [1, 2, 3],  # 将在测试中替换为实际的slot_ids
        "invalid_slots": [999, 998],  # 不存在的slot_ids
        "mixed_slots": [1, 999, 2],  # 混合有效和无效的slot_ids
        "status": MemberFixedSlotLockStatus.ACTIVE
    }


@pytest.fixture
def status_transition_data():
    """状态转换测试数据"""
    return {
        "valid_transitions": [
            (MemberFixedSlotLockStatus.ACTIVE, MemberFixedSlotLockStatus.PAUSED),
            (MemberFixedSlotLockStatus.PAUSED, MemberFixedSlotLockStatus.ACTIVE),
            (MemberFixedSlotLockStatus.ACTIVE, MemberFixedSlotLockStatus.CANCELLED),
            (MemberFixedSlotLockStatus.PAUSED, MemberFixedSlotLockStatus.CANCELLED)
        ],
        "invalid_transitions": [
            (MemberFixedSlotLockStatus.CANCELLED, MemberFixedSlotLockStatus.ACTIVE),
            (MemberFixedSlotLockStatus.CANCELLED, MemberFixedSlotLockStatus.PAUSED)
        ]
    }


@pytest.fixture
def conflict_test_data():
    """冲突检测测试数据"""
    return {
        "same_slot_different_member": {
            "member_id_1": 1,
            "member_id_2": 2,
            "teacher_fixed_slot_id": 1
        },
        "different_slot_same_member": {
            "member_id": 1,
            "teacher_fixed_slot_id_1": 1,
            "teacher_fixed_slot_id_2": 2
        }
    }


@pytest.fixture
def created_lock(test_session: Session, created_member, created_fixed_slot, created_admin_user):
    """创建单个测试锁定记录"""
    from app.features.members.fixed_lock_service import MemberFixedSlotLockService
    from app.features.members.fixed_lock_schemas import MemberFixedSlotLockCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
    
    lock_data = MemberFixedSlotLockCreate(
        member_id=created_member["id"],
        teacher_fixed_slot_id=created_fixed_slot["id"],
        status=MemberFixedSlotLockStatus.ACTIVE,
        created_by=created_admin_user["id"]
    )
    
    lock = service.create_lock(lock_data, created_admin_user["id"])
    test_session.commit()
    
    return {
        "id": lock.id,
        "member_id": lock.member_id,
        "teacher_fixed_slot_id": lock.teacher_fixed_slot_id,
        "teacher_id": lock.teacher_id,
        "weekday": lock.weekday,
        "start_time": lock.start_time,
        "status": lock.status,
        "locked_at": lock.locked_at,
        "tenant_id": lock.tenant_id,
        "created_by": lock.created_by,
        "created_at": lock.created_at,
        "updated_at": lock.updated_at
    }


@pytest.fixture
def created_multiple_locks(test_session: Session, created_member, created_multiple_fixed_slots, created_admin_user):
    """创建多个测试锁定记录"""
    from app.features.members.fixed_lock_service import MemberFixedSlotLockService
    from app.features.members.fixed_lock_schemas import MemberFixedSlotLockCreate
    
    service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
    
    created_locks = []
    statuses = [MemberFixedSlotLockStatus.ACTIVE, MemberFixedSlotLockStatus.PAUSED, MemberFixedSlotLockStatus.CANCELLED]
    
    # 只锁定前3个可用且可见的时间段
    available_slots = [
        slot for slot in created_multiple_fixed_slots
        if slot["is_available"] and slot["is_visible_to_members"]
    ][:3]
    
    for i, slot in enumerate(available_slots):
        lock_data = MemberFixedSlotLockCreate(
            member_id=created_member["id"],
            teacher_fixed_slot_id=slot["id"],
            status=statuses[i % len(statuses)],
            created_by=created_admin_user["id"]
        )
        
        lock = service.create_lock(lock_data, created_admin_user["id"])
        created_locks.append({
            "id": lock.id,
            "member_id": lock.member_id,
            "teacher_fixed_slot_id": lock.teacher_fixed_slot_id,
            "teacher_id": lock.teacher_id,
            "weekday": lock.weekday,
            "start_time": lock.start_time,
            "status": lock.status,
            "locked_at": lock.locked_at,
            "tenant_id": lock.tenant_id,
            "created_by": lock.created_by
        })
    
    test_session.commit()
    return created_locks


@pytest.fixture
def created_member_2_locks(test_session: Session, created_member_2, created_teacher_2_with_slots, created_admin_user):
    """为第二个会员创建锁定记录"""
    from app.features.members.fixed_lock_service import MemberFixedSlotLockService
    from app.features.members.fixed_lock_schemas import MemberFixedSlotLockCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
    
    created_locks = []
    # 为第二个会员锁定第二个教师的时间段
    for slot in created_teacher_2_with_slots["slots"]:
        lock_data = MemberFixedSlotLockCreate(
            member_id=created_member_2["id"],
            teacher_fixed_slot_id=slot["id"],
            status=MemberFixedSlotLockStatus.ACTIVE,
            created_by=created_admin_user["id"]
        )
        
        lock = service.create_lock(lock_data, created_admin_user["id"])
        created_locks.append({
            "id": lock.id,
            "member_id": lock.member_id,
            "teacher_fixed_slot_id": lock.teacher_fixed_slot_id,
            "teacher_id": lock.teacher_id,
            "weekday": lock.weekday,
            "start_time": lock.start_time,
            "status": lock.status
        })
    
    test_session.commit()
    return {
        "member": created_member_2,
        "locks": created_locks
    }


@pytest.fixture
def query_test_data():
    """查询测试数据"""
    return {
        "basic_query": {
            "member_id": 1,
            "page": 1,
            "size": 10
        },
        "filtered_query": {
            "member_id": 1,
            "status": MemberFixedSlotLockStatus.ACTIVE,
            "page": 1,
            "size": 20
        },
        "teacher_query": {
            "teacher_id": 1,
            "status": MemberFixedSlotLockStatus.ACTIVE,
            "page": 1,
            "size": 50
        },
        "time_range_query": {
            "member_id": 1,
            "start_time_from": time(8, 0),
            "start_time_to": time(18, 0),
            "page": 1,
            "size": 50
        }
    }


@pytest.fixture
def available_slot_query_data():
    """可用时间段查询测试数据"""
    return {
        "basic_query": {
            "teacher_id": 1,
            "only_available": True,
            "only_visible": True,
            "exclude_locked": True
        },
        "weekday_filter": {
            "teacher_id": 1,
            "weekdays": [1, 2, 3],  # 周一到周三
            "only_available": True,
            "only_visible": True,
            "exclude_locked": True
        },
        "time_range_filter": {
            "teacher_id": 1,
            "start_time_from": time(9, 0),
            "start_time_to": time(17, 0),
            "only_available": True,
            "only_visible": True,
            "exclude_locked": True
        }
    }
