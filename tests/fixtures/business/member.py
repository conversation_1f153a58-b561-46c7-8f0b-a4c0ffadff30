"""会员相关测试fixtures"""
import pytest
import uuid
from sqlmodel import Session
from sqlalchemy import text


@pytest.fixture
def sample_member_data():
    """会员数据"""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": "测试会员",
        "phone": f"138{unique_id[:8]}",
        "email": f"member_{unique_id}@test.com",
        "gender": "male",
        "birthday": "1990-01-01"
    }


@pytest.fixture
def created_member(test_session: Session, created_admin_user):
    """使用MemberService创建会员（会自动创建默认会员卡）"""
    from app.features.members.service import MemberService
    from app.features.members.schemas import MemberCreate
    from app.features.members.models import MemberType
    
    # 设置租户上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
    
    unique_id = str(uuid.uuid4())[:8]
    member_data = MemberCreate(
        name="测试会员",
        phone=f"138{unique_id[:8]}",
        email=f"member_{unique_id}@test.com",
        gender="male",
        birthday="1990-01-01",
        member_type=MemberType.TRIAL
    )
    
    member = service.create_member(member_data, created_by=created_admin_user["id"])
    
    return {
        "id": member.id,
        "name": member.name,
        "phone": member.phone,
        "email": member.email,
        "member_type": member.member_type.value if member.member_type else "trial",
        "tenant_id": member.tenant_id,
        "created_by": member.created_by,
        "primary_member_card_id": member.primary_member_card_id,
        "primary_member_card_name": member.primary_member_card_name
    }


@pytest.fixture
def created_member_2(test_session: Session, created_admin_user):
    """使用MemberService创建第二个测试会员（会自动创建默认会员卡）"""
    from app.features.members.service import MemberService
    from app.features.members.schemas import MemberCreate
    from app.features.members.models import MemberType

    # 设置租户上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))

    service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])

    unique_id = str(uuid.uuid4())[:8]
    member_data = MemberCreate(
        name="测试会员2",
        phone=f"139{unique_id[:8]}",
        email=f"member2_{unique_id}@test.com",
        gender="female",
        birthday="1992-05-15",
        member_type=MemberType.FORMAL
    )

    member = service.create_member(member_data, created_by=created_admin_user["id"])

    return {
        "id": member.id,
        "name": member.name,
        "phone": member.phone,
        "email": member.email,
        "member_type": member.member_type.value if member.member_type else "formal",
        "tenant_id": member.tenant_id,
        "created_by": member.created_by,
        "primary_member_card_id": member.primary_member_card_id,
        "primary_member_card_name": member.primary_member_card_name
    }


@pytest.fixture
def member_token(client, created_member, created_tenant):
    """获取会员认证token"""
    login_data = {
        "phone": created_member["phone"],
        "verification_code": "1234",  # 使用测试验证码
        "tenant_code": created_tenant["code"]
    }

    response = client.post("/api/v1/auth/member/login", json=login_data)
    assert response.status_code == 200

    return response.json()["data"]["access_token"]