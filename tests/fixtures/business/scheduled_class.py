"""已排课表相关测试fixtures"""
import pytest
import uuid
from datetime import datetime, timedelta, timezone
from sqlmodel import Session
from sqlalchemy import text


@pytest.fixture
def sample_scheduled_class_data():
    """基础已排课表数据"""
    future_time = datetime.now() + timedelta(hours=3)
    return {
        "teacher_id": 1,  # 将在测试中替换为实际的teacher_id
        "member_id": None,  # 初始状态为可预约
        "class_datetime": future_time,
        "duration_minutes": 25,
        "class_type": "direct",
        "price": 100,
        "member_card_id": None,
        "member_card_name": None,
        "booking_remark": None,
        "member_no_cancel": False,
        "material_id": None,
        "material_name": "测试教材",
        "status": "available",
        "is_deleted": False,
        "is_visible_to_member": True,
        "operator_name": "测试操作员"
    }


@pytest.fixture
def sample_booked_class_data(sample_scheduled_class_data):
    """已预约课程数据"""
    data = sample_scheduled_class_data.copy()
    data.update({
        "member_id": 1,  # 将在测试中替换为实际的member_id
        "status": "booked",
        "member_card_id": 1,
        "member_card_name": "测试会员卡",
        "booking_remark": "测试预约备注"
    })
    return data


@pytest.fixture
def sample_teacher_class_create_data():
    """教师版课程创建数据"""
    future_time = datetime.now() + timedelta(hours=3)
    return {
        "class_datetime": future_time,
        "duration_minutes": 25,
        "price": 120,
        "material_name": "教师指定教材",
        "is_visible_to_member": True,
        "operator_name": "教师本人"
    }


@pytest.fixture
def sample_member_booking_data():
    """会员预约数据"""
    return {
        "class_id": 1,  # 将在测试中替换为实际的class_id
        "member_card_id": 1,
        "member_card_name": "会员测试卡",
        "material_name": "会员选择教材",
        "booking_remark": "会员预约备注"
    }


@pytest.fixture
def sample_admin_class_create_data():
    """管理员版课程创建数据"""
    future_time = datetime.now() + timedelta(hours=4)
    return {
        "teacher_id": 1,  # 将在测试中替换为实际的teacher_id
        "member_id": 1,   # 将在测试中替换为实际的member_id
        "class_datetime": future_time,
        "duration_minutes": 30,
        "class_type": "fixed",
        "price": 150,
        "member_card_id": 1,
        "member_card_name": "管理员指定卡",
        "booking_remark": "管理员创建备注",
        "member_no_cancel": False,
        "material_name": "管理员指定教材",
        "is_visible_to_member": True,
        "operator_name": "管理员"
    }


@pytest.fixture
def created_scheduled_class(test_session: Session, sample_scheduled_class_data, 
                          created_teacher, created_admin_user):
    """创建测试已排课表记录"""
    from app.features.courses.scheduled_classes_service import ScheduledClassService
    from app.features.courses.scheduled_classes_schemas import ScheduledClassCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    service = ScheduledClassService(test_session, created_admin_user["tenant_id"])
    
    # 使用实际的teacher_id
    class_data = sample_scheduled_class_data.copy()
    class_data["teacher_id"] = created_teacher["id"]
    
    scheduled_class_create = ScheduledClassCreate(**class_data)
    scheduled_class = service.create_scheduled_class(
        scheduled_class_create, 
        created_by=created_admin_user["id"]
    )
    
    return {
        "id": scheduled_class.id,
        "teacher_id": scheduled_class.teacher_id,
        "member_id": scheduled_class.member_id,
        "class_datetime": scheduled_class.class_datetime,
        "duration_minutes": scheduled_class.duration_minutes,
        "class_type": scheduled_class.class_type,
        "status": scheduled_class.status,
        "price": scheduled_class.price,
        "tenant_id": scheduled_class.tenant_id
    }


@pytest.fixture
def created_booked_class(test_session: Session, sample_booked_class_data,
                       created_teacher, created_member, created_admin_user):
    """创建已预约的测试课程"""
    from app.features.courses.scheduled_classes_service import ScheduledClassService
    from app.features.courses.scheduled_classes_schemas import ScheduledClassCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    service = ScheduledClassService(test_session, created_admin_user["tenant_id"])
    
    # 使用实际的teacher_id和member_id
    class_data = sample_booked_class_data.copy()
    class_data["teacher_id"] = created_teacher["id"]
    class_data["member_id"] = created_member["id"]
    
    scheduled_class_create = ScheduledClassCreate(**class_data)
    scheduled_class = service.create_scheduled_class(
        scheduled_class_create,
        created_by=created_admin_user["id"]
    )
    
    return {
        "id": scheduled_class.id,
        "teacher_id": scheduled_class.teacher_id,
        "member_id": scheduled_class.member_id,
        "class_datetime": scheduled_class.class_datetime,
        "duration_minutes": scheduled_class.duration_minutes,
        "class_type": scheduled_class.class_type,
        "status": scheduled_class.status,
        "price": scheduled_class.price,
        "tenant_id": scheduled_class.tenant_id
    }


@pytest.fixture
def multiple_scheduled_classes(test_session: Session, created_teacher, created_admin_user):
    """创建多个测试课程（可预约的时间设置）"""
    from app.features.courses.scheduled_classes_service import ScheduledClassService
    from app.features.courses.scheduled_classes_schemas import ScheduledClassCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    service = ScheduledClassService(test_session, created_admin_user["tenant_id"])
    
    classes = []
    # 修改为3小时后，确保可以预约（需要在上课前2小时预约）
    base_time = datetime.now() + timedelta(hours=3)
    
    for i in range(5):
        class_data = {
            "teacher_id": created_teacher["id"],
            "member_id": None,
            "class_datetime": base_time + timedelta(hours=i),
            "duration_minutes": 25,
            "class_type": "direct",
            "price": 100 + i * 10,
            "material_name": f"测试教材{i+1}",
            "status": "available",
            "is_visible_to_member": True,
            "operator_name": f"操作员{i+1}"
        }
        
        scheduled_class_create = ScheduledClassCreate(**class_data)
        scheduled_class = service.create_scheduled_class(
            scheduled_class_create,
            created_by=created_admin_user["id"]
        )
        
        classes.append({
            "id": scheduled_class.id,
            "teacher_id": scheduled_class.teacher_id,
            "class_datetime": scheduled_class.class_datetime,
            "duration_minutes": scheduled_class.duration_minutes,
            "price": scheduled_class.price,
            "status": scheduled_class.status
        })
    
    return classes


@pytest.fixture
def conflict_time_scenarios():
    """时间冲突测试场景数据"""
    base_time = datetime.now() + timedelta(hours=2)
    
    return {
        "base_time": base_time,
        "overlapping_start": base_time + timedelta(minutes=10),  # 开始时间重叠
        "overlapping_end": base_time - timedelta(minutes=10),    # 结束时间重叠
        "completely_inside": base_time + timedelta(minutes=5),   # 完全包含
        "completely_outside": base_time - timedelta(minutes=30), # 完全包含原课程
        "no_conflict": base_time + timedelta(hours=1),           # 无冲突
        "adjacent_before": base_time - timedelta(minutes=25),    # 紧邻前面
        "adjacent_after": base_time + timedelta(minutes=25)      # 紧邻后面
    }


@pytest.fixture
def batch_class_creation_data():
    """批量创建课程的测试数据"""
    base_time = datetime.now() + timedelta(days=1)
    
    return {
        "teacher_id": 1,  # 将在测试中替换
        "class_datetimes": [
            base_time + timedelta(hours=i) for i in range(8, 18)  # 8:00-17:00
        ],
        "duration_minutes": 25,
        "price": 100,
        "material_name": "批量创建教材",
        "is_visible_to_member": True,
        "operator_name": "批量操作员"
    }


# 为API测试添加的fixtures别名
@pytest.fixture
def sample_scheduled_class(created_scheduled_class):
    """API测试使用的已排课表fixture别名"""
    return created_scheduled_class


@pytest.fixture
def sample_available_class(created_scheduled_class):
    """API测试使用的可预约课程fixture别名"""
    return created_scheduled_class


@pytest.fixture
def sample_booked_class(created_booked_class):
    """API测试使用的已预约课程fixture别名"""
    return created_booked_class


@pytest.fixture
def multiple_available_classes(multiple_scheduled_classes):
    """API测试使用的多个可预约课程fixture别名"""
    return multiple_scheduled_classes


@pytest.fixture
def multiple_booked_classes(test_session: Session, created_teacher, created_member, created_admin_user):
    """创建多个已预约的测试课程（可取消的时间设置）"""
    from app.features.courses.scheduled_classes_service import ScheduledClassService
    from app.features.courses.scheduled_classes_schemas import ScheduledClassCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    service = ScheduledClassService(test_session, created_admin_user["tenant_id"])
    
    classes = []
    # 修改为3小时后，确保可以取消（需要在上课前2小时取消）
    base_time = datetime.now() + timedelta(hours=3)
    
    for i in range(3):
        class_data = {
            "teacher_id": created_teacher["id"],
            "member_id": created_member["id"],
            "class_datetime": base_time + timedelta(hours=i),
            "duration_minutes": 25,
            "class_type": "direct",
            "price": 100 + i * 10,
            "material_name": f"测试教材{i+1}",
            "status": "booked",
            "member_card_id": 1,
            "member_card_name": "测试会员卡",
            "booking_remark": f"测试预约备注{i+1}",
            "is_visible_to_member": True,
            "operator_name": f"操作员{i+1}"
        }
        
        scheduled_class_create = ScheduledClassCreate(**class_data)
        scheduled_class = service.create_scheduled_class(
            scheduled_class_create,
            created_by=created_admin_user["id"]
        )
        
        classes.append({
            "id": scheduled_class.id,
            "teacher_id": scheduled_class.teacher_id,
            "member_id": scheduled_class.member_id,
            "class_datetime": scheduled_class.class_datetime,
            "duration_minutes": scheduled_class.duration_minutes,
            "price": scheduled_class.price,
            "status": scheduled_class.status
        })
    
    return classes


@pytest.fixture
def create_class_factory(test_session: Session, sample_scheduled_class_data, 
                 created_teacher, created_member, created_admin_user):
    """通用课程创建工厂 fixture
    
    使用方式：
    def test_something(self, create_class_factory):
        # 创建3小时后的可预约课程（默认）
        available_class = create_class_factory()
        
        # 创建5小时后的可预约课程
        available_class = create_class_factory(hours_offset=5)
        
        # 创建3小时后的已预约课程
        booked_class = create_class_factory(is_booked=True)
        
        # 创建5小时后的已预约课程，使用默认会员
        booked_class = create_class_factory(hours_offset=5, is_booked=True)
        
        # 创建自定义会员的已预约课程
        custom_booked_class = create_class_factory(
            hours_offset=4, 
            is_booked=True, 
            member_id=123,
            booking_remark="自定义备注"
        )
        
        # 创建自定义价格的课程
        expensive_class = create_class_factory(hours_offset=3, price=200)
        
        # 边界测试：创建刚好2小时后的课程（不能取消）
        boundary_class = create_class_factory(hours_offset=2, is_booked=True)
    """
    from app.features.courses.scheduled_classes_service import ScheduledClassService
    from app.features.courses.scheduled_classes_schemas import ScheduledClassCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    service = ScheduledClassService(test_session, created_admin_user["tenant_id"])
    
    def _create_class(hours_offset=3, is_booked=False, member_id=None, **kwargs):
        """创建课程的内部函数
        
        Args:
            hours_offset: 时间偏移（小时），默认3小时
            is_booked: 是否创建已预约的课程，默认False
            member_id: 会员ID（当is_booked=True时，默认使用created_member）
            **kwargs: 其他课程数据覆盖
        """
        class_data = sample_scheduled_class_data.copy()
        class_data["teacher_id"] = created_teacher["id"]
        class_data["class_datetime"] = datetime.now() + timedelta(hours=hours_offset)
        
        if is_booked:
            # 创建已预约的课程
            class_data["member_id"] = member_id or created_member["id"]
            class_data["status"] = "booked"
            class_data["member_card_id"] = kwargs.get("member_card_id", 1)
            class_data["member_card_name"] = kwargs.get("member_card_name", "测试会员卡")
            class_data["booking_remark"] = kwargs.get("booking_remark", "测试预约备注")
        else:
            # 创建可预约的课程
            class_data["member_id"] = None
            class_data["status"] = "available"
            class_data["member_card_id"] = None
            class_data["member_card_name"] = None
            class_data["booking_remark"] = None
        
        # 应用额外的参数覆盖
        class_data.update(kwargs)
        
        scheduled_class_create = ScheduledClassCreate(**class_data)
        scheduled_class = service.create_scheduled_class(
            scheduled_class_create, 
            created_by=created_admin_user["id"]
        )
        
        return {
            "id": scheduled_class.id,
            "teacher_id": scheduled_class.teacher_id,
            "member_id": scheduled_class.member_id,
            "class_datetime": scheduled_class.class_datetime,
            "duration_minutes": scheduled_class.duration_minutes,
            "class_type": scheduled_class.class_type,
            "status": scheduled_class.status,
            "price": scheduled_class.price,
            "tenant_id": scheduled_class.tenant_id
        }
    
    return _create_class
