"""会员卡相关测试fixtures"""
import pytest
import uuid
from datetime import datetime, timezone, timedelta
from sqlmodel import Session
from sqlalchemy import text


@pytest.fixture
def sample_template_data():
    """会员卡模板数据"""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"测试模板_{unique_id}",
        "card_type": "times_limited",
        "sale_price": 1000,
        "available_balance": 10,
        "validity_days": 90,
        "description": "测试用会员卡模板"
    }


@pytest.fixture
def sample_value_template_data():
    """储值卡模板数据"""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"储值卡模板_{unique_id}",
        "card_type": "value_unlimited",
        "sale_price": 1000,
        "available_balance": 1000,
        "description": "测试用储值卡模板"
    }


@pytest.fixture
def created_template(test_session: Session, created_admin_user, sample_template_data):
    """直接在数据库中创建会员卡模板"""
    from app.features.member_cards.models import MemberCardTemplate, CardType
    
    # 设置租户上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    template = MemberCardTemplate(
        tenant_id=created_admin_user["tenant_id"],
        name=sample_template_data["name"],
        card_type=CardType(sample_template_data["card_type"]),
        sale_price=sample_template_data["sale_price"],
        available_balance=sample_template_data["available_balance"],
        validity_days=sample_template_data["validity_days"],
        description=sample_template_data["description"],
        is_active=True,
        created_by=created_admin_user["id"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    test_session.add(template)
    test_session.commit()
    test_session.refresh(template)
    
    return {
        "id": template.id,
        "name": template.name,
        "card_type": template.card_type.value,
        "sale_price": template.sale_price,
        "available_balance": template.available_balance,
        "validity_days": template.validity_days,
        "tenant_id": template.tenant_id,
        "created_by": template.created_by
    }


@pytest.fixture
def created_value_template(test_session: Session, created_admin_user, sample_value_template_data):
    """直接在数据库中创建储值卡模板"""
    from app.features.member_cards.models import MemberCardTemplate, CardType
    
    # 设置租户上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    template = MemberCardTemplate(
        tenant_id=created_admin_user["tenant_id"],
        name=sample_value_template_data["name"],
        card_type=CardType(sample_value_template_data["card_type"]),
        sale_price=sample_value_template_data["sale_price"],
        available_balance=sample_value_template_data["available_balance"],
        description=sample_value_template_data["description"],
        is_active=True,
        created_by=created_admin_user["id"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    test_session.add(template)
    test_session.commit()
    test_session.refresh(template)
    
    return {
        "id": template.id,
        "name": template.name,
        "card_type": template.card_type.value,
        "sale_price": template.sale_price,
        "available_balance": template.available_balance,
        "tenant_id": template.tenant_id,
        "created_by": template.created_by
    }


@pytest.fixture
def sample_card_data(created_member, created_template):
    """会员卡数据"""
    return {
        "member_id": created_member["id"],
        "template_id": created_template["id"],
        "card_type": created_template["card_type"],
        "balance": created_template["available_balance"]
    }


@pytest.fixture
def sample_value_card_data(created_member, created_value_template):
    """储值卡数据"""
    return {
        "member_id": created_member["id"],
        "template_id": created_value_template["id"],
        "card_type": created_value_template["card_type"],
        "balance": created_value_template["available_balance"]
    }


@pytest.fixture
def created_card(test_session: Session, created_admin_user, sample_card_data):
    """直接在数据库中创建会员卡"""
    from app.features.member_cards.models import MemberCard, CardType, CardStatus
    
    # 设置租户上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    # 生成卡号
    unique_id = str(uuid.uuid4())[:8].upper()
    card_number = f"TL{datetime.now().strftime('%Y%m%d')}{unique_id}"
    
    card = MemberCard(
        tenant_id=created_admin_user["tenant_id"],
        member_id=sample_card_data["member_id"],
        template_id=sample_card_data["template_id"],
        card_type=CardType(sample_card_data["card_type"]),
        balance=sample_card_data["balance"],
        status=CardStatus.ACTIVE,
        card_number=card_number,
        total_recharged=sample_card_data["balance"],
        total_consumed=0,
        expires_at=datetime.now() + timedelta(days=90),
        created_by=created_admin_user["id"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    test_session.add(card)
    test_session.commit()
    test_session.refresh(card)
    
    return {
        "id": card.id,
        "member_id": card.member_id,
        "template_id": card.template_id,
        "card_type": card.card_type.value,
        "balance": card.balance,
        "status": card.status.value,
        "card_number": card.card_number,
        "tenant_id": card.tenant_id,
        "created_by": card.created_by
    }


@pytest.fixture
def created_value_card(test_session: Session, created_admin_user, sample_value_card_data):
    """直接在数据库中创建储值卡"""
    from app.features.member_cards.models import MemberCard, CardType, CardStatus
    
    # 设置租户上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    # 生成卡号
    unique_id = str(uuid.uuid4())[:8].upper()
    card_number = f"VU{datetime.now().strftime('%Y%m%d')}{unique_id}"
    
    card = MemberCard(
        tenant_id=created_admin_user["tenant_id"],
        member_id=sample_value_card_data["member_id"],
        template_id=sample_value_card_data["template_id"],
        card_type=CardType(sample_value_card_data["card_type"]),
        balance=sample_value_card_data["balance"],
        status=CardStatus.ACTIVE,
        card_number=card_number,
        total_recharged=sample_value_card_data["balance"],
        total_consumed=0,
        created_by=created_admin_user["id"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    test_session.add(card)
    test_session.commit()
    test_session.refresh(card)
    
    return {
        "id": card.id,
        "member_id": card.member_id,
        "template_id": card.template_id,
        "card_type": card.card_type.value,
        "balance": card.balance,
        "status": card.status.value,
        "card_number": card.card_number,
        "tenant_id": card.tenant_id,
        "created_by": card.created_by
    }


@pytest.fixture
def sample_recharge_data(created_value_card):
    """充值数据"""
    return {
        "member_card_id": created_value_card["id"],
        "amount": 500,
        "bonus_amount": 50,
        "payment_method": "wechat",
        "notes": "测试充值"
    }


@pytest.fixture
def created_recharge_operation(test_session: Session, created_admin_user, created_value_card):
    """直接在数据库中创建充值操作记录"""
    from app.features.member_cards.models import MemberCardOperation, MemberCardOperationType, PaymentMethod
    
    # 设置租户上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    operation = MemberCardOperation(
        tenant_id=created_admin_user["tenant_id"],
        member_id=created_value_card["member_id"],
        member_card_id=created_value_card["id"],
        operation_type=MemberCardOperationType.RECHARGE,
        operation_description="测试充值500元",
        amount_change=500,
        balance_before=1000,
        balance_after=1500,
        payment_method=PaymentMethod.WECHAT,
        payment_amount=500,
        bonus_amount=0,
        transaction_id="TEST123456",
        operator_id=created_admin_user["id"],
        status="completed",
        created_at=datetime.now()
    )
    
    test_session.add(operation)
    test_session.commit()
    test_session.refresh(operation)
    
    return {
        "id": operation.id,
        "member_card_id": operation.member_card_id,
        "operation_type": operation.operation_type.value,
        "amount_change": operation.amount_change,
        "balance_before": operation.balance_before,
        "balance_after": operation.balance_after,
        "transaction_id": operation.transaction_id,
        "tenant_id": operation.tenant_id,
        "created_by": operation.operator_id
    }


@pytest.fixture
def created_consumption_operation(test_session: Session, created_admin_user, created_value_card):
    """直接在数据库中创建消费操作记录"""
    from app.features.member_cards.models import MemberCardOperation, MemberCardOperationType
    
    # 设置租户上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    operation = MemberCardOperation(
        tenant_id=created_admin_user["tenant_id"],
        member_id=created_value_card["member_id"],
        member_card_id=created_value_card["id"],
        operation_type=MemberCardOperationType.MANUAL_DEDUCTION,
        operation_description="课程扣费100元",
        amount_change=-100,
        balance_before=1000,
        balance_after=900,
        # 移除scheduled_class_id以避免外键约束问题
        operator_id=created_admin_user["id"],
        status="completed",
        created_at=datetime.now()
    )
    
    test_session.add(operation)
    test_session.commit()
    test_session.refresh(operation)
    
    return {
        "id": operation.id,
        "member_card_id": operation.member_card_id,
        "operation_type": operation.operation_type.value,
        "amount_change": operation.amount_change,
        "balance_before": operation.balance_before,
        "balance_after": operation.balance_after,
        "scheduled_class_id": None,  # 没有关联课程
        "tenant_id": operation.tenant_id,
        "created_by": operation.operator_id
    }
