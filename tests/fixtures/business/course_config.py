"""课程系统配置相关测试fixtures"""
import pytest
from datetime import time
from sqlmodel import Session
from sqlalchemy import text


@pytest.fixture
def sample_course_config_data():
    """课程系统配置数据"""
    return {
        "default_slot_duration_minutes": 30,
        "default_slot_interval_minutes": 10,
        "direct_booking_enabled": True,
        "max_advance_days": 15,
        "booking_deadline_hours": 3,
        "cancel_deadline_hours": 3,
        "booking_time_from": time(8, 0),
        "booking_time_to": time(22, 0),
        "require_material": True,
        "teacher_can_add_slots": True,
        "teacher_can_delete_empty_slots": True,
        "teacher_can_cancel_booking": False,
        "teacher_need_confirm": False,
        "fixed_booking_enabled": True,
        "auto_schedule_enabled": True,
        "auto_schedule_day": 1,  # 周日
        "auto_schedule_time": time(7, 0),
        "default_schedule_weeks": 6
    }


@pytest.fixture
def minimal_course_config_data():
    """最小化课程系统配置数据（仅必需字段）"""
    return {
        "default_slot_duration_minutes": 25,
        "direct_booking_enabled": True
    }


@pytest.fixture
def invalid_course_config_data():
    """无效的课程系统配置数据（用于测试验证）"""
    return {
        "default_slot_duration_minutes": 200,  # 超过最大值
        "default_slot_interval_minutes": 50,   # 超过最大值
        "max_advance_days": 400,               # 超过最大值
        "booking_deadline_hours": -1,          # 负数
        "booking_time_from": time(22, 0),      # 开始时间晚于结束时间
        "booking_time_to": time(8, 0),
        "auto_schedule_day": 8,                # 超出周日范围
        "default_schedule_weeks": 0            # 小于最小值
    }


@pytest.fixture
def created_course_config(test_session: Session, sample_course_config_data, created_admin_user):
    """创建测试课程系统配置"""
    from app.features.courses.config_service import CourseSystemConfigService
    from app.features.courses.config_schemas import CourseSystemConfigCreate
    
    # 设置RLS上下文
    test_session.execute(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    config_service = CourseSystemConfigService(test_session, created_admin_user["tenant_id"])
    config_create = CourseSystemConfigCreate(**sample_course_config_data)
    
    config = config_service.create_config(config_create, created_by=created_admin_user["id"])
    test_session.commit()
    
    return {
        "id": config.id,
        "tenant_id": config.tenant_id,
        "default_slot_duration_minutes": config.default_slot_duration_minutes,
        "default_slot_interval_minutes": config.default_slot_interval_minutes,
        "direct_booking_enabled": config.direct_booking_enabled,
        "max_advance_days": config.max_advance_days,
        "booking_deadline_hours": config.booking_deadline_hours,
        "cancel_deadline_hours": config.cancel_deadline_hours,
        "booking_time_from": config.booking_time_from,
        "booking_time_to": config.booking_time_to,
        "require_material": config.require_material,
        "teacher_can_add_slots": config.teacher_can_add_slots,
        "teacher_can_delete_empty_slots": config.teacher_can_delete_empty_slots,
        "teacher_can_cancel_booking": config.teacher_can_cancel_booking,
        "teacher_need_confirm": config.teacher_need_confirm,
        "fixed_booking_enabled": config.fixed_booking_enabled,
        "auto_schedule_enabled": config.auto_schedule_enabled,
        "auto_schedule_day": config.auto_schedule_day,
        "auto_schedule_time": config.auto_schedule_time,
        "default_schedule_weeks": config.default_schedule_weeks,
        "created_by": config.created_by,
        "created_at": config.created_at,
        "updated_at": config.updated_at
    }


@pytest.fixture
def default_course_config(test_session: Session, created_admin_user):
    """创建默认课程系统配置"""
    from app.features.courses.config_service import CourseSystemConfigService
    from app.features.courses.config_schemas import CourseSystemConfigCreate
    
    # 设置RLS上下文
    test_session.execute(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    config_service = CourseSystemConfigService(test_session, created_admin_user["tenant_id"])
    # 使用默认值创建配置
    config_create = CourseSystemConfigCreate()
    
    config = config_service.create_config(config_create, created_by=created_admin_user["id"])
    test_session.commit()
    
    return {
        "id": config.id,
        "tenant_id": config.tenant_id,
        "default_slot_duration_minutes": config.default_slot_duration_minutes,
        "default_slot_interval_minutes": config.default_slot_interval_minutes,
        "direct_booking_enabled": config.direct_booking_enabled,
        "created_by": config.created_by
    }


@pytest.fixture
def course_config_update_data():
    """课程配置更新数据"""
    return {
        "default_slot_duration_minutes": 45,
        "max_advance_days": 20,
        "booking_deadline_hours": 4,
        "teacher_can_add_slots": False,
        "auto_schedule_enabled": False
    }


@pytest.fixture
def course_config_partial_update_data():
    """课程配置部分更新数据"""
    return {
        "booking_time_from": time(9, 0),
        "booking_time_to": time(21, 0),
        "require_material": False
    }


@pytest.fixture
def course_config_time_update_data():
    """课程配置时间相关更新数据"""
    return {
        "booking_time_from": time(7, 30),
        "booking_time_to": time(23, 30),
        "auto_schedule_time": time(6, 30)
    }


@pytest.fixture
def course_config_invalid_update_data():
    """无效的课程配置更新数据"""
    return {
        "default_slot_duration_minutes": 150,  # 超过最大值
        "booking_time_from": time(20, 0),      # 开始时间晚于结束时间
        "booking_time_to": time(10, 0),
        "max_advance_days": -5                 # 负数
    }


@pytest.fixture
def course_config_field_validation_data():
    """用于字段验证测试的数据"""
    return [
        # (field_name, valid_value, invalid_value)
        ("default_slot_duration_minutes", 30, 200),
        ("default_slot_interval_minutes", 5, 50),
        ("max_advance_days", 30, 400),
        ("booking_deadline_hours", 2, -1),
        ("cancel_deadline_hours", 2, -1),
        ("auto_schedule_day", 1, 8),
        ("default_schedule_weeks", 4, 0),
        ("direct_booking_enabled", True, "invalid"),
        ("teacher_can_add_slots", False, "invalid")
    ]


@pytest.fixture
def course_config_time_validation_data():
    """用于时间字段验证测试的数据"""
    return [
        # (field_name, valid_value, invalid_value)
        ("booking_time_from", time(8, 0), "25:00"),
        ("booking_time_to", time(22, 0), "invalid_time"),
        ("auto_schedule_time", time(7, 0), "24:60")
    ]


@pytest.fixture
def course_config_consistency_test_data():
    """用于配置一致性测试的数据"""
    return [
        # 有效的配置组合
        {
            "booking_time_from": time(8, 0),
            "booking_time_to": time(22, 0),
            "booking_deadline_hours": 2,
            "cancel_deadline_hours": 2
        },
        # 无效的配置组合（开始时间晚于结束时间）
        {
            "booking_time_from": time(22, 0),
            "booking_time_to": time(8, 0),
            "booking_deadline_hours": 2,
            "cancel_deadline_hours": 2
        }
    ]


@pytest.fixture
def multiple_tenant_configs(test_session: Session, created_admin_user, created_second_tenant_admin):
    """创建多个租户的配置（用于测试数据隔离）"""
    from app.features.courses.config_service import CourseSystemConfigService
    from app.features.courses.config_schemas import CourseSystemConfigCreate
    
    configs = []
    
    # 第一个租户的配置
    test_session.execute(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    config_service_1 = CourseSystemConfigService(test_session, created_admin_user["tenant_id"])
    config_data_1 = {
        "default_slot_duration_minutes": 25,
        "max_advance_days": 30,
        "direct_booking_enabled": True
    }
    config_1 = config_service_1.create_config(
        CourseSystemConfigCreate(**config_data_1), 
        created_by=created_admin_user["id"]
    )
    configs.append({
        "tenant_id": created_admin_user["tenant_id"],
        "config": config_1
    })
    
    # 第二个租户的配置
    test_session.execute(text(f"SET app.current_tenant_id = '{created_second_tenant_admin['tenant_id']}'"))
    config_service_2 = CourseSystemConfigService(test_session, created_second_tenant_admin["tenant_id"])
    config_data_2 = {
        "default_slot_duration_minutes": 45,
        "max_advance_days": 15,
        "direct_booking_enabled": False
    }
    config_2 = config_service_2.create_config(
        CourseSystemConfigCreate(**config_data_2), 
        created_by=created_second_tenant_admin["id"]
    )
    configs.append({
        "tenant_id": created_second_tenant_admin["tenant_id"],
        "config": config_2
    })
    
    test_session.commit()
    return configs 