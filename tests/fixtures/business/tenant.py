"""租户相关测试fixtures"""
import pytest
from sqlmodel import Session
from sqlalchemy import text
import uuid
from datetime import datetime, timezone, timedelta


@pytest.fixture
def sample_tenant_data():
    """示例租户数据"""
    unique_id = str(uuid.uuid4())[:8]  # 使用UUID的前8位确保唯一性
    return {
        "name": "测试机构",
        "code": f"test_org_{unique_id}",
        "contact_name": "张三",
        "contact_phone": "13800138000",
        "contact_email": "<EMAIL>",
        "address": "北京市朝阳区测试街道123号"
    }


@pytest.fixture
def sample_plan_template(test_session: Session):
    """创建示例套餐模板"""
    from app.features.tenants.models import TenantPlanTemplate
    
    plan = TenantPlanTemplate(
        plan_code="basic",
        plan_name="测试套餐",
        description="用于测试的套餐",
        max_teachers=100,
        max_members=1000,
        max_storage_gb=10,
        monthly_price=99.00,
        features={"test_feature": True}
    )
    
    test_session.add(plan)
    test_session.commit()
    test_session.refresh(plan)
    return plan


@pytest.fixture
def created_tenant(test_session: Session, sample_tenant_data):
    """直接在数据库中创建一个测试租户（绕过API）"""
    from app.features.tenants.models import Tenant, TenantStatus, PlanType, BillingCycle
    
    # 清除租户上下文
    try:
        test_session.execute(text("RESET app.current_tenant_id"))
    except:
        pass
    
    tenant = Tenant(
        name=sample_tenant_data["name"],
        code=sample_tenant_data["code"],
        contact_name=sample_tenant_data["contact_name"],
        contact_phone=sample_tenant_data["contact_phone"],
        contact_email=sample_tenant_data["contact_email"],
        address=sample_tenant_data["address"],
        status=TenantStatus.TRIAL,
        plan_type=PlanType.TRIAL,
        max_teachers=5,
        max_members=50,
        max_storage_gb=1,
        billing_cycle=BillingCycle.MONTHLY,
        trial_expires_at=datetime.now() + timedelta(days=14),
        database_schema=f"tenant_{sample_tenant_data['code']}",
        api_key="test_api_key_" + sample_tenant_data["code"]
    )
    
    test_session.add(tenant)
    test_session.commit()
    test_session.refresh(tenant)
    
    return {
        "id": tenant.id,
        "name": tenant.name,
        "code": tenant.code,
        "contact_name": tenant.contact_name,
        "contact_phone": tenant.contact_phone,
        "contact_email": tenant.contact_email,
        "address": tenant.address,
        "status": tenant.status.value,
        "plan_type": tenant.plan_type.value,
        "max_teachers": tenant.max_teachers,
        "max_members": tenant.max_members,
        "max_storage_gb": tenant.max_storage_gb,
        "trial_expires_at": tenant.trial_expires_at.isoformat() if tenant.trial_expires_at else None,
        "created_at": tenant.created_at.isoformat(),
        "updated_at": tenant.updated_at.isoformat()
    } 