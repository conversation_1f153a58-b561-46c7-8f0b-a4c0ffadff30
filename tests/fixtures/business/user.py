"""用户相关测试fixtures"""
import pytest
from sqlmodel import Session
from sqlalchemy import text
import uuid


@pytest.fixture
def sample_user_data():
    """用户数据"""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "username": f"testuser_{unique_id}",
        "password": "password123",
        "real_name": "测试用户",
        "role": "admin",
        "email": f"test_{unique_id}@test.com"
    }


@pytest.fixture
def admin_user_data():
    """管理员用户数据"""
    from app.features.users.models import UserRole
    unique_id = str(uuid.uuid4())[:8]
    return {
        "username": f"admin_{unique_id}",
        "password": "password123",
        "real_name": "测试管理员",
        "role": UserRole.ADMIN,
        "email": f"admin_{unique_id}@test.com"
    }


@pytest.fixture
def super_admin_user_data():
    """超级管理员用户数据"""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "username": f"superadmin_{unique_id}",
        "password": "password123",
        "real_name": "超级管理员",
        "role": "super_admin",
        "email": f"superadmin_{unique_id}@test.com"
    }


@pytest.fixture
def created_admin_user(test_session: Session, created_tenant):
    """直接在数据库中创建管理员用户（绕过API认证）"""
    from app.features.users.models import User, UserRole, UserStatus
    from app.utils.security import get_password_hash

    # 设置租户上下文，这样RLS才允许我们创建属于该租户的用户
    test_session.execute(text(f"SET app.current_tenant_id = '{created_tenant['id']}'"))

    unique_id = str(uuid.uuid4())[:8]
    password_hash = get_password_hash("password123")

    user = User(
        username=f"admin_{unique_id}",
        email=f"admin_{unique_id}@test.com",
        password_hash=password_hash,
        real_name="测试管理员",
        role=UserRole.ADMIN,
        status=UserStatus.ACTIVE,
        tenant_id=created_tenant["id"]
    )

    test_session.add(user)
    test_session.commit()
    test_session.refresh(user)

    # 验证用户确实被创建了
    assert user.id is not None
    assert user.role == UserRole.ADMIN
    assert user.tenant_id == created_tenant["id"]

    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "password": "password123",  # 原始密码，用于登录
        "tenant_id": user.tenant_id
    }


@pytest.fixture
def created_super_admin_user(test_session: Session):
    """直接在数据库中创建超级管理员用户（绕过API认证）"""
    from app.features.users.models import User, UserRole, UserStatus
    from app.utils.security import get_password_hash
    
    # 重置租户上下文（超级管理员不属于任何租户）
    test_session.execute(text("RESET app.current_tenant_id"))
    
    unique_id = str(uuid.uuid4())[:8]
    password_hash = get_password_hash("password123")
    
    user = User(
        username=f"superadmin_{unique_id}",
        email=f"superadmin_{unique_id}@test.com",
        password_hash=password_hash,
        real_name="超级管理员",
        role=UserRole.SUPER_ADMIN,
        status=UserStatus.ACTIVE,
        tenant_id=None  # 超级管理员没有租户ID
    )
    
    test_session.add(user)
    test_session.commit()
    test_session.refresh(user)
    
    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "password": "password123",  # 原始密码，用于登录
        "tenant_id": None
    }


@pytest.fixture
def admin_token(client, created_admin_user, created_tenant):
    """获取管理员认证token"""
    login_data = {
        "email": created_admin_user["email"],
        "password": created_admin_user["password"],
        "tenant_code": created_tenant["code"]
    }
    
    response = client.post("/api/v1/auth/admin/login", json=login_data)
    assert response.status_code == 200
    
    return response.json()["data"]["access_token"]


@pytest.fixture
def super_admin_token(client, created_super_admin_user):
    """获取超级管理员认证token"""
    login_data = {
        "email": created_super_admin_user["email"],
        "password": created_super_admin_user["password"]
        # 超级管理员不需要tenant_code
    }
    
    response = client.post("/api/v1/auth/admin/login", json=login_data)
    assert response.status_code == 200
    
    return response.json()["data"]["access_token"]


@pytest.fixture
def created_second_tenant_admin(test_session: Session):
    """创建第二个租户和其管理员用户"""
    from app.features.tenants.models import Tenant
    from app.features.users.models import User, UserRole, UserStatus
    from app.utils.security import get_password_hash
    import uuid
    
    # 先创建第二个租户
    unique_id = str(uuid.uuid4())[:8]
    tenant = Tenant(
        name=f"测试租户2_{unique_id}",
        code=f"tenant2_{unique_id}",
        email=f"tenant2_{unique_id}@test.com",
        phone="13800000002",
        address="测试地址2"
    )
    test_session.add(tenant)
    test_session.commit()
    test_session.refresh(tenant)
    
    # 设置第二个租户的上下文
    test_session.execute(text(f"SET app.current_tenant_id = '{tenant.id}'"))
    
    # 为第二个租户创建管理员用户
    password_hash = get_password_hash("password123")
    user = User(
        username=f"admin2_{unique_id}",
        email=f"admin2_{unique_id}@test.com",
        password_hash=password_hash,
        real_name="第二个租户管理员",
        role=UserRole.ADMIN,
        status=UserStatus.ACTIVE,
        tenant_id=tenant.id
    )
    test_session.add(user)
    test_session.commit()
    test_session.refresh(user)
    
    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "password": "password123",
        "tenant_id": user.tenant_id,
        "tenant_code": tenant.code
    }


@pytest.fixture
def second_tenant_admin_token(client, created_second_tenant_admin):
    """获取第二个租户管理员认证token"""
    login_data = {
        "email": created_second_tenant_admin["email"],
        "password": created_second_tenant_admin["password"],
        "tenant_code": created_second_tenant_admin["tenant_code"]
    }
    
    response = client.post("/api/v1/auth/admin/login", json=login_data)
    assert response.status_code == 200
    
    return response.json()["data"]["access_token"] 