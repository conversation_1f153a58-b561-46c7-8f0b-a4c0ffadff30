"""教师相关测试fixtures"""
import pytest
import uuid
from decimal import Decimal
from sqlmodel import Session
from sqlalchemy import text


@pytest.fixture
def sample_teacher_data():
    """教师数据"""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"测试教师_{unique_id}",
        "gender": "male",
        "avatar": "https://example.com/avatar.jpg",
        "phone": f"139{unique_id[:8]}",
        "email": f"teacher_{unique_id}@test.com",
        "price_per_class": Decimal("100.00"),
        "teacher_category": "european",
        "region": "europe",
        "wechat_bound": False,
        "show_to_members": True,
        "introduction": "这是一位测试教师",
        "teaching_experience": 5,
        "specialties": ["口语", "语法"],
        "certifications": ["TESOL", "TEFL"],
        "priority_level": 10,
        "notes": "测试备注",
        "tag_ids": []
    }


@pytest.fixture
def sample_teacher_data_with_tags(sample_teacher_data, multiple_tags):
    """带标签的教师数据"""
    teacher_data = sample_teacher_data.copy()
    teacher_data["tag_ids"] = [tag["id"] for tag in multiple_tags[:2]]  # 使用前两个标签
    return teacher_data


@pytest.fixture
def created_teacher(test_session: Session, sample_teacher_data, created_admin_user):
    """创建测试教师"""
    from app.features.teachers.service import TeacherService
    from app.features.teachers.schemas import TeacherCreate
    
    # 设置RLS上下文
    test_session.execute(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    teacher_service = TeacherService(test_session, created_admin_user["tenant_id"])
    teacher_create = TeacherCreate(**sample_teacher_data)
    
    teacher = teacher_service.create_teacher(teacher_create, created_by=created_admin_user["id"])
    test_session.commit()
    
    return {
        "id": teacher.id,
        "name": teacher.name,
        "gender": teacher.gender,
        "avatar": teacher.avatar,
        "phone": teacher.phone,
        "email": teacher.email,
        "price_per_class": teacher.price_per_class,
        "teacher_category": teacher.teacher_category,
        "region": teacher.region,
        "wechat_bound": teacher.wechat_bound,
        "show_to_members": teacher.show_to_members,
        "status": teacher.status,
        "tenant_id": teacher.tenant_id,
        "created_by": teacher.created_by
    }


@pytest.fixture
def created_teacher_with_tags(test_session: Session, sample_teacher_data_with_tags, created_admin_user):
    """创建带标签的测试教师"""
    from app.features.teachers.service import TeacherService
    from app.features.teachers.schemas import TeacherCreate

    # 设置RLS上下文
    test_session.execute(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))

    teacher_service = TeacherService(test_session, created_admin_user["tenant_id"])
    teacher_create = TeacherCreate(**sample_teacher_data_with_tags)

    teacher = teacher_service.create_teacher(teacher_create, created_by=created_admin_user["id"])
    test_session.commit()

    return {
        "id": teacher.id,
        "name": teacher.name,
        "gender": teacher.gender,
        "avatar": teacher.avatar,
        "phone": teacher.phone,
        "email": teacher.email,
        "price_per_class": teacher.price_per_class,
        "teacher_category": teacher.teacher_category,
        "region": teacher.region,
        "wechat_bound": teacher.wechat_bound,
        "show_to_members": teacher.show_to_members,
        "status": teacher.status,
        "tenant_id": teacher.tenant_id,
        "created_by": teacher.created_by,
        "tag_ids": sample_teacher_data_with_tags["tag_ids"]
    }


@pytest.fixture
def created_teacher_2(test_session: Session, created_admin_user):
    """创建第二个测试教师"""
    from app.features.teachers.service import TeacherService
    from app.features.teachers.schemas import TeacherCreate

    # 设置RLS上下文
    test_session.execute(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))

    unique_id = str(uuid.uuid4())[:8]
    teacher_data = {
        "name": f"测试教师2_{unique_id}",
        "gender": "female",
        "avatar": "https://example.com/avatar2.jpg",
        "phone": f"138{unique_id[:8]}",
        "email": f"teacher2_{unique_id}@test.com",
        "price_per_class": Decimal("120.00"),
        "teacher_category": "filipino",
        "region": "philippines",
        "wechat_bound": False,
        "show_to_members": True,
        "introduction": "这是第二位测试教师",
        "teaching_experience": 3,
        "specialties": ["听力", "写作"],
        "certifications": ["IELTS", "TOEFL"],
        "priority_level": 8,
        "notes": "第二个教师的测试备注",
        "tag_ids": []
    }

    teacher_service = TeacherService(test_session, created_admin_user["tenant_id"])
    teacher_create = TeacherCreate(**teacher_data)

    teacher = teacher_service.create_teacher(teacher_create, created_by=created_admin_user["id"])
    test_session.commit()

    return {
        "id": teacher.id,
        "name": teacher.name,
        "gender": teacher.gender,
        "avatar": teacher.avatar,
        "phone": teacher.phone,
        "email": teacher.email,
        "price_per_class": teacher.price_per_class,
        "teacher_category": teacher.teacher_category,
        "region": teacher.region,
        "wechat_bound": teacher.wechat_bound,
        "show_to_members": teacher.show_to_members,
        "status": teacher.status,
        "tenant_id": teacher.tenant_id,
        "created_by": teacher.created_by
    }


@pytest.fixture
def multiple_teachers(test_session: Session, created_admin_user, multiple_tags):
    """创建多个测试教师"""
    from app.features.teachers.service import TeacherService
    from app.features.teachers.schemas import TeacherCreate
    from app.features.teachers.models import TeacherCategory, TeacherRegion, TeacherStatus
    
    # 设置RLS上下文
    test_session.execute(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    teacher_service = TeacherService(test_session, created_admin_user["tenant_id"])
    
    teachers = []
    categories = [TeacherCategory.EUROPEAN, TeacherCategory.FILIPINO, TeacherCategory.SOUTH_AFRICAN]
    regions = [TeacherRegion.EUROPE, TeacherRegion.PHILIPPINES, TeacherRegion.SOUTH_AFRICA]
    prices = [Decimal("100.00"), Decimal("80.00"), Decimal("120.00")]
    
    for i in range(3):
        unique_id = str(uuid.uuid4())[:8]
        teacher_data = {
            "name": f"教师_{i}_{unique_id}",
            "gender": "male" if i % 2 == 0 else "female",
            "phone": f"139{unique_id[:8]}",
            "email": f"teacher_{i}_{unique_id}@test.com",
            "price_per_class": prices[i],
            "teacher_category": categories[i],
            "region": regions[i],
            "wechat_bound": i == 1,  # 只有第二个教师绑定微信
            "show_to_members": i != 2,  # 第三个教师不对会员展示
            "introduction": f"这是测试教师{i}",
            "teaching_experience": (i + 1) * 2,
            "specialties": [f"技能{i}"],
            "certifications": [f"证书{i}"],
            "priority_level": (i + 1) * 5,
            "notes": f"教师{i}的备注",
            "tag_ids": [multiple_tags[i]["id"]] if i < len(multiple_tags) else []
        }
        
        teacher_create = TeacherCreate(**teacher_data)
        teacher = teacher_service.create_teacher(teacher_create, created_by=created_admin_user["id"])
        
        # 注意：保持默认的PENDING状态，让测试自己管理状态转换
        
        teachers.append({
            "id": teacher.id,
            "name": teacher.name,
            "gender": teacher.gender,
            "phone": teacher.phone,
            "email": teacher.email,
            "price_per_class": teacher.price_per_class,
            "teacher_category": teacher.teacher_category,
            "region": teacher.region,
            "wechat_bound": teacher.wechat_bound,
            "show_to_members": teacher.show_to_members,
            "status": teacher.status,
            "tenant_id": teacher.tenant_id,
            "created_by": teacher.created_by,
            "tag_ids": teacher_data["tag_ids"]
        })
    
    test_session.commit()
    return teachers


@pytest.fixture
def teacher_update_data():
    """教师更新数据"""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"更新教师_{unique_id}",
        "price_per_class": Decimal("150.00"),
        "introduction": "更新后的教师介绍",
        "teaching_experience": 8,
        "priority_level": 15
    }


@pytest.fixture
def teacher_query_params():
    """教师查询参数"""
    return {
        "page": 1,
        "size": 10,
        "sort_by": "created_at",
        "sort_order": "desc"
    }


@pytest.fixture
def teacher_tag_assign_data(created_teacher, multiple_tags):
    """教师标签分配数据"""
    return {
        "teacher_id": created_teacher["id"],
        "tag_ids": [tag["id"] for tag in multiple_tags]
    }


@pytest.fixture
def teacher_tag_batch_data(multiple_teachers, multiple_tags):
    """教师标签批量操作数据"""
    return {
        "teacher_ids": [teacher["id"] for teacher in multiple_teachers[:2]],
        "tag_ids": [tag["id"] for tag in multiple_tags[:2]],
        "operation": "add"
    }


@pytest.fixture
def teacher_status_update_data():
    """教师状态更新数据"""
    return {
        "status": "active",
        "reason": "测试状态更新"
    }
