import pytest
from datetime import datetime, timezone
from decimal import Decimal
from sqlmodel import Session

from app.features.teachers.service import TeacherService
from app.features.teachers.schemas import TeacherC<PERSON>, TeacherUpdate, TeacherQuery, TeacherStatusUpdate
from app.features.teachers.models import TeacherCategory, TeacherRegion, TeacherStatus
from app.features.teachers.exceptions import TeacherBusinessException, TeacherNotFoundError


class TestTeacherService:
    """教师服务单元测试"""
    
    def test_create_teacher(self, test_session: Session, sample_teacher_data, created_admin_user):
        """测试创建教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        teacher_create = TeacherCreate(**sample_teacher_data)
        
        teacher = service.create_teacher(teacher_create, created_by=created_admin_user["id"])
        
        assert teacher.id is not None
        assert teacher.name == sample_teacher_data["name"]
        assert teacher.phone == sample_teacher_data["phone"]
        assert teacher.email == sample_teacher_data["email"]
        assert teacher.price_per_class == sample_teacher_data["price_per_class"]
        assert teacher.teacher_category == TeacherCategory(sample_teacher_data["teacher_category"])
        assert teacher.region == TeacherRegion(sample_teacher_data["region"])
        assert teacher.status == TeacherStatus.PENDING  # 默认状态
        assert teacher.tenant_id == created_admin_user["tenant_id"]
        assert teacher.created_by == created_admin_user["id"]
        assert teacher.created_at is not None
        assert teacher.updated_at is not None
    
    def test_create_teacher_with_tags(self, test_session: Session, sample_teacher_data_with_tags, created_admin_user):
        """测试创建带标签的教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        teacher_create = TeacherCreate(**sample_teacher_data_with_tags)
        
        teacher = service.create_teacher(teacher_create, created_by=created_admin_user["id"])
        
        assert teacher.id is not None
        assert teacher.name == sample_teacher_data_with_tags["name"]
        
        # 验证标签关联
        teacher_tags = service.get_teacher_tags(teacher.id)
        assert len(teacher_tags) == len(sample_teacher_data_with_tags["tag_ids"])
    
    def test_create_teacher_duplicate_email(self, test_session: Session, sample_teacher_data, created_admin_user):
        """测试创建重复邮箱教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        teacher_create = TeacherCreate(**sample_teacher_data)
        
        # 先创建一个教师
        service.create_teacher(teacher_create, created_by=created_admin_user["id"])
        
        # 尝试创建相同邮箱的教师
        with pytest.raises(TeacherBusinessException, match="邮箱.*已存在"):
            service.create_teacher(teacher_create, created_by=created_admin_user["id"])
    
    def test_create_teacher_duplicate_phone(self, test_session: Session, created_admin_user):
        """测试创建重复手机号教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        teacher_data1 = TeacherCreate(
            name="教师1",
            phone="***********",
            email="<EMAIL>",
            price_per_class=Decimal("100.00"),
            teacher_category=TeacherCategory.EUROPEAN,
            region=TeacherRegion.EUROPE
        )
        
        teacher_data2 = TeacherCreate(
            name="教师2",
            phone="***********",  # 相同手机号
            email="<EMAIL>",
            price_per_class=Decimal("100.00"),
            teacher_category=TeacherCategory.EUROPEAN,
            region=TeacherRegion.EUROPE
        )
        
        # 先创建一个教师
        service.create_teacher(teacher_data1, created_by=created_admin_user["id"])
        
        # 尝试创建相同手机号的教师
        with pytest.raises(TeacherBusinessException, match="手机号.*已存在"):
            service.create_teacher(teacher_data2, created_by=created_admin_user["id"])
    
    def test_create_teacher_duplicate_wechat(self, test_session: Session, created_admin_user):
        """测试创建重复微信OpenID教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        teacher_data1 = TeacherCreate(
            name="教师1",
            phone="***********",
            email="<EMAIL>",
            price_per_class=Decimal("100.00"),
            teacher_category=TeacherCategory.EUROPEAN,
            region=TeacherRegion.EUROPE,
            wechat_openid="test_openid_123"
        )
        
        teacher_data2 = TeacherCreate(
            name="教师2",
            phone="***********",
            email="<EMAIL>",
            price_per_class=Decimal("100.00"),
            teacher_category=TeacherCategory.EUROPEAN,
            region=TeacherRegion.EUROPE,
            wechat_openid="test_openid_123"  # 相同微信OpenID
        )
        
        # 先创建一个教师
        service.create_teacher(teacher_data1, created_by=created_admin_user["id"])
        
        # 尝试创建相同微信OpenID的教师
        with pytest.raises(TeacherBusinessException, match="微信账号已绑定"):
            service.create_teacher(teacher_data2, created_by=created_admin_user["id"])
    
    def test_get_teacher(self, test_session: Session, created_teacher, created_admin_user):
        """测试根据ID获取教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        teacher = service.get_teacher(created_teacher["id"])
        
        assert teacher is not None
        assert teacher.id == created_teacher["id"]
        assert teacher.name == created_teacher["name"]
    
    def test_get_teacher_not_found(self, test_session: Session, created_admin_user):
        """测试获取不存在的教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        teacher = service.get_teacher(99999)
        
        assert teacher is None
    
    def test_get_teacher_by_email(self, test_session: Session, created_teacher, created_admin_user):
        """测试根据邮箱获取教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        teacher = service.get_teacher_by_email(created_teacher["email"])
        
        assert teacher is not None
        assert teacher.email == created_teacher["email"]
        assert teacher.name == created_teacher["name"]
    
    def test_get_teacher_by_phone(self, test_session: Session, created_teacher, created_admin_user):
        """测试根据手机号获取教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        teacher = service.get_teacher_by_phone(created_teacher["phone"])
        
        assert teacher is not None
        assert teacher.phone == created_teacher["phone"]
        assert teacher.name == created_teacher["name"]
    
    def test_get_teachers_list(self, test_session: Session, multiple_teachers, created_admin_user):
        """测试获取教师列表"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        query_params = TeacherQuery(page=1, size=10)
        
        teachers, total = service.get_teachers(query_params)
        
        assert len(teachers) == len(multiple_teachers)
        assert total == len(multiple_teachers)
        assert all(teacher.tenant_id == created_admin_user["tenant_id"] for teacher in teachers)
    
    def test_get_teachers_pagination(self, test_session: Session, multiple_teachers, created_admin_user):
        """测试教师列表分页"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        query_params = TeacherQuery(page=1, size=2)
        
        teachers, total = service.get_teachers(query_params)
        
        assert len(teachers) == 2
        assert total == len(multiple_teachers)
    
    def test_get_teachers_filter_by_category(self, test_session: Session, multiple_teachers, created_admin_user):
        """测试按分类筛选教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        query_params = TeacherQuery(teacher_category=TeacherCategory.EUROPEAN)
        
        teachers, total = service.get_teachers(query_params)
        
        assert all(teacher.teacher_category == TeacherCategory.EUROPEAN for teacher in teachers)
    
    def test_get_teachers_filter_by_region(self, test_session: Session, multiple_teachers, created_admin_user):
        """测试按区域筛选教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        query_params = TeacherQuery(region=TeacherRegion.EUROPE)
        
        teachers, total = service.get_teachers(query_params)
        
        assert all(teacher.region == TeacherRegion.EUROPE for teacher in teachers)
    
    def test_get_teachers_filter_by_status(self, test_session: Session, multiple_teachers, created_admin_user):
        """测试按状态筛选教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        query_params = TeacherQuery(status=TeacherStatus.ACTIVE)
        
        teachers, total = service.get_teachers(query_params)
        
        assert all(teacher.status == TeacherStatus.ACTIVE for teacher in teachers)
    
    def test_get_teachers_filter_by_price_range(self, test_session: Session, multiple_teachers, created_admin_user):
        """测试按价格范围筛选教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        query_params = TeacherQuery(min_price=Decimal("90.00"), max_price=Decimal("110.00"))
        
        teachers, total = service.get_teachers(query_params)
        
        assert all(Decimal("90.00") <= teacher.price_per_class <= Decimal("110.00") for teacher in teachers)
    
    def test_get_teachers_search(self, test_session: Session, multiple_teachers, created_admin_user):
        """测试搜索教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        query_params = TeacherQuery(name="教师_0")
        
        teachers, total = service.get_teachers(query_params)
        
        assert all("教师_0" in teacher.name for teacher in teachers)
    
    def test_count_teachers(self, test_session: Session, multiple_teachers, created_admin_user):
        """测试统计教师数量"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        total_count = service.count_teachers()
        active_count = service.count_teachers(status=TeacherStatus.ACTIVE)
        european_count = service.count_teachers(teacher_category=TeacherCategory.EUROPEAN)
        
        assert total_count == len(multiple_teachers)
        assert active_count >= 0
        assert european_count >= 0

    def test_update_teacher(self, test_session: Session, created_teacher, teacher_update_data, created_admin_user):
        """测试更新教师信息"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        teacher_update = TeacherUpdate(**teacher_update_data)

        updated_teacher = service.update_teacher(created_teacher["id"], teacher_update)

        assert updated_teacher.id == created_teacher["id"]
        assert updated_teacher.name == teacher_update_data["name"]
        assert updated_teacher.price_per_class == teacher_update_data["price_per_class"]
        assert updated_teacher.introduction == teacher_update_data["introduction"]
        assert updated_teacher.teaching_experience == teacher_update_data["teaching_experience"]
        assert updated_teacher.priority_level == teacher_update_data["priority_level"]
        assert updated_teacher.updated_at is not None

    def test_update_teacher_not_found(self, test_session: Session, teacher_update_data, created_admin_user):
        """测试更新不存在的教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        teacher_update = TeacherUpdate(**teacher_update_data)

        with pytest.raises(TeacherNotFoundError):
            service.update_teacher(99999, teacher_update)

    def test_update_teacher_duplicate_email(self, test_session: Session, multiple_teachers, created_admin_user):
        """测试更新教师邮箱为已存在的邮箱"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 尝试将第二个教师的邮箱更新为第一个教师的邮箱
        teacher_update = TeacherUpdate(email=multiple_teachers[0]["email"])

        with pytest.raises(TeacherBusinessException, match="邮箱.*已存在"):
            service.update_teacher(multiple_teachers[1]["id"], teacher_update)

    def test_delete_teacher(self, test_session: Session, created_teacher, created_admin_user):
        """测试删除教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 删除教师
        service.delete_teacher(created_teacher["id"])

        # 验证教师已被删除
        deleted_teacher = service.get_teacher(created_teacher["id"])
        assert deleted_teacher is None

    def test_delete_teacher_not_found(self, test_session: Session, created_admin_user):
        """测试删除不存在的教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        with pytest.raises(TeacherNotFoundError):
            service.delete_teacher(99999)

    # 标签管理测试
    def test_assign_tags_to_teacher(self, test_session: Session, created_teacher, multiple_tags, created_admin_user):
        """测试为教师分配标签"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        tag_ids = [tag["id"] for tag in multiple_tags[:2]]

        service.assign_tags_to_teacher(created_teacher["id"], tag_ids, created_by=created_admin_user["id"])

        # 验证标签已分配
        teacher_tags = service.get_teacher_tags(created_teacher["id"])
        assert len(teacher_tags) == 2
        assigned_tag_ids = [tag["id"] for tag in teacher_tags]
        assert all(tag_id in assigned_tag_ids for tag_id in tag_ids)

    def test_assign_tags_to_nonexistent_teacher(self, test_session: Session, multiple_tags, created_admin_user):
        """测试为不存在的教师分配标签"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        tag_ids = [tag["id"] for tag in multiple_tags[:1]]

        with pytest.raises(TeacherNotFoundError):
            service.assign_tags_to_teacher(99999, tag_ids, created_by=created_admin_user["id"])

    def test_remove_tags_from_teacher(self, test_session: Session, created_teacher_with_tags, created_admin_user):
        """测试移除教师标签"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 获取当前标签
        current_tags = service.get_teacher_tags(created_teacher_with_tags["id"])
        assert len(current_tags) > 0

        # 移除第一个标签
        tag_ids_to_remove = [current_tags[0]["id"]]
        service.remove_tags_from_teacher(created_teacher_with_tags["id"], tag_ids_to_remove)

        # 验证标签已移除
        remaining_tags = service.get_teacher_tags(created_teacher_with_tags["id"])
        assert len(remaining_tags) == len(current_tags) - 1
        remaining_tag_ids = [tag["id"] for tag in remaining_tags]
        assert current_tags[0]["id"] not in remaining_tag_ids

    def test_get_teacher_tags(self, test_session: Session, created_teacher_with_tags, created_admin_user):
        """测试获取教师标签列表"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        teacher_tags = service.get_teacher_tags(created_teacher_with_tags["id"])

        assert len(teacher_tags) == len(created_teacher_with_tags["tag_ids"])
        assert all("id" in tag for tag in teacher_tags)
        assert all("name" in tag for tag in teacher_tags)
        assert all("assigned_at" in tag for tag in teacher_tags)

    def test_batch_assign_tags(self, test_session: Session, multiple_teachers, multiple_tags, created_admin_user):
        """测试批量为教师分配标签"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        teacher_ids = [teacher["id"] for teacher in multiple_teachers[:2]]
        tag_ids = [tag["id"] for tag in multiple_tags[:1]]

        service.batch_assign_tags(teacher_ids, tag_ids, created_by=created_admin_user["id"])

        # 验证所有教师都分配了标签
        for teacher_id in teacher_ids:
            teacher_tags = service.get_teacher_tags(teacher_id)
            assigned_tag_ids = [tag["id"] for tag in teacher_tags]
            assert all(tag_id in assigned_tag_ids for tag_id in tag_ids)

    def test_batch_remove_tags(self, test_session: Session, multiple_teachers, multiple_tags, created_admin_user):
        """测试批量移除教师标签"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        teacher_ids = [teacher["id"] for teacher in multiple_teachers[:2]]
        tag_ids = [tag["id"] for tag in multiple_tags[:1]]

        # 先批量分配标签
        service.batch_assign_tags(teacher_ids, tag_ids, created_by=created_admin_user["id"])

        # 再批量移除标签
        service.batch_remove_tags(teacher_ids, tag_ids)

        # 验证标签已移除
        for teacher_id in teacher_ids:
            teacher_tags = service.get_teacher_tags(teacher_id)
            assigned_tag_ids = [tag["id"] for tag in teacher_tags]
            assert not any(tag_id in assigned_tag_ids for tag_id in tag_ids)

    # 状态管理测试
    def test_update_teacher_status(self, test_session: Session, created_teacher, created_admin_user):
        """测试更新教师状态"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        status_update = TeacherStatusUpdate(status=TeacherStatus.ACTIVE)

        updated_teacher = service.update_teacher_status(created_teacher["id"], status_update)

        assert updated_teacher.status == TeacherStatus.ACTIVE
        assert updated_teacher.updated_at is not None

    def test_update_teacher_status_invalid_transition(self, test_session: Session, created_teacher, created_admin_user):
        """测试无效的状态转换"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 先将教师状态设为INACTIVE
        service.update_teacher_status(created_teacher["id"], TeacherStatusUpdate(status=TeacherStatus.ACTIVE))
        service.update_teacher_status(created_teacher["id"], TeacherStatusUpdate(status=TeacherStatus.INACTIVE))

        # 尝试从INACTIVE直接转换到SUSPENDED（无效转换）
        with pytest.raises(TeacherBusinessException, match="无法从状态.*转换到"):
            service.update_teacher_status(created_teacher["id"], TeacherStatusUpdate(status=TeacherStatus.SUSPENDED))

    def test_activate_teacher(self, test_session: Session, created_teacher, created_admin_user):
        """测试激活教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        activated_teacher = service.activate_teacher(created_teacher["id"])

        assert activated_teacher.status == TeacherStatus.ACTIVE

    def test_deactivate_teacher(self, test_session: Session, created_teacher, created_admin_user):
        """测试停用教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 先激活教师
        service.activate_teacher(created_teacher["id"])

        # 再停用教师
        deactivated_teacher = service.deactivate_teacher(created_teacher["id"])

        assert deactivated_teacher.status == TeacherStatus.INACTIVE

    def test_suspend_teacher(self, test_session: Session, created_teacher, created_admin_user):
        """测试暂停教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 先激活教师
        service.activate_teacher(created_teacher["id"])

        # 再暂停教师
        suspended_teacher = service.suspend_teacher(created_teacher["id"], reason="测试暂停")

        assert suspended_teacher.status == TeacherStatus.SUSPENDED

    def test_status_management_nonexistent_teacher(self, test_session: Session, created_admin_user):
        """测试对不存在教师的状态管理操作"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        with pytest.raises(TeacherNotFoundError):
            service.activate_teacher(99999)

        with pytest.raises(TeacherNotFoundError):
            service.deactivate_teacher(99999)

        with pytest.raises(TeacherNotFoundError):
            service.suspend_teacher(99999)

    # 高级查询测试
    def test_get_teachers_by_priority(self, test_session: Session, multiple_teachers, created_admin_user):
        """测试按优先级获取教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 先激活所有教师（检查当前状态避免重复激活）
        for teacher in multiple_teachers:
            current_teacher = service.get_teacher(teacher["id"])
            if current_teacher.status != TeacherStatus.ACTIVE:
                service.activate_teacher(teacher["id"])

        priority_teachers = service.get_teachers_by_priority(limit=2)

        assert len(priority_teachers) <= 2
        assert all(teacher.status == TeacherStatus.ACTIVE for teacher in priority_teachers)
        # 验证按优先级排序
        if len(priority_teachers) > 1:
            assert priority_teachers[0].priority_level >= priority_teachers[1].priority_level

    def test_get_teachers_by_region_and_category(self, test_session: Session, multiple_teachers, created_admin_user):
        """测试按区域和分类获取教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 先激活所有教师（检查当前状态避免重复激活）
        for teacher in multiple_teachers:
            current_teacher = service.get_teacher(teacher["id"])
            if current_teacher.status != TeacherStatus.ACTIVE:
                service.activate_teacher(teacher["id"])

        teachers = service.get_teachers_by_region_and_category(
            region=TeacherRegion.EUROPE,
            category=TeacherCategory.EUROPEAN
        )

        assert all(teacher.region == TeacherRegion.EUROPE for teacher in teachers)
        assert all(teacher.teacher_category == TeacherCategory.EUROPEAN for teacher in teachers)
        assert all(teacher.status == TeacherStatus.ACTIVE for teacher in teachers)
        assert all(teacher.show_to_members == True for teacher in teachers)

    def test_search_teachers(self, test_session: Session, multiple_teachers, created_admin_user):
        """测试搜索教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 先激活所有教师（检查当前状态避免重复激活）
        for teacher in multiple_teachers:
            current_teacher = service.get_teacher(teacher["id"])
            if current_teacher.status != TeacherStatus.ACTIVE:
                service.activate_teacher(teacher["id"])

        # 搜索包含"教师"的教师
        teachers = service.search_teachers("教师", limit=10)

        assert all(teacher.status == TeacherStatus.ACTIVE for teacher in teachers)
        assert all("教师" in teacher.name or (teacher.introduction and "教师" in teacher.introduction) for teacher in teachers)

    def test_get_available_teachers_for_members(self, test_session: Session, multiple_teachers, created_admin_user):
        """测试获取对会员可见的教师"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 先激活所有教师（检查当前状态避免重复激活）
        for teacher in multiple_teachers:
            current_teacher = service.get_teacher(teacher["id"])
            if current_teacher.status != TeacherStatus.ACTIVE:
                service.activate_teacher(teacher["id"])

        available_teachers = service.get_available_teachers_for_members()

        assert all(teacher.status == TeacherStatus.ACTIVE for teacher in available_teachers)
        assert all(teacher.show_to_members == True for teacher in available_teachers)

    def test_get_teachers_statistics(self, test_session: Session, multiple_teachers, created_admin_user):
        """测试获取教师统计信息"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 激活部分教师（检查当前状态避免重复激活）
        first_teacher = service.get_teacher(multiple_teachers[0]["id"])
        if first_teacher.status != TeacherStatus.ACTIVE:
            service.activate_teacher(multiple_teachers[0]["id"])

        stats = service.get_teachers_statistics()

        assert "total_teachers" in stats
        assert "active_teachers" in stats
        assert "category_distribution" in stats
        assert "region_distribution" in stats
        assert stats["total_teachers"] == len(multiple_teachers)
        assert stats["active_teachers"] >= 1
        assert isinstance(stats["category_distribution"], dict)
        assert isinstance(stats["region_distribution"], dict)

    def test_get_teachers_with_tags(self, test_session: Session, created_teacher_with_tags, created_admin_user):
        """测试获取带标签信息的教师列表"""
        service = TeacherService(test_session, tenant_id=created_admin_user["tenant_id"])
        query_params = TeacherQuery(page=1, size=10)

        teachers_with_tags, total = service.get_teachers_with_tags(query_params)

        assert len(teachers_with_tags) >= 1
        assert total >= 1

        # 找到我们创建的教师
        target_teacher = None
        for teacher in teachers_with_tags:
            if teacher["id"] == created_teacher_with_tags["id"]:
                target_teacher = teacher
                break

        assert target_teacher is not None
        assert "tags" in target_teacher
        assert "tag_count" in target_teacher
        assert target_teacher["tag_count"] == len(target_teacher["tags"])
        assert target_teacher["tag_count"] > 0
