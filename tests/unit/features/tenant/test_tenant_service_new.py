import pytest
from datetime import datetime, timedelta, timezone
from sqlmodel import Session

from app.features.tenants.service import TenantService
from app.features.tenants.schemas import TenantCreate, TenantUpdate
from app.features.tenants.models import TenantStatus, PlanType
from app.features.tenants.exceptions import TenantNotFoundError, TenantBusinessException, PlanTemplateNotFoundError


class TestTenantService:
    """租户服务单元测试"""
    
    def test_create_tenant(self, test_session: Session, sample_tenant_data):
        """测试创建租户"""
        service = TenantService(test_session)
        tenant_create = TenantCreate(**sample_tenant_data)
        
        tenant = service.create_tenant(tenant_create, created_by="test_user")
        
        assert tenant.id is not None
        assert tenant.name == sample_tenant_data["name"]
        assert tenant.code == sample_tenant_data["code"]
        assert tenant.status == TenantStatus.TRIAL
        assert tenant.database_schema == f"tenant_{sample_tenant_data['code'].lower()}"
        assert tenant.api_key is not None
        assert len(tenant.api_key) == 32
        assert tenant.trial_expires_at > datetime.now()
        assert tenant.created_by == "test_user"
    
    def test_create_duplicate_tenant_code(self, test_session: Session, sample_tenant_data):
        """测试创建重复代码的租户"""
        service = TenantService(test_session)
        tenant_create = TenantCreate(**sample_tenant_data)
        
        # 创建第一个租户
        service.create_tenant(tenant_create)
        
        # 尝试创建相同代码的租户应该抛出业务异常
        with pytest.raises(TenantBusinessException) as exc_info:
            service.create_tenant(tenant_create)
        
        assert "已存在" in str(exc_info.value)
    
    def test_create_duplicate_contact_email(self, test_session: Session, sample_tenant_data):
        """测试创建重复联系邮箱的租户"""
        service = TenantService(test_session)
        tenant_create1 = TenantCreate(**sample_tenant_data)
        
        # 创建第一个租户
        service.create_tenant(tenant_create1)
        
        # 创建不同代码但相同邮箱的租户
        tenant_data2 = sample_tenant_data.copy()
        tenant_data2["code"] = "different_code"
        tenant_create2 = TenantCreate(**tenant_data2)
        
        # 应该抛出邮箱已存在异常
        with pytest.raises(TenantBusinessException) as exc_info:
            service.create_tenant(tenant_create2)
        
        assert "邮箱" in str(exc_info.value) and "已存在" in str(exc_info.value)
    
    def test_get_tenant(self, test_session: Session, sample_tenant_data):
        """测试获取租户"""
        service = TenantService(test_session)
        tenant_create = TenantCreate(**sample_tenant_data)
        
        created_tenant = service.create_tenant(tenant_create)
        retrieved_tenant = service.get_tenant(created_tenant.id)
        
        assert retrieved_tenant is not None
        assert retrieved_tenant.id == created_tenant.id
        assert retrieved_tenant.name == created_tenant.name
    
    def test_get_tenant_by_code(self, test_session: Session, sample_tenant_data):
        """测试根据代码获取租户"""
        service = TenantService(test_session)
        tenant_create = TenantCreate(**sample_tenant_data)
        
        created_tenant = service.create_tenant(tenant_create)
        retrieved_tenant = service.get_tenant_by_code(sample_tenant_data["code"])
        
        assert retrieved_tenant is not None
        assert retrieved_tenant.id == created_tenant.id
        assert retrieved_tenant.code == sample_tenant_data["code"]
    
    def test_get_nonexistent_tenant(self, test_session: Session):
        """测试获取不存在的租户"""
        service = TenantService(test_session)
        
        tenant = service.get_tenant(99999)
        assert tenant is None
        
        tenant_by_code = service.get_tenant_by_code("nonexistent")
        assert tenant_by_code is None
    
    def test_update_tenant(self, test_session: Session, sample_tenant_data):
        """测试更新租户"""
        service = TenantService(test_session)
        tenant_create = TenantCreate(**sample_tenant_data)
        
        created_tenant = service.create_tenant(tenant_create)
        created_tenant_update_time = created_tenant.updated_at
        
        # 添加1秒延迟以确保更新时间不同
        import time
        time.sleep(1)
        
        # 更新租户信息
        update_data = TenantUpdate(
            name="更新后的机构名称",
            contact_name="李四",
            contact_phone="13900139000"
        )
        
        updated_tenant = service.update_tenant(created_tenant.id, update_data)
        
        assert updated_tenant is not None
        assert updated_tenant.name == "更新后的机构名称"
        assert updated_tenant.contact_name == "李四"
        assert updated_tenant.contact_phone == "13900139000"
        assert updated_tenant.contact_email == sample_tenant_data["contact_email"]  # 未更新的字段保持不变
        assert updated_tenant.updated_at > created_tenant_update_time
    
    def test_update_nonexistent_tenant(self, test_session: Session):
        """测试更新不存在的租户"""
        service = TenantService(test_session)
        update_data = TenantUpdate(name="新名称")
        
        with pytest.raises(TenantNotFoundError):
            service.update_tenant(99999, update_data)
    
    def test_update_tenant_duplicate_email(self, test_session: Session, sample_tenant_data):
        """测试更新租户为重复的联系邮箱"""
        service = TenantService(test_session)
        
        # 创建两个租户
        tenant1_data = sample_tenant_data.copy()
        tenant1 = service.create_tenant(TenantCreate(**tenant1_data))
        
        tenant2_data = sample_tenant_data.copy()
        tenant2_data["code"] = "tenant2"
        tenant2_data["contact_email"] = "<EMAIL>"
        tenant2 = service.create_tenant(TenantCreate(**tenant2_data))
        
        # 尝试将 tenant2 的邮箱更新为 tenant1 的邮箱
        update_data = TenantUpdate(contact_email=tenant1.contact_email)
        
        with pytest.raises(TenantBusinessException) as exc_info:
            service.update_tenant(tenant2.id, update_data)
        
        assert "邮箱" in str(exc_info.value) and "已存在" in str(exc_info.value)
    
    def test_delete_tenant(self, test_session: Session, sample_tenant_data):
        """测试删除租户（软删除）"""
        service = TenantService(test_session)
        tenant_create = TenantCreate(**sample_tenant_data)
        
        created_tenant = service.create_tenant(tenant_create)
        
        # 删除租户（现在不返回值）
        service.delete_tenant(created_tenant.id)
        
        # 验证软删除
        deleted_tenant = service.get_tenant(created_tenant.id)
        assert deleted_tenant is not None
        assert deleted_tenant.status == TenantStatus.TERMINATED
    
    def test_delete_nonexistent_tenant(self, test_session: Session):
        """测试删除不存在的租户"""
        service = TenantService(test_session)
        
        with pytest.raises(TenantNotFoundError):
            service.delete_tenant(99999)
    
    def test_apply_plan_template(self, test_session: Session, sample_tenant_data, sample_plan_template):
        """测试应用套餐模板"""
        service = TenantService(test_session)
        tenant_create = TenantCreate(**sample_tenant_data)
        
        created_tenant = service.create_tenant(tenant_create)
        
        # 应用套餐模板
        updated_tenant = service.apply_plan_template(created_tenant.id, sample_plan_template.plan_code)
        
        assert updated_tenant is not None
        assert updated_tenant.plan_type == PlanType(sample_plan_template.plan_code)
        assert updated_tenant.max_teachers == sample_plan_template.max_teachers
        assert updated_tenant.max_members == sample_plan_template.max_members
        assert updated_tenant.max_storage_gb == sample_plan_template.max_storage_gb
        assert updated_tenant.monthly_fee == sample_plan_template.monthly_price
        assert updated_tenant.features == sample_plan_template.features
    
    def test_apply_plan_template_nonexistent_tenant(self, test_session: Session, sample_plan_template):
        """测试为不存在的租户应用套餐模板"""
        service = TenantService(test_session)
        
        with pytest.raises(TenantNotFoundError):
            service.apply_plan_template(99999, sample_plan_template.plan_code)
    
    def test_apply_nonexistent_plan_template(self, test_session: Session, sample_tenant_data):
        """测试应用不存在的套餐模板"""
        service = TenantService(test_session)
        tenant_create = TenantCreate(**sample_tenant_data)
        
        created_tenant = service.create_tenant(tenant_create)
        
        with pytest.raises(PlanTemplateNotFoundError) as exc_info:
            service.apply_plan_template(created_tenant.id, "nonexistent_plan")
    
    def test_activate_tenant(self, test_session: Session, sample_tenant_data):
        """测试激活租户"""
        service = TenantService(test_session)
        tenant_create = TenantCreate(**sample_tenant_data)
        
        created_tenant = service.create_tenant(tenant_create)
        
        # 激活租户
        activated_tenant = service.activate_tenant(created_tenant.id)
        
        assert activated_tenant is not None
        assert activated_tenant.status == TenantStatus.ACTIVE
    
    def test_activate_nonexistent_tenant(self, test_session: Session):
        """测试激活不存在的租户"""
        service = TenantService(test_session)
        
        with pytest.raises(TenantNotFoundError):
            service.activate_tenant(99999)
    
    def test_suspend_tenant(self, test_session: Session, sample_tenant_data):
        """测试暂停租户"""
        service = TenantService(test_session)
        tenant_create = TenantCreate(**sample_tenant_data)
        
        created_tenant = service.create_tenant(tenant_create)
        
        # 暂停租户
        suspended_tenant = service.suspend_tenant(created_tenant.id, reason="测试暂停")
        
        assert suspended_tenant is not None
        assert suspended_tenant.status == TenantStatus.SUSPENDED
    
    def test_suspend_nonexistent_tenant(self, test_session: Session):
        """测试暂停不存在的租户"""
        service = TenantService(test_session)
        
        with pytest.raises(TenantNotFoundError):
            service.suspend_tenant(99999, reason="测试")
    
    def test_get_tenants_pagination(self, test_session: Session):
        """测试租户列表分页"""
        service = TenantService(test_session)
        
        # 创建多个租户
        for i in range(5):
            tenant_data = {
                "name": f"测试机构{i}",
                "code": f"test_org_{i}",
                "contact_name": "张三",
                "contact_phone": "13800138000",
                "contact_email": f"test{i}@example.com",
                "address": "测试地址"
            }
            tenant_create = TenantCreate(**tenant_data)
            service.create_tenant(tenant_create)
        
        # 测试分页
        tenants_page1 = service.get_tenants(skip=0, limit=3)
        tenants_page2 = service.get_tenants(skip=3, limit=3)
        
        assert len(tenants_page1) == 3
        assert len(tenants_page2) == 2
        
        # 验证不重复
        page1_ids = {t.id for t in tenants_page1}
        page2_ids = {t.id for t in tenants_page2}
        assert len(page1_ids.intersection(page2_ids)) == 0 