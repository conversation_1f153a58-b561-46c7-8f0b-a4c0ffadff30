"""会员卡模板服务单元测试"""
import pytest
from sqlmodel import Session

from app.features.member_cards.template_service import MemberCardTemplateService
from app.features.member_cards.schemas import MemberCardTemplateCreate, MemberCardTemplateUpdate, MemberCardTemplateQuery
from app.features.member_cards.models import CardType
from app.features.member_cards.exceptions import MemberCardTemplateBusinessException, MemberCardTemplateNotFoundError


class TestMemberCardTemplateService:
    """会员卡模板服务单元测试"""
    
    def test_create_template_success(self, test_session: Session, sample_template_data, created_admin_user):
        """测试成功创建模板"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        template_create = MemberCardTemplateCreate(**sample_template_data)
        
        template = service.create_template(template_create, created_by=created_admin_user["id"])
        
        assert template.id is not None
        assert template.name == sample_template_data["name"]
        assert template.card_type.value == sample_template_data["card_type"]
        assert template.sale_price == sample_template_data["sale_price"]
        assert template.available_balance == sample_template_data["available_balance"]
        assert template.validity_days == sample_template_data["validity_days"]
        assert template.is_active is True
        assert template.tenant_id == created_admin_user["tenant_id"]
        assert template.created_by == created_admin_user["id"]
    
    def test_create_template_duplicate_name(self, test_session: Session, sample_template_data, created_admin_user):
        """测试创建重复名称模板失败"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        template_create = MemberCardTemplateCreate(**sample_template_data)
        
        # 先创建一个模板
        service.create_template(template_create, created_by=created_admin_user["id"])
        
        # 再次创建同名模板应该失败
        with pytest.raises(MemberCardTemplateBusinessException) as exc_info:
            service.create_template(template_create, created_by=created_admin_user["id"])
        assert "模板名称已存在" in exc_info.value.message
    
    def test_create_times_card_without_balance(self, test_session: Session, created_admin_user):
        """测试创建次卡但未设置可用次数失败"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        invalid_data = MemberCardTemplateCreate(
            name="无效次卡",
            card_type=CardType.TIMES_LIMITED,
            sale_price=1000,
            validity_days=90
            # 缺少 available_balance
        )
        
        with pytest.raises(MemberCardTemplateBusinessException) as exc_info:
            service.create_template(invalid_data, created_admin_user["id"])
        assert "次卡类型必须设置有效的可用次数" in exc_info.value.message
    
    def test_create_limited_card_without_validity(self, test_session: Session, created_admin_user):
        """测试创建有期限卡但未设置有效期失败"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        invalid_data = MemberCardTemplateCreate(
            name="无效有期限卡",
            card_type=CardType.VALUE_LIMITED,
            sale_price=1000,
            available_balance=1000
            # 缺少 validity_days
        )
        
        with pytest.raises(MemberCardTemplateBusinessException) as exc_info:
            service.create_template(invalid_data, created_admin_user["id"])
        assert "有期限卡片必须设置有效期天数" in exc_info.value.message
    
    def test_update_template_success(self, test_session: Session, created_template, created_admin_user):
        """测试成功更新模板"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        update_data = MemberCardTemplateUpdate(
            name="更新后的模板名称",
            sale_price=1200,
            description="更新后的描述"
        )
        
        updated_template = service.update_template(
            created_template["id"],
            update_data,
            created_admin_user["id"]
        )
        
        assert updated_template.name == "更新后的模板名称"
        assert updated_template.sale_price == 1200
        assert updated_template.description == "更新后的描述"
        # 其他字段保持不变
        assert updated_template.card_type.value == created_template["card_type"]
        assert updated_template.available_balance == created_template["available_balance"]
    
    def test_update_template_not_found(self, test_session: Session, created_admin_user):
        """测试更新不存在的模板失败"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        update_data = MemberCardTemplateUpdate(name="不存在的模板")
        
        with pytest.raises(MemberCardTemplateNotFoundError):
            service.update_template(99999, update_data, created_admin_user["id"])
    
    def test_get_template_success(self, test_session: Session, created_template, created_admin_user):
        """测试成功获取模板"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        template = service.get_template(created_template["id"])
        
        assert template is not None
        assert template.id == created_template["id"]
        assert template.name == created_template["name"]
    
    def test_get_template_not_found(self, test_session: Session, created_admin_user):
        """测试获取不存在的模板"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        template = service.get_template(99999)
        assert template is None
    
    def test_get_templates_with_pagination(self, test_session: Session, created_template, created_value_template, created_admin_user):
        """测试分页获取模板列表"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        query_params = MemberCardTemplateQuery(
            page=1,
            size=10,
            sort_by="created_at",
            sort_order="desc"
        )
        
        templates, total = service.get_templates(query_params)
        
        assert len(templates) >= 2  # 至少有创建的两个模板
        assert total >= 2
        assert templates[0].created_at >= templates[1].created_at  # 降序排列
    
    def test_get_templates_with_filter(self, test_session: Session, created_template, created_value_template, created_admin_user):
        """测试筛选获取模板列表"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        query_params = MemberCardTemplateQuery(
            card_type=CardType.TIMES_LIMITED,
            is_active=True
        )
        
        templates, total = service.get_templates(query_params)
        
        # 验证所有返回的模板都是次卡类型且激活状态
        for template in templates:
            assert template.card_type == CardType.TIMES_LIMITED
            assert template.is_active is True
    
    def test_get_active_templates(self, test_session: Session, created_template, created_value_template, created_admin_user):
        """测试获取激活的模板"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        active_templates = service.get_active_templates()
        
        # 验证所有返回的模板都是激活状态
        for template in active_templates:
            assert template.is_active is True
    
    def test_get_active_templates_by_type(self, test_session: Session, created_template, created_admin_user):
        """测试按类型获取激活的模板"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        times_templates = service.get_active_templates(card_type=CardType.TIMES_LIMITED)
        
        # 验证所有返回的模板都是次卡类型且激活状态
        for template in times_templates:
            assert template.card_type == CardType.TIMES_LIMITED
            assert template.is_active is True
    
    def test_activate_template(self, test_session: Session, created_template, created_admin_user):
        """测试激活模板"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 先停用模板
        service.deactivate_template(created_template["id"], created_admin_user["id"])
        
        # 再激活模板
        activated_template = service.activate_template(created_template["id"], created_admin_user["id"])
        
        assert activated_template.is_active is True
    
    def test_deactivate_template(self, test_session: Session, created_template, created_admin_user):
        """测试停用模板"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        deactivated_template = service.deactivate_template(created_template["id"], created_admin_user["id"])
        
        assert deactivated_template.is_active is False
    
    def test_get_template_statistics(self, test_session: Session, created_template, created_value_template, created_admin_user):
        """测试获取模板统计信息"""
        service = MemberCardTemplateService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        stats = service.get_template_statistics()
        
        assert "total_templates" in stats
        assert "active_templates" in stats
        assert "inactive_templates" in stats
        assert "type_statistics" in stats
        
        assert stats["total_templates"] >= 2  # 至少有创建的两个模板
        assert stats["active_templates"] <= stats["total_templates"]
        assert stats["inactive_templates"] >= 0
        
        # 验证类型统计
        type_stats = stats["type_statistics"]
        for card_type in CardType:
            assert card_type.value in type_stats
            assert isinstance(type_stats[card_type.value], int)
