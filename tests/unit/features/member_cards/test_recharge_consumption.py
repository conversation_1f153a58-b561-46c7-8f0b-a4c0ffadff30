"""会员卡充值和扣费服务单元测试"""
import pytest
from sqlmodel import Session

from app.features.member_cards.recharge_service import RechargeService
from app.features.member_cards.consumption_service import ConsumptionService
from app.features.member_cards.models import CardStatus, PaymentMethod
from app.features.member_cards.schemas import (
    RechargeRequest, ConsumptionRequest, BalanceCheckRequest,
    CourseBookingBalanceCheck, CourseBookingDeduction
)
from app.features.member_cards.exceptions import (
    MemberCardNotFoundError, MemberCardBusinessException,
    RechargeBusinessException, ConsumptionBusinessException
)


class TestRechargeService:
    """充值服务单元测试"""
    
    def test_recharge_success(self, test_session: Session, created_value_card, created_admin_user):
        """测试成功充值"""
        service = RechargeService(test_session, tenant_id=created_admin_user["tenant_id"])
        initial_balance = created_value_card["balance"]
        
        recharge_request = RechargeRequest(
            member_card_id=created_value_card["id"],
            amount=500,
            bonus_amount=50,
            payment_method=PaymentMethod.WECHAT,
            notes="测试充值"
        )
        
        response = service.recharge(recharge_request, created_admin_user["id"])
        
        # 验证响应
        assert response.operation_id is not None
        assert response.member_card_id == created_value_card["id"]
        assert response.amount == 500
        assert response.bonus_amount == 50
        assert response.total_amount == 550
        assert response.balance_before == initial_balance
        assert response.balance_after == initial_balance + 550
        assert response.payment_method == PaymentMethod.WECHAT
        assert response.transaction_id is not None
        
        # 验证数据库中的卡片余额已更新
        from app.features.member_cards.models import MemberCard
        updated_card = test_session.get(MemberCard, created_value_card["id"])
        assert updated_card.balance == initial_balance + 550
        assert updated_card.total_recharged == initial_balance + 550
    
    def test_recharge_card_not_found(self, test_session: Session, created_admin_user):
        """测试充值不存在的卡片失败"""
        service = RechargeService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        recharge_request = RechargeRequest(
            member_card_id=99999,
            amount=500,
            payment_method=PaymentMethod.WECHAT
        )
        
        with pytest.raises(MemberCardNotFoundError):
            service.recharge(recharge_request, created_admin_user["id"])
    
    def test_recharge_amount_too_small(self, test_session: Session, created_value_card, created_admin_user):
        """测试充值金额过小失败"""
        service = RechargeService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建一个绕过Pydantic验证的请求
        recharge_request = RechargeRequest(
            member_card_id=created_value_card["id"],
            amount=1,  # 先设置有效值
            payment_method=PaymentMethod.WECHAT
        )
        # 然后手动设置无效值
        recharge_request.amount = 0
        
        with pytest.raises(RechargeBusinessException, match="充值金额过小"):
            service.recharge(recharge_request, created_admin_user["id"])
    
    def test_recharge_bonus_amount_too_large(self, test_session: Session, created_value_card, created_admin_user):
        """测试赠送金额过大失败"""
        service = RechargeService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        recharge_request = RechargeRequest(
            member_card_id=created_value_card["id"],
            amount=100,
            bonus_amount=100,  # 赠送金额等于充值金额，超过50%限制
            payment_method=PaymentMethod.WECHAT
        )
        
        with pytest.raises(MemberCardBusinessException, match="赠送金额不能超过"):
            service.recharge(recharge_request, created_admin_user["id"])
    
    def test_get_recharge_history(self, test_session: Session, created_recharge_operation, created_admin_user):
        """测试获取充值历史"""
        service = RechargeService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        history = service.get_recharge_history(created_recharge_operation["member_card_id"])
        
        assert len(history) > 0
        assert history[0].id == created_recharge_operation["id"]
        assert history[0].operation_type.value == "recharge"


class TestConsumptionService:
    """扣费服务单元测试"""
    
    def test_check_balance_sufficient(self, test_session: Session, created_value_card, created_admin_user):
        """测试余额充足检查"""
        service = ConsumptionService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        request = BalanceCheckRequest(
            member_card_id=created_value_card["id"],
            required_amount=500
        )
        
        response = service.check_balance(request)
        
        assert response.member_card_id == created_value_card["id"]
        assert response.current_balance == created_value_card["balance"]
        assert response.required_amount == 500
        assert response.is_sufficient is True
        assert response.shortage_amount == 0
    
    def test_check_balance_insufficient(self, test_session: Session, created_value_card, created_admin_user):
        """测试余额不足检查"""
        service = ConsumptionService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        request = BalanceCheckRequest(
            member_card_id=created_value_card["id"],
            required_amount=2000  # 超过卡片余额
        )
        
        response = service.check_balance(request)
        
        assert response.is_sufficient is False
        assert response.shortage_amount == 2000 - created_value_card["balance"]
    
    def test_consume_success(self, test_session: Session, created_value_card, created_admin_user):
        """测试成功扣费"""
        service = ConsumptionService(test_session, tenant_id=created_admin_user["tenant_id"])
        initial_balance = created_value_card["balance"]
        
        consumption_request = ConsumptionRequest(
            member_card_id=created_value_card["id"],
            amount=100,
            operation_description="课程扣费",
            reason="测试扣费"
        )
        
        response = service.consume(consumption_request, created_admin_user["id"])
        
        # 验证响应
        assert response.operation_id is not None
        assert response.member_card_id == created_value_card["id"]
        assert response.amount == 100
        assert response.balance_before == initial_balance
        assert response.balance_after == initial_balance - 100
        
        # 验证数据库中的卡片余额已更新
        from app.features.member_cards.models import MemberCard
        updated_card = test_session.get(MemberCard, created_value_card["id"])
        assert updated_card.balance == initial_balance - 100
        assert updated_card.total_consumed == 100
    
    def test_consume_insufficient_balance(self, test_session: Session, created_value_card, created_admin_user):
        """测试余额不足扣费失败"""
        service = ConsumptionService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        consumption_request = ConsumptionRequest(
            member_card_id=created_value_card["id"],
            amount=2000,  # 超过卡片余额
            operation_description="课程扣费"
        )
        
        with pytest.raises(ConsumptionBusinessException, match="余额不足"):
            service.consume(consumption_request, created_admin_user["id"])
    
    def test_course_booking_balance_check_success(self, test_session: Session, created_value_card, created_member, created_admin_user):
        """测试课程预约余额检查成功"""
        service = ConsumptionService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        request = CourseBookingBalanceCheck(
            member_id=created_member["id"],
            course_price=100,
            preferred_card_id=created_value_card["id"]
        )
        
        response = service.check_course_booking_balance(request)
        
        assert response.can_book is True
        assert response.selected_card_id == created_value_card["id"]
        assert response.current_balance == created_value_card["balance"]
        assert response.required_amount == 100
        assert response.shortage_amount == 0
        assert len(response.available_cards) > 0
    
    
    def test_refund_success(self, test_session: Session, created_consumption_operation, created_admin_user):
        """测试成功退费"""
        service = ConsumptionService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 获取原始操作对应的卡片
        from app.features.member_cards.models import MemberCard
        card = test_session.get(MemberCard, created_consumption_operation["member_card_id"])
        initial_balance = card.balance
        
        response = service.refund(
            original_operation_id=created_consumption_operation["id"],
            refund_amount=50,  # 部分退费
            reason="测试退费",
            operator_id=created_admin_user["id"]
        )
        
        assert response.amount == 50
        assert response.balance_after == initial_balance + 50
        
        # 验证卡片余额已更新
        test_session.refresh(card)
        assert card.balance == initial_balance + 50
    
    def test_refund_amount_invalid(self, test_session: Session, created_consumption_operation, created_admin_user):
        """测试无效退费金额失败"""
        service = ConsumptionService(test_session, tenant_id=created_admin_user["tenant_id"])
        original_amount = abs(created_consumption_operation["amount_change"])
        
        with pytest.raises(ConsumptionBusinessException, match="退款金额无效"):
            service.refund(
                original_operation_id=created_consumption_operation["id"],
                refund_amount=original_amount + 100,  # 超过原始金额
                reason="测试退费",
                operator_id=created_admin_user["id"]
            )
    
    def test_get_consumption_history(self, test_session: Session, created_consumption_operation, created_admin_user):
        """测试获取消费历史"""
        service = ConsumptionService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        history = service.get_consumption_history(created_consumption_operation["member_card_id"])
        
        assert len(history) > 0
        assert history[0].id == created_consumption_operation["id"]
