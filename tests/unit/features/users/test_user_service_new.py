import pytest
from datetime import datetime, timezone
from sqlmodel import Session

from app.features.users.service import UserService
from app.features.users.schemas import UserCreate, UserUpdate, UserSearchParams
from app.features.users.models import UserRole, UserStatus
from app.api.common.exceptions import BusinessException
from app.features.users.exceptions import UserBusinessException, UserNotFoundError


class TestUserService:
    """用户服务单元测试"""
    
    def test_create_user(self, test_session: Session, sample_user_data, created_admin_user):
        """测试创建用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        user_create = UserCreate(**sample_user_data)
        
        user = service.create_user(user_create, created_by=created_admin_user["id"])
        
        assert user.id is not None
        assert user.username == sample_user_data["username"]
        assert user.real_name == sample_user_data["real_name"]
        assert user.role == sample_user_data["role"]
        assert user.status == UserStatus.ACTIVE
        assert user.tenant_id == created_admin_user["tenant_id"]
        assert user.created_by == created_admin_user["id"]
        assert user.password_hash is not None
        assert user.password_hash != sample_user_data["password"]  # 应该是哈希值
    
    def test_create_super_admin_user(self, test_session: Session, super_admin_user_data):
        """测试创建超级管理员用户"""
        service = UserService(test_session, tenant_id=None)  # 超级管理员没有租户ID
        user_create = UserCreate(**super_admin_user_data)
        
        user = service.create_user(user_create)
        
        assert user.id is not None
        assert user.username == super_admin_user_data["username"]
        assert user.role == UserRole.SUPER_ADMIN
        assert user.tenant_id is None  # 超级管理员没有租户ID
    
    def test_create_duplicate_username(self, test_session: Session, sample_user_data, created_admin_user):
        """测试创建重复用户名"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        user_create = UserCreate(**sample_user_data)
        
        # 创建第一个用户
        service.create_user(user_create, created_by=created_admin_user["id"])
        
        # 尝试创建相同用户名的用户应该抛出异常
        with pytest.raises(UserBusinessException) as exc_info:
            service.create_user(user_create)
        
        assert "已存在" in str(exc_info.value)
        assert exc_info.value.code.value == "USER_USERNAME_EXISTS"
    
    def test_create_duplicate_email(self, test_session: Session, sample_user_data, created_admin_user):
        """测试创建重复邮箱"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建第一个用户
        user_create1 = UserCreate(**sample_user_data)
        service.create_user(user_create1, created_by=created_admin_user["id"])
        
        # 尝试创建相同邮箱的用户
        user_data2 = sample_user_data.copy()
        user_data2["username"] = "different_username"
        user_create2 = UserCreate(**user_data2)
        
        with pytest.raises(UserBusinessException) as exc_info:
            service.create_user(user_create2)
        
        assert "已存在" in str(exc_info.value)
        assert exc_info.value.code.value == "USER_EMAIL_EXISTS"
    
    def test_get_user(self, test_session: Session, sample_user_data, created_admin_user):
        """测试获取用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        user_create = UserCreate(**sample_user_data)
        
        created_user = service.create_user(user_create, created_by=created_admin_user["id"])
        retrieved_user = service.get_user(created_user.id)
        
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
        assert retrieved_user.username == created_user.username
    
    def test_get_user_by_username(self, test_session: Session, sample_user_data, created_admin_user):
        """测试根据用户名获取用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        user_create = UserCreate(**sample_user_data)
        
        created_user = service.create_user(user_create, created_by=created_admin_user["id"])
        retrieved_user = service.get_user_by_username(sample_user_data["username"])
        
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
        assert retrieved_user.username == sample_user_data["username"]
    
    def test_get_user_by_email(self, test_session: Session, sample_user_data, created_admin_user):
        """测试根据邮箱获取用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        user_create = UserCreate(**sample_user_data)
        
        created_user = service.create_user(user_create, created_by=created_admin_user["id"])
        retrieved_user = service.get_user_by_email(sample_user_data["email"])
        
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
        assert retrieved_user.email == sample_user_data["email"]
    
    def test_get_nonexistent_user(self, test_session: Session, created_admin_user):
        """测试获取不存在的用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        user = service.get_user(99999)
        assert user is None
        
        user_by_username = service.get_user_by_username("nonexistent")
        assert user_by_username is None
        
        user_by_email = service.get_user_by_email("<EMAIL>")
        assert user_by_email is None
    
    def test_get_users_with_pagination(self, test_session: Session, created_admin_user):
        """测试用户列表分页（新方法）"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建多个用户
        for i in range(5):
            user_data = {
                "username": f"testuser{i}",
                "password": "password123",
                "real_name": f"测试用户{i}",
                "role": UserRole.AGENT,
                "email": f"user{i}@test.com"
            }
            user_create = UserCreate(**user_data)
            service.create_user(user_create, created_by=created_admin_user["id"])
        
        # 测试分页
        params = UserSearchParams(skip=0, limit=3)
        result = service.get_users_with_pagination(params)
        
        assert result.total == 5+1 # 1是created_admin_user
        assert len(result.items) == 3
        assert result.skip == 0
        assert result.limit == 3
        
        # 测试第二页
        params2 = UserSearchParams(skip=3, limit=3)
        result2 = service.get_users_with_pagination(params2)
        
        assert result2.total == 5+1 # 1是created_admin_user
        assert len(result2.items) == 2+1 # 1是created_admin_user
        assert result2.skip == 3
        assert result2.limit == 3
    
    def test_search_users(self, test_session: Session, created_admin_user):
        """测试用户搜索功能"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建测试用户
        users_data = [
            {"username": "alice", "real_name": "Alice Smith", "email": "<EMAIL>"},
            {"username": "bob", "real_name": "Bob Johnson", "email": "<EMAIL>"},
            {"username": "charlie", "real_name": "Charlie Brown", "email": "<EMAIL>"},
        ]
        
        for user_data in users_data:
            full_data = {
                "password": "password123",
                "role": UserRole.AGENT,
                **user_data
            }
            user_create = UserCreate(**full_data)
            service.create_user(user_create, created_by=created_admin_user["id"])
        
        # 测试按用户名搜索
        params = UserSearchParams(search="alice")
        result = service.get_users_with_pagination(params)
        assert len(result.items) == 1
        assert result.items[0].username == "alice"
        
        # 测试按真实姓名搜索
        params = UserSearchParams(search="Smith")
        result = service.get_users_with_pagination(params)
        assert len(result.items) == 1
        assert result.items[0].real_name == "Alice Smith"
        
        # 测试按邮箱搜索
        params = UserSearchParams(search="<EMAIL>")
        result = service.get_users_with_pagination(params)
        assert len(result.items) == 1
        assert result.items[0].email == "<EMAIL>"

    def test_update_user(self, test_session: Session, sample_user_data, created_admin_user):
        """测试更新用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        user_create = UserCreate(**sample_user_data)

        created_user = service.create_user(user_create, created_by=created_admin_user["id"])
        created_user_update_time = created_user.updated_at

        # 添加1秒延迟以确保更新时间不同
        import time
        time.sleep(1)

        # 更新用户信息
        update_data = UserUpdate(
            real_name="更新后的姓名",
            email="<EMAIL>",
            phone="13900139000"
        )

        updated_user = service.update_user(created_user.id, update_data)

        assert updated_user is not None
        assert updated_user.real_name == "更新后的姓名"
        assert updated_user.email == "<EMAIL>"
        assert updated_user.phone == "13900139000"
        assert updated_user.username == sample_user_data["username"]  # 未更新的字段保持不变
        assert updated_user.updated_at > created_user_update_time

    def test_update_nonexistent_user(self, test_session: Session, created_admin_user):
        """测试更新不存在的用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        update_data = UserUpdate(real_name="测试更新")
        
        with pytest.raises(UserNotFoundError):
            service.update_user(99999, update_data)

    def test_update_user_duplicate_email(self, test_session: Session, sample_user_data, created_admin_user):
        """测试更新用户时邮箱重复"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建两个用户
        user1_create = UserCreate(**sample_user_data)
        user1 = service.create_user(user1_create, created_by=created_admin_user["id"])
        
        user2_data = sample_user_data.copy()
        user2_data["username"] = "user2"
        user2_data["email"] = "<EMAIL>"
        user2_create = UserCreate(**user2_data)
        user2 = service.create_user(user2_create, created_by=created_admin_user["id"])
        
        # 尝试将user2的邮箱更新为user1的邮箱
        update_data = UserUpdate(email=sample_user_data["email"])
        
        with pytest.raises(UserBusinessException) as exc_info:
            service.update_user(user2.id, update_data)
        
        assert exc_info.value.code.value == "USER_EMAIL_EXISTS"

    def test_change_password(self, test_session: Session, sample_user_data, created_admin_user):
        """测试修改密码"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        user_create = UserCreate(**sample_user_data)

        created_user = service.create_user(user_create, created_by=created_admin_user["id"])
        old_password_hash = created_user.password_hash

        # 修改密码
        updated_user = service.change_password(
            created_user.id,
            sample_user_data["password"],
            "newpassword123"
        )

        assert updated_user is not None
        assert updated_user.password_hash != old_password_hash

    def test_change_password_nonexistent_user(self, test_session: Session, created_admin_user):
        """测试修改不存在用户的密码"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        with pytest.raises(UserNotFoundError):
            service.change_password(99999, "oldpass", "newpass")

    def test_change_password_wrong_old_password(self, test_session: Session, sample_user_data, created_admin_user):
        """测试修改密码时原密码错误"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        user_create = UserCreate(**sample_user_data)
        created_user = service.create_user(user_create, created_by=created_admin_user["id"])

        with pytest.raises(UserBusinessException) as exc_info:
            service.change_password(created_user.id, "wrongpassword", "newpass")
        
        assert exc_info.value.code.value == "USER_INVALID_PASSWORD"

    def test_reset_password(self, test_session: Session, sample_user_data, created_admin_user):
        """测试重置密码"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        user_create = UserCreate(**sample_user_data)
        created_user = service.create_user(user_create, created_by=created_admin_user["id"])
        old_password_hash = created_user.password_hash

        # 重置密码
        updated_user = service.reset_password(created_user.id, "resetpassword123")

        assert updated_user is not None
        assert updated_user.password_hash != old_password_hash

    def test_reset_password_nonexistent_user(self, test_session: Session, created_admin_user):
        """测试重置不存在用户的密码"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        with pytest.raises(UserNotFoundError):
            service.reset_password(99999, "newpass")

    def test_activate_user(self, test_session: Session, sample_user_data, created_admin_user):
        """测试激活用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        user_create = UserCreate(**sample_user_data)

        created_user = service.create_user(user_create, created_by=created_admin_user["id"])

        # 先停用用户
        service.deactivate_user(created_user.id)

        # 激活用户
        activated_user = service.activate_user(created_user.id)

        assert activated_user is not None
        assert activated_user.status == UserStatus.ACTIVE

    def test_activate_nonexistent_user(self, test_session: Session, created_admin_user):
        """测试激活不存在的用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        with pytest.raises(UserNotFoundError):
            service.activate_user(99999)

    def test_deactivate_user(self, test_session: Session, sample_user_data, created_admin_user):
        """测试停用用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        user_create = UserCreate(**sample_user_data)

        created_user = service.create_user(user_create, created_by=created_admin_user["id"])

        # 停用用户
        deactivated_user = service.deactivate_user(created_user.id)

        assert deactivated_user is not None
        assert deactivated_user.status == UserStatus.INACTIVE

    def test_deactivate_nonexistent_user(self, test_session: Session, created_admin_user):
        """测试停用不存在的用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        with pytest.raises(UserNotFoundError):
            service.deactivate_user(99999)

    def test_lock_user(self, test_session: Session, sample_user_data, created_admin_user):
        """测试锁定用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        user_create = UserCreate(**sample_user_data)
        created_user = service.create_user(user_create, created_by=created_admin_user["id"])

        # 锁定用户
        lock_until = datetime.now().replace(microsecond=0)
        locked_user = service.lock_user(created_user.id, lock_until)

        assert locked_user is not None
        assert locked_user.status == UserStatus.LOCKED
        assert locked_user.locked_until is not None

    def test_lock_nonexistent_user(self, test_session: Session, created_admin_user):
        """测试锁定不存在的用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        with pytest.raises(UserNotFoundError):
            service.lock_user(99999)

    def test_delete_user(self, test_session: Session, sample_user_data, created_admin_user):
        """测试删除用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        user_create = UserCreate(**sample_user_data)

        created_user = service.create_user(user_create, created_by=created_admin_user["id"])

        # 删除用户
        service.delete_user(created_user.id)

        # 验证用户已被删除
        deleted_user = service.get_user(created_user.id)
        assert deleted_user is None

    def test_delete_nonexistent_user(self, test_session: Session, created_admin_user):
        """测试删除不存在的用户"""
        service = UserService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        with pytest.raises(UserNotFoundError):
            service.delete_user(99999)
