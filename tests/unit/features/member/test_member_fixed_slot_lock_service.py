"""会员固定课位锁定服务层单元测试"""
import pytest
from sqlmodel import Session

from app.features.members.fixed_lock_service import MemberFixedSlotLockService
from app.features.members.fixed_lock_schemas import (
    MemberFixedSlotLockCreate, MemberFixedSlotLockUpdate, MemberFixedSlotLockQuery,
    MemberFixedSlotLockBatchCreate, MemberFixedSlotLockBatchUpdate, MemberFixedSlotLockBatchDelete,
    AvailableSlotQuery, LockConflictCheck
)
from app.features.members.fixed_lock_models import MemberFixedSlotLockStatus
from app.features.members.fixed_lock_exceptions import (
    MemberFixedSlotLockNotFoundError, MemberFixedSlotLockBusinessException
)


class TestMemberFixedSlotLockService:
    """会员固定课位锁定服务测试类"""

    def test_create_lock_success(self, test_session: Session, created_member, created_fixed_slot, created_admin_user):
        """测试成功创建锁定记录"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        lock_data = MemberFixedSlotLockCreate(
            member_id=created_member["id"],
            teacher_fixed_slot_id=created_fixed_slot["id"],
            status=MemberFixedSlotLockStatus.ACTIVE,
            created_by=created_admin_user["id"]
        )
        
        lock = service.create_lock(lock_data, created_admin_user["id"])
        
        assert lock is not None
        assert lock.member_id == created_member["id"]
        assert lock.teacher_fixed_slot_id == created_fixed_slot["id"]
        assert lock.teacher_id == created_fixed_slot["teacher_id"]
        assert lock.weekday == created_fixed_slot["weekday"]
        assert lock.start_time == created_fixed_slot["start_time"]
        assert lock.status == MemberFixedSlotLockStatus.ACTIVE
        assert lock.tenant_id == created_admin_user["tenant_id"]
        assert lock.created_by == created_admin_user["id"]

    def test_create_lock_member_not_found(self, test_session: Session, created_fixed_slot, created_admin_user):
        """测试创建锁定时会员不存在"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        lock_data = MemberFixedSlotLockCreate(
            member_id=99999,  # 不存在的会员ID
            teacher_fixed_slot_id=created_fixed_slot["id"],
            status=MemberFixedSlotLockStatus.ACTIVE,
            created_by=created_admin_user["id"]
        )
        
        with pytest.raises(MemberFixedSlotLockBusinessException) as exc_info:
            service.create_lock(lock_data, created_admin_user["id"])
        
        assert "会员不存在" in str(exc_info.value)

    def test_create_lock_teacher_slot_not_found(self, test_session: Session, created_member, created_admin_user):
        """测试创建锁定时教师时间段不存在"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        lock_data = MemberFixedSlotLockCreate(
            member_id=created_member["id"],
            teacher_fixed_slot_id=99999,  # 不存在的时间段ID
            status=MemberFixedSlotLockStatus.ACTIVE,
            created_by=created_admin_user["id"]
        )
        
        with pytest.raises(MemberFixedSlotLockBusinessException) as exc_info:
            service.create_lock(lock_data, created_admin_user["id"])
        
        assert "教师固定时间段不存在" in str(exc_info.value)

    def test_create_lock_slot_already_locked(self, test_session: Session, created_lock, created_member_2, created_admin_user):
        """测试创建锁定时时间段已被锁定"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        # 尝试让另一个会员锁定同一个时间段
        lock_data = MemberFixedSlotLockCreate(
            member_id=created_member_2["id"],
            teacher_fixed_slot_id=created_lock["teacher_fixed_slot_id"],
            status=MemberFixedSlotLockStatus.ACTIVE,
            created_by=created_admin_user["id"]
        )
        
        with pytest.raises(MemberFixedSlotLockBusinessException) as exc_info:
            service.create_lock(lock_data, created_admin_user["id"])
        
        assert "已被会员" in str(exc_info.value) and "锁定" in str(exc_info.value)

    def test_get_lock_success(self, test_session: Session, created_lock, created_admin_user):
        """测试成功获取锁定记录"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        lock = service.get_lock(created_lock["id"])
        
        assert lock is not None
        assert lock.id == created_lock["id"]
        assert lock.member_id == created_lock["member_id"]
        assert lock.teacher_fixed_slot_id == created_lock["teacher_fixed_slot_id"]

    def test_get_lock_not_found(self, test_session: Session, created_admin_user):
        """测试获取不存在的锁定记录"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        lock = service.get_lock(99999)
        
        assert lock is None

    def test_get_locks_with_query(self, test_session: Session, created_multiple_locks, created_admin_user):
        """测试查询锁定记录列表"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        query = MemberFixedSlotLockQuery(
            member_id=created_multiple_locks[0]["member_id"],
            status=MemberFixedSlotLockStatus.ACTIVE,
            page=1,
            size=10
        )
        
        locks, total = service.get_locks(query)
        
        assert len(locks) > 0
        assert total > 0
        for lock in locks:
            assert lock.member_id == created_multiple_locks[0]["member_id"]
            assert lock.status == MemberFixedSlotLockStatus.ACTIVE

    def test_update_lock_success(self, test_session: Session, created_lock, created_admin_user):
        """测试成功更新锁定记录"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        update_data = MemberFixedSlotLockUpdate(
            status=MemberFixedSlotLockStatus.PAUSED
        )
        
        updated_lock = service.update_lock(created_lock["id"], update_data)
        
        assert updated_lock is not None
        assert updated_lock.status == MemberFixedSlotLockStatus.PAUSED
        assert updated_lock.id == created_lock["id"]

    def test_update_lock_not_found(self, test_session: Session, created_admin_user):
        """测试更新不存在的锁定记录"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        update_data = MemberFixedSlotLockUpdate(
            status=MemberFixedSlotLockStatus.PAUSED
        )
        
        with pytest.raises(MemberFixedSlotLockNotFoundError):
            service.update_lock(99999, update_data)

    def test_delete_lock_success(self, test_session: Session, created_lock, created_admin_user):
        """测试成功删除锁定记录"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        result = service.delete_lock(created_lock["id"])
        
        assert result is True
        
        # 验证记录已被删除
        deleted_lock = service.get_lock(created_lock["id"])
        assert deleted_lock is None

    def test_delete_lock_not_found(self, test_session: Session, created_admin_user):
        """测试删除不存在的锁定记录"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        result = service.delete_lock(99999)
        
        assert result is False

    def test_check_lock_conflict_no_conflict(self, test_session: Session, created_fixed_slot, created_member, created_admin_user):
        """测试检查锁定冲突 - 无冲突"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        conflict_check = LockConflictCheck(
            member_id=created_member["id"],
            teacher_fixed_slot_id=created_fixed_slot["id"]
        )
        
        result = service.check_lock_conflict(conflict_check)
        
        assert result.has_conflict is False

    def test_check_lock_conflict_has_conflict(self, test_session: Session, created_lock, created_member_2, created_admin_user):
        """测试检查锁定冲突 - 有冲突"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        conflict_check = LockConflictCheck(
            member_id=created_member_2["id"],
            teacher_fixed_slot_id=created_lock["teacher_fixed_slot_id"]
        )
        
        result = service.check_lock_conflict(conflict_check)
        
        assert result.has_conflict is True
        assert result.conflict_type == "slot_already_locked"
        assert result.conflicting_lock_id == created_lock["id"]

    def test_get_available_slots(self, test_session: Session, created_multiple_fixed_slots, created_admin_user):
        """测试获取可用时间段"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        query = AvailableSlotQuery(
            teacher_id=created_multiple_fixed_slots[0]["teacher_id"],
            only_available=True,
            only_visible=True,
            exclude_locked=True
        )
        
        available_slots = service.get_available_slots(query)
        
        assert len(available_slots) > 0
        for slot in available_slots:
            assert slot.teacher_id == created_multiple_fixed_slots[0]["teacher_id"]
            assert slot.is_available is True
            assert slot.is_visible_to_members is True

    def test_get_member_locks(self, test_session: Session, created_multiple_locks, created_admin_user):
        """测试获取会员的锁定记录"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        member_id = created_multiple_locks[0]["member_id"]
        locks = service.get_member_locks(member_id, MemberFixedSlotLockStatus.ACTIVE)
        
        assert len(locks) > 0
        for lock in locks:
            assert lock.member_id == member_id
            assert lock.status == MemberFixedSlotLockStatus.ACTIVE

    def test_get_teacher_slot_locks(self, test_session: Session, created_multiple_locks, created_admin_user):
        """测试获取教师时间段的锁定情况"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
        
        teacher_id = created_multiple_locks[0]["teacher_id"]
        locks = service.get_teacher_slot_locks(teacher_id)
        
        assert len(locks) > 0
        for lock in locks:
            assert lock.teacher_id == teacher_id


class TestMemberFixedSlotLockBatchOperations:
    """会员固定课位锁定批量操作测试类"""

    def test_batch_create_locks_success(self, test_session: Session, created_member, created_multiple_fixed_slots, created_admin_user):
        """测试批量创建锁定记录成功"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])

        # 选择可用且可见的时间段
        available_slots = [
            slot for slot in created_multiple_fixed_slots
            if slot["is_available"] and slot["is_visible_to_members"]
        ]
        slot_ids = [slot["id"] for slot in available_slots[:3]]

        batch_data = MemberFixedSlotLockBatchCreate(
            member_id=created_member["id"],
            teacher_fixed_slot_ids=slot_ids,
            status=MemberFixedSlotLockStatus.ACTIVE
        )

        created_locks = service.batch_create_locks(batch_data, created_admin_user["id"])

        assert len(created_locks) == len(slot_ids)
        for lock in created_locks:
            assert lock.member_id == created_member["id"]
            assert lock.status == MemberFixedSlotLockStatus.ACTIVE
            assert lock.teacher_fixed_slot_id in slot_ids

    def test_batch_create_locks_with_invalid_slots(self, test_session: Session, created_member, created_admin_user):
        """测试批量创建锁定记录包含无效时间段"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])

        # 包含不存在的时间段ID
        slot_ids = [99999, 99998]

        batch_data = MemberFixedSlotLockBatchCreate(
            member_id=created_member["id"],
            teacher_fixed_slot_ids=slot_ids,
            status=MemberFixedSlotLockStatus.ACTIVE
        )

        created_locks = service.batch_create_locks(batch_data, created_admin_user["id"])

        # 应该跳过无效的时间段，返回空列表
        assert len(created_locks) == 0

    def test_batch_update_locks_success(self, test_session: Session, created_multiple_locks, created_admin_user):
        """测试批量更新锁定记录成功"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])

        lock_ids = [lock["id"] for lock in created_multiple_locks[:2]]

        batch_data = MemberFixedSlotLockBatchUpdate(
            lock_ids=lock_ids,
            status=MemberFixedSlotLockStatus.PAUSED
        )

        updated_locks = service.batch_update_locks(batch_data)

        assert len(updated_locks) == len(lock_ids)
        for lock in updated_locks:
            assert lock.status == MemberFixedSlotLockStatus.PAUSED
            assert lock.id in lock_ids

    def test_batch_delete_locks_success(self, test_session: Session, created_multiple_locks, created_admin_user):
        """测试批量删除锁定记录成功"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])

        lock_ids = [lock["id"] for lock in created_multiple_locks[:2]]

        batch_data = MemberFixedSlotLockBatchDelete(
            lock_ids=lock_ids
        )

        deleted_count = service.batch_delete_locks(batch_data)

        assert deleted_count == len(lock_ids)

        # 验证记录已被删除
        for lock_id in lock_ids:
            deleted_lock = service.get_lock(lock_id)
            assert deleted_lock is None

    def test_batch_update_status_success(self, test_session: Session, created_multiple_locks, created_admin_user):
        """测试批量更新状态成功"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])

        lock_ids = [lock["id"] for lock in created_multiple_locks[:2]]

        updated_locks = service.batch_update_status(lock_ids, MemberFixedSlotLockStatus.CANCELLED)

        assert len(updated_locks) == len(lock_ids)
        for lock in updated_locks:
            assert lock.status == MemberFixedSlotLockStatus.CANCELLED
            assert lock.id in lock_ids


class TestMemberFixedSlotLockStatusManagement:
    """会员固定课位锁定状态管理测试类"""

    def test_activate_lock_success(self, test_session: Session, created_lock, created_admin_user):
        """测试激活锁定成功"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])

        # 先暂停锁定
        service.pause_lock(created_lock["id"])

        # 然后激活锁定
        activated_lock = service.activate_lock(created_lock["id"])

        assert activated_lock is not None
        assert activated_lock.status == MemberFixedSlotLockStatus.ACTIVE
        assert activated_lock.id == created_lock["id"]

    def test_pause_lock_success(self, test_session: Session, created_lock, created_admin_user):
        """测试暂停锁定成功"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])

        paused_lock = service.pause_lock(created_lock["id"])

        assert paused_lock is not None
        assert paused_lock.status == MemberFixedSlotLockStatus.PAUSED
        assert paused_lock.id == created_lock["id"]

    def test_cancel_lock_success(self, test_session: Session, created_lock, created_admin_user):
        """测试取消锁定成功"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])

        cancelled_lock = service.cancel_lock(created_lock["id"])

        assert cancelled_lock is not None
        assert cancelled_lock.status == MemberFixedSlotLockStatus.CANCELLED
        assert cancelled_lock.id == created_lock["id"]

    def test_activate_lock_not_found(self, test_session: Session, created_admin_user):
        """测试激活不存在的锁定"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])

        with pytest.raises(MemberFixedSlotLockNotFoundError):
            service.activate_lock(99999)

    def test_pause_lock_not_found(self, test_session: Session, created_admin_user):
        """测试暂停不存在的锁定"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])

        with pytest.raises(MemberFixedSlotLockNotFoundError):
            service.pause_lock(99999)

    def test_cancel_lock_not_found(self, test_session: Session, created_admin_user):
        """测试取消不存在的锁定"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])

        with pytest.raises(MemberFixedSlotLockNotFoundError):
            service.cancel_lock(99999)


class TestMemberFixedSlotLockEdgeCases:
    """会员固定课位锁定边界情况测试类"""

    def test_get_available_slots_exclude_locked(self, test_session: Session, created_lock, created_admin_user):
        """测试获取可用时间段时排除已锁定的"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])

        query = AvailableSlotQuery(
            teacher_id=created_lock["teacher_id"],
            exclude_locked=True
        )

        available_slots = service.get_available_slots(query)

        # 已锁定的时间段不应该在结果中
        locked_slot_ids = [slot.id for slot in available_slots]
        assert created_lock["teacher_fixed_slot_id"] not in locked_slot_ids

    def test_check_conflict_with_exclude_id(self, test_session: Session, created_lock, created_admin_user):
        """测试冲突检测时排除指定ID"""
        service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])

        conflict_check = LockConflictCheck(
            member_id=created_lock["member_id"],
            teacher_fixed_slot_id=created_lock["teacher_fixed_slot_id"],
            exclude_lock_id=created_lock["id"]  # 排除自己
        )

        result = service.check_lock_conflict(conflict_check)

        # 排除自己后应该没有冲突
        assert result.has_conflict is False
