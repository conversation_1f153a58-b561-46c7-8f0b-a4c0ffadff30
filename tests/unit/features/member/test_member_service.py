import pytest
from datetime import datetime, timezone, date
from decimal import Decimal
from sqlmodel import Session

from app.features.members.service import MemberService
from app.features.members.schemas import MemberCreate, MemberUpdate
from app.features.members.models import MemberType, MemberStatus
from app.api.common.exceptions import BusinessException


class TestMemberService:
    """会员服务单元测试"""
    
    def test_create_member(self, test_session: Session, sample_member_data, created_admin_user):
        """测试创建会员"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        member_create = MemberCreate(**sample_member_data)
        
        member = service.create_member(member_create, created_by=created_admin_user["id"])
        
        print("test_create_member member:", member)
        assert member.id is not None
        assert member.name == sample_member_data["name"]
        assert member.phone == sample_member_data["phone"]
        assert member.member_type == MemberType.TRIAL  # 默认为试用会员
        assert member.member_status == MemberStatus.ACTIVE
        assert member.tenant_id == created_admin_user["tenant_id"]
        assert member.created_by == created_admin_user["id"]
        assert member.created_at is not None
        assert member.updated_at is not None
        assert member.registered_at is not None
    
    def test_create_member_duplicate_phone(self, test_session: Session, sample_member_data, created_admin_user):
        """测试创建重复手机号会员"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        member_create = MemberCreate(**sample_member_data)
        
        # 先创建一个会员
        service.create_member(member_create, created_by=created_admin_user["id"])
        
        # 尝试创建相同手机号的会员
        with pytest.raises(BusinessException, match="手机号.*已存在"):
            service.create_member(member_create, created_by=created_admin_user["id"])
    
    def test_create_member_duplicate_email(self, test_session: Session, created_admin_user):
        """测试创建重复邮箱会员"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        member_data1 = MemberCreate(
            name="会员1",
            phone="***********",
            email="<EMAIL>",
            member_type=MemberType.TRIAL
        )
        
        member_data2 = MemberCreate(
            name="会员2",
            phone="***********",
            email="<EMAIL>",  # 相同邮箱
            member_type=MemberType.TRIAL
        )
        
        # 先创建一个会员
        service.create_member(member_data1, created_by=created_admin_user["id"])
        
        # 尝试创建相同邮箱的会员
        with pytest.raises(BusinessException, match="邮箱.*已存在"):
            service.create_member(member_data2, created_by=created_admin_user["id"])
    
    def test_get_member(self, test_session: Session, sample_member_data, created_admin_user):
        """测试根据ID获取会员"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        member_create = MemberCreate(**sample_member_data)
        member = service.create_member(member_create, created_by=created_admin_user["id"])
        member_get = service.get_member(member.id)
        
        assert member is not None
        assert member.id == member_get.id
        assert member.name == member_get.name
    
    def test_get_member_not_found(self, test_session: Session, created_admin_user):
        """测试获取不存在的会员"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        member = service.get_member(99999)
        
        assert member is None
    
    def test_get_member_by_phone(self, test_session: Session, sample_member_data, created_admin_user):
        """测试根据手机号获取会员"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        member_create = MemberCreate(**sample_member_data)
        member = service.create_member(member_create, created_by=created_admin_user["id"])
        member_get = service.get_member_by_phone(member.phone)
        
        assert member is not None
        assert member.phone == member_get.phone
        assert member.name == member_get.name
    
    def test_get_member_by_email(self, test_session: Session, created_admin_user):
        """测试根据邮箱获取会员"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        member_data = MemberCreate(
            name="测试会员",
            phone="***********",
            email="<EMAIL>",
            member_type=MemberType.TRIAL
        )
        
        created_member = service.create_member(member_data, created_by=created_admin_user["id"])
        
        member = service.get_member_by_email("<EMAIL>")
        
        assert member is not None
        assert member.email == "<EMAIL>"
        assert member.id == created_member.id
    
    def test_get_member_by_wechat(self, test_session: Session, created_admin_user):
        """测试根据微信OpenID获取会员"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        member_data = MemberCreate(
            name="微信会员",
            phone="***********",
            member_type=MemberType.TRIAL
        )
        
        created_member = service.create_member(member_data, created_by=None)
        
                # 更新微信信息
        update_data = MemberUpdate(wechat_openid="wx123456789")
        updated_member = service.update_member(created_member.id, update_data)
        
        # 验证更新是否成功
        assert updated_member is not None
        assert updated_member.wechat_openid == "wx123456789"

        member = service.get_member_by_wechat("wx123456789")

        assert member is not None
        assert member.wechat_openid == "wx123456789"
        assert member.id == created_member.id
    
    def test_get_members_list(self, test_session: Session, created_admin_user):
        """测试获取会员列表"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建多个会员
        for i in range(3):
            member_data = MemberCreate(
                name=f"会员{i}",
                phone=f"1380013800{i}",
                member_type=MemberType.TRIAL
            )
            service.create_member(member_data, created_by=created_admin_user["id"])
        
        members = service.get_members()
        
        assert len(members) == 3
        # 验证按创建时间倒序排列
        assert members[0].created_at >= members[1].created_at >= members[2].created_at
    
    def test_get_members_pagination(self, test_session: Session, created_admin_user):
        """测试会员列表分页"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建5个会员
        for i in range(5):
            member_data = MemberCreate(
                name=f"会员{i}",
                phone=f"1380013800{i}",
                member_type=MemberType.TRIAL
            )
            service.create_member(member_data, created_by=created_admin_user["id"])
        
        # 测试分页
        page1 = service.get_members(skip=0, limit=3)
        page2 = service.get_members(skip=3, limit=3)
        
        assert len(page1) == 3
        assert len(page2) == 2
        
        # 验证没有重复
        page1_ids = {m.id for m in page1}
        page2_ids = {m.id for m in page2}
        assert len(page1_ids.intersection(page2_ids)) == 0
    
    def test_get_members_filter_by_type(self, test_session: Session, created_admin_user):
        """测试按会员类型筛选"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建不同类型的会员
        trial_data = MemberCreate(name="试听会员", phone="***********", member_type=MemberType.TRIAL)
        formal_data = MemberCreate(name="正式会员", phone="***********", member_type=MemberType.FORMAL)
        vip_data = MemberCreate(name="VIP会员", phone="13800138003", member_type=MemberType.VIP)
        
        service.create_member(trial_data, created_by=created_admin_user["id"])
        service.create_member(formal_data, created_by=created_admin_user["id"])
        service.create_member(vip_data, created_by=created_admin_user["id"])
        
        # 测试筛选
        trial_members = service.get_members(member_type=MemberType.TRIAL)
        formal_members = service.get_members(member_type=MemberType.FORMAL)
        vip_members = service.get_members(member_type=MemberType.VIP)
        
        assert len(trial_members) == 1
        assert len(formal_members) == 1
        assert len(vip_members) == 1
        
        assert trial_members[0].member_type == MemberType.TRIAL
        assert formal_members[0].member_type == MemberType.FORMAL
        assert vip_members[0].member_type == MemberType.VIP
    
    def test_get_members_filter_by_status(self, test_session: Session, created_admin_user):
        """测试按会员状态筛选"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建会员并设置不同状态
        member_data1 = MemberCreate(name="活跃会员", phone="***********", member_type=MemberType.TRIAL)
        member_data2 = MemberCreate(name="冻结会员", phone="***********", member_type=MemberType.TRIAL)
        
        member1 = service.create_member(member_data1, created_by=created_admin_user["id"])
        member2 = service.create_member(member_data2, created_by=created_admin_user["id"])
        
        # 更新状态
        service.update_member_status(member2.id, MemberStatus.FROZEN)
        
        # 测试筛选
        active_members = service.get_members(member_status=MemberStatus.ACTIVE)
        frozen_members = service.get_members(member_status=MemberStatus.FROZEN)
        
        assert len(active_members) == 1
        assert len(frozen_members) == 1
        
        assert active_members[0].member_status == MemberStatus.ACTIVE
        assert frozen_members[0].member_status == MemberStatus.FROZEN
    
    def test_get_members_search(self, test_session: Session, created_admin_user):
        """测试会员搜索"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建会员
        member_data1 = MemberCreate(name="张三", phone="***********", email="<EMAIL>", member_type=MemberType.TRIAL)
        member_data2 = MemberCreate(name="李四", phone="13900139002", email="<EMAIL>", member_type=MemberType.TRIAL)
        
        service.create_member(member_data1, created_by=created_admin_user["id"])
        service.create_member(member_data2, created_by=created_admin_user["id"])
        
        # 按姓名搜索
        result1 = service.get_members(search_keyword="张三")
        assert len(result1) == 1
        assert result1[0].name == "张三"
        
        # 按手机号搜索
        result2 = service.get_members(search_keyword="139")
        assert len(result2) == 1
        assert result2[0].phone == "13900139002"
        
        # 按邮箱搜索
        result3 = service.get_members(search_keyword="zhangsan")
        assert len(result3) == 1
        assert result3[0].email == "<EMAIL>"
    
    def test_count_members(self, test_session: Session, created_admin_user):
        """测试统计会员数量"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建不同类型的会员
        for i in range(3):
            trial_data = MemberCreate(name=f"试听会员{i}", phone=f"1380013800{i}", member_type=MemberType.TRIAL)
            service.create_member(trial_data, created_by=created_admin_user["id"])
        
        for i in range(2):
            formal_data = MemberCreate(name=f"正式会员{i}", phone=f"1390013900{i}", member_type=MemberType.FORMAL)
            service.create_member(formal_data, created_by=created_admin_user["id"])
        
        # 测试统计
        total_count = service.count_members()
        trial_count = service.count_members(member_type=MemberType.TRIAL)
        formal_count = service.count_members(member_type=MemberType.FORMAL)
        
        assert total_count == 5
        assert trial_count == 3
        assert formal_count == 2
    
    def test_update_member(self, test_session: Session, sample_member_data, created_admin_user):
        """测试更新会员"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        update_data = MemberUpdate(
            name="更新后的姓名",
            email="<EMAIL>",
            member_type=MemberType.FORMAL,
            notes="更新备注"
        )
        
        member_create = MemberCreate(**sample_member_data)
        member = service.create_member(member_create, created_by=created_admin_user["id"])
        updated_member = service.update_member(member.id, update_data)
        
        assert updated_member is not None
        assert updated_member.name == "更新后的姓名"
        assert updated_member.email == "<EMAIL>"
        assert updated_member.member_type == MemberType.FORMAL
        assert updated_member.notes == "更新备注"
        assert updated_member.updated_at is not None
    
    def test_update_member_not_found(self, test_session: Session, created_admin_user):
        """测试更新不存在的会员"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        update_data = MemberUpdate(name="新名称")
        
        result = service.update_member(99999, update_data)
        
        assert result is None
    
    def test_update_member_duplicate_email(self, test_session: Session, created_admin_user):
        """测试更新会员时邮箱重复"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建两个会员
        member1_data = MemberCreate(name="会员1", phone="***********", email="<EMAIL>", member_type=MemberType.TRIAL)
        member2_data = MemberCreate(name="会员2", phone="***********", email="<EMAIL>", member_type=MemberType.TRIAL)
        
        member1 = service.create_member(member1_data, created_by=created_admin_user["id"])
        member2 = service.create_member(member2_data, created_by=created_admin_user["id"])
        
        # 尝试将member2的邮箱改为member1的邮箱
        update_data = MemberUpdate(email="<EMAIL>")
        
        with pytest.raises(BusinessException, match="邮箱.*已存在"):
            service.update_member(member2.id, update_data)
    
    def test_update_member_status(self, test_session: Session, sample_member_data, created_admin_user):
        """测试更新会员状态"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        member_create = MemberCreate(**sample_member_data)
        member = service.create_member(member_create, created_by=created_admin_user["id"])
        updated_member = service.update_member_status(member.id, MemberStatus.FROZEN)
        
        assert updated_member is not None
        assert updated_member.member_status == MemberStatus.FROZEN
        assert updated_member.updated_at is not None
    
    def test_update_member_stats(self, test_session: Session, sample_member_data, created_admin_user):
        """测试更新会员统计信息"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        member_create = MemberCreate(**sample_member_data)
        member = service.create_member(member_create, created_by=created_admin_user["id"])
        # 测试完成课程
        updated_member = service.update_member_stats(
            member.id,
            class_completed=True,
            amount_spent=100.0
        )
        
        assert updated_member is not None
        assert updated_member.total_classes == 1
        assert updated_member.completed_classes == 1
        assert updated_member.total_spent == Decimal("100.00")
        assert updated_member.last_class_at is not None
        
        # 测试取消课程
        updated_member = service.update_member_stats(
            member.id,
            class_cancelled=True
        )
        
        assert updated_member.total_classes == 2
        assert updated_member.cancelled_classes == 1
        
        # 测试缺席课程
        updated_member = service.update_member_stats(
            member.id,
            class_no_show=True
        )
        
        assert updated_member.total_classes == 3
        assert updated_member.no_show_classes == 1
    
    def test_update_login_time(self, test_session: Session, sample_member_data, created_admin_user):
        """测试更新登录时间"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        member_create = MemberCreate(**sample_member_data)
        member = service.create_member(member_create, created_by=created_admin_user["id"])
        updated_member = service.update_login_time(member.id)
        
        assert updated_member is not None
        assert updated_member.last_login_at is not None
        assert updated_member.updated_at is not None
    
    def test_delete_member(self, test_session: Session, sample_member_data, created_admin_user):
        """测试删除会员"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        member_create = MemberCreate(**sample_member_data)
        member = service.create_member(member_create, created_by=created_admin_user["id"])
        success = service.delete_member(member.id)
        
        assert success is True
        
        # 验证会员已被删除
        deleted_member = service.get_member(member.id)
        assert deleted_member is None
    
    def test_delete_member_not_found(self, test_session: Session, created_admin_user):
        """测试删除不存在的会员"""
        service = MemberService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        success = service.delete_member(99999)
        
        assert success is False 