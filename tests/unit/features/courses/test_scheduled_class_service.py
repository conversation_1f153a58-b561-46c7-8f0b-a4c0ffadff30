import pytest
from datetime import datetime, timezone, timedelta
from sqlmodel import Session

from app.features.courses.scheduled_classes_service import ScheduledClassService
from app.features.courses.scheduled_classes_schemas import (
    ScheduledClassCreate, ScheduledClassUpdate, ScheduledClassQuery,
    TeacherClassCreate, MemberC<PERSON>Booking, AdminClassCreate, ClassCancellation
)
from app.features.courses.scheduled_classes_models import ClassStatus, ClassType
from app.features.courses.scheduled_classes_exceptions import (
    ScheduledClassBusinessException, ScheduledClassNotFoundError
)
from app.features.member_cards.models import MemberCardOperation, MemberCardOperationType
from app.features.member_cards.card_service import MemberCardService
from app.api.common.exceptions import BusinessException
from sqlmodel import select, and_


class TestScheduledClassService:
    """已排课表服务单元测试"""
    
    def test_create_scheduled_class(self, test_session: Session, sample_scheduled_class_data, 
                                  created_teacher, created_admin_user):
        """测试创建已排课表记录"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 使用实际的teacher_id
        class_data = sample_scheduled_class_data.copy()
        class_data["teacher_id"] = created_teacher["id"]
        
        scheduled_class_create = ScheduledClassCreate(**class_data)
        scheduled_class = service.create_scheduled_class(
            scheduled_class_create, 
            created_by=created_admin_user["id"]
        )
        
        assert scheduled_class.id is not None
        assert scheduled_class.teacher_id == created_teacher["id"]
        assert scheduled_class.member_id is None  # 初始状态无会员
        assert scheduled_class.status == ClassStatus.AVAILABLE
        assert scheduled_class.class_type == ClassType.DIRECT
        assert scheduled_class.tenant_id == created_admin_user["tenant_id"]
        assert scheduled_class.created_by == created_admin_user["id"]
        assert scheduled_class.created_at is not None
        assert scheduled_class.updated_at is not None
    
    def test_create_scheduled_class_with_member(self, test_session: Session, sample_booked_class_data,
                                              created_teacher, created_member, created_admin_user):
        """测试创建带会员的已排课表记录"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 使用实际的teacher_id和member_id
        class_data = sample_booked_class_data.copy()
        class_data["teacher_id"] = created_teacher["id"]
        class_data["member_id"] = created_member["id"]
        
        scheduled_class_create = ScheduledClassCreate(**class_data)
        scheduled_class = service.create_scheduled_class(
            scheduled_class_create,
            created_by=created_admin_user["id"]
        )
        
        assert scheduled_class.member_id == created_member["id"]
        assert scheduled_class.status == ClassStatus.BOOKED
        assert scheduled_class.member_card_name == "测试会员卡"
        assert scheduled_class.booking_remark == "测试预约备注"
    
    def test_create_scheduled_class_past_time(self, test_session: Session, sample_scheduled_class_data,
                                            created_teacher, created_admin_user):
        """测试创建过去时间的课程（应该失败）"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 设置过去时间
        class_data = sample_scheduled_class_data.copy()
        class_data["teacher_id"] = created_teacher["id"]
        class_data["class_datetime"] = datetime.now() - timedelta(hours=1)

        # 验证在schema层面就会失败
        from pydantic import ValidationError
        with pytest.raises(ValidationError, match="上课时间必须是未来时间"):
            ScheduledClassCreate(**class_data)
    
    def test_get_scheduled_class(self, test_session: Session, created_scheduled_class, created_admin_user):
        """测试根据ID获取已排课表记录"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        scheduled_class = service.get_scheduled_class(created_scheduled_class["id"])
        
        assert scheduled_class is not None
        assert scheduled_class.id == created_scheduled_class["id"]
        assert scheduled_class.teacher_id == created_scheduled_class["teacher_id"]
    
    def test_get_scheduled_class_not_found(self, test_session: Session, created_admin_user):
        """测试获取不存在的已排课表记录"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        scheduled_class = service.get_scheduled_class(99999)
        
        assert scheduled_class is None
    
    def test_update_scheduled_class(self, test_session: Session, created_scheduled_class, created_admin_user):
        """测试更新已排课表记录"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 更新价格和备注
        update_data = ScheduledClassUpdate(
            price=150,
            booking_remark="更新后的备注"
        )
        
        updated_class = service.update_scheduled_class(
            created_scheduled_class["id"], 
            update_data
        )
        
        assert updated_class.price == 150
        assert updated_class.booking_remark == "更新后的备注"
        assert updated_class.updated_at > updated_class.created_at
    
    def test_update_scheduled_class_not_found(self, test_session: Session, created_admin_user):
        """测试更新不存在的已排课表记录"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        update_data = ScheduledClassUpdate(price=150)
        
        with pytest.raises(ScheduledClassNotFoundError):
            service.update_scheduled_class(99999, update_data)
    
    def test_delete_scheduled_class(self, test_session: Session, created_scheduled_class, created_admin_user):
        """测试删除已排课表记录（软删除）"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        result = service.delete_scheduled_class(created_scheduled_class["id"])
        
        assert result is True
        
        # 验证软删除
        deleted_class = service.get_scheduled_class(created_scheduled_class["id"])
        assert deleted_class.is_deleted is True
    
    def test_delete_scheduled_class_not_found(self, test_session: Session, created_admin_user):
        """测试删除不存在的已排课表记录"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        result = service.delete_scheduled_class(99999)
        
        assert result is False
    
    def test_get_scheduled_classes_with_query(self, test_session: Session, multiple_scheduled_classes, 
                                            created_admin_user):
        """测试查询已排课表列表"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        query_params = ScheduledClassQuery(
            page=1,
            size=10,
            teacher_id=multiple_scheduled_classes[0]["teacher_id"],
            status=ClassStatus.AVAILABLE
        )
        
        classes, total = service.get_scheduled_classes(query_params)
        
        assert len(classes) > 0
        assert total > 0
        assert all(cls.teacher_id == multiple_scheduled_classes[0]["teacher_id"] for cls in classes)
        assert all(cls.status == ClassStatus.AVAILABLE for cls in classes)
    
    def test_get_available_classes(self, test_session: Session, multiple_scheduled_classes, created_admin_user):
        """测试获取可预约课程列表"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        available_classes = service.get_available_classes(
            teacher_id=multiple_scheduled_classes[0]["teacher_id"]
        )
        
        assert len(available_classes) > 0
        assert all(cls.status == ClassStatus.AVAILABLE for cls in available_classes)
        assert all(cls.is_visible_to_member is True for cls in available_classes)
    
    def test_get_teacher_classes(self, test_session: Session, multiple_scheduled_classes, created_admin_user):
        """测试获取教师课程列表"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        teacher_classes = service.get_teacher_classes(
            teacher_id=multiple_scheduled_classes[0]["teacher_id"]
        )
        
        assert len(teacher_classes) > 0
        assert all(cls.teacher_id == multiple_scheduled_classes[0]["teacher_id"] for cls in teacher_classes)
    
    def test_count_classes_by_status(self, test_session: Session, multiple_scheduled_classes, created_admin_user):
        """测试统计各状态课程数量"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        status_counts = service.count_classes_by_status(
            teacher_id=multiple_scheduled_classes[0]["teacher_id"]
        )
        
        assert isinstance(status_counts, dict)
        assert "available" in status_counts
        assert "booked" in status_counts
        assert status_counts["available"] > 0  # 应该有可预约的课程
    
    def test_batch_update_status(self, test_session: Session, multiple_scheduled_classes, created_admin_user):
        """测试批量更新课程状态"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 获取前3个课程的ID
        class_ids = [cls["id"] for cls in multiple_scheduled_classes[:3]]
        
        updated_classes = service.batch_update_status(class_ids, ClassStatus.TEACHER_NO_SHOW)
        
        assert len(updated_classes) == 3
        assert all(cls.status == ClassStatus.TEACHER_NO_SHOW for cls in updated_classes)
    
    def test_batch_delete_classes(self, test_session: Session, multiple_scheduled_classes, created_admin_user):
        """测试批量删除课程"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 获取前2个课程的ID
        class_ids = [cls["id"] for cls in multiple_scheduled_classes[:2]]
        
        deleted_count = service.batch_delete_classes(class_ids)
        
        assert deleted_count == 2


class TestScheduledClassBooking:
    """课程预约相关测试"""

    def test_book_class_with_factory(self, test_session: Session, create_class_factory,
                                   created_member, created_admin_user):
        """测试预约课程 - 使用工厂模式"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建3小时后的可预约课程
        scheduled_class = create_class_factory(hours_offset=3, is_booked=False)

        booking_data = MemberClassBooking(
            class_id=scheduled_class["id"],
            member_card_id=1,
            member_card_name="测试会员卡",
            material_name="测试教材",
            booking_remark="测试预约"
        )

        booked_class = service.book_class(
            scheduled_class["id"],
            created_member["id"],
            booking_data,
            operator_name="测试操作员"
        )

        assert booked_class.member_id == created_member["id"]
        assert booked_class.status == ClassStatus.BOOKED
        assert booked_class.member_card_name == "测试会员卡"
        assert booked_class.booking_remark == "测试预约"
        assert booked_class.operator_name == "测试操作员"

    def test_cancel_booking_with_factory(self, test_session: Session, create_class_factory, created_admin_user):
        """测试取消课程预约 - 使用工厂模式"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建3小时后的已预约课程
        booked_class = create_class_factory(hours_offset=3, is_booked=True)

        cancelled_class = service.cancel_booking(
            booked_class["id"],
            operator_name="测试操作员"
        )

        assert cancelled_class.member_id is None
        assert cancelled_class.status == ClassStatus.AVAILABLE
        assert cancelled_class.member_card_id is None
        assert cancelled_class.member_card_name is None
        assert cancelled_class.operator_name == "测试操作员"



    def test_time_boundary_conditions(self, test_session: Session, create_class_factory, created_admin_user):
        """测试时间边界条件"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 测试刚好2小时后的课程（应该不能取消）
        booked_class_2h = create_class_factory(hours_offset=2, is_booked=True)
        
        with pytest.raises(ScheduledClassBusinessException) as exc_info:
            service.cancel_booking(booked_class_2h["id"])
        
        assert "取消时间已过" in exc_info.value.message
        
        # 测试3小时后的课程（应该可以取消）
        booked_class_3h = create_class_factory(hours_offset=3, is_booked=True)
        
        cancelled_class = service.cancel_booking(booked_class_3h["id"])
        assert cancelled_class.status == ClassStatus.AVAILABLE

    def test_book_class_not_found(self, test_session: Session, created_member, created_admin_user):
        """测试预约不存在的课程"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        with pytest.raises(ScheduledClassNotFoundError):
            service.book_class(99999, created_member["id"])

    def test_book_class_already_booked(self, test_session: Session, create_class_factory,
                                     created_member, created_admin_user):
        """测试预约已被预约的课程"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建已预约的课程
        booked_class = create_class_factory(hours_offset=3, is_booked=True)

        with pytest.raises(ScheduledClassBusinessException) as exc_info:
            service.book_class(booked_class["id"], created_member["id"])
        
        assert "不可预约" in exc_info.value.message

    def test_cancel_booking_not_found(self, test_session: Session, created_admin_user):
        """测试取消不存在课程的预约"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        with pytest.raises(ScheduledClassNotFoundError):
            service.cancel_booking(99999)

    def test_cancel_booking_not_booked(self, test_session: Session, create_class_factory, created_admin_user):
        """测试取消未预约课程的预约"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 创建可预约（未预约）的课程
        available_class = create_class_factory(hours_offset=3, is_booked=False)

        with pytest.raises(ScheduledClassBusinessException) as exc_info:
            service.cancel_booking(available_class["id"])
        
        assert "不可预约" in exc_info.value.message


class TestTimeConflictDetection:
    """时间冲突检测测试"""

    def test_check_time_conflict_no_conflict(self, test_session: Session, created_scheduled_class,
                                           created_teacher, created_member, created_admin_user):
        """测试无时间冲突的情况"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 测试不冲突的时间
        future_time = datetime.now() + timedelta(hours=5)

        result = service.check_time_conflict(
            teacher_id=created_teacher["id"],
            member_id=created_member["id"],
            class_datetime=future_time,
            duration_minutes=25
        )

        assert result["has_conflict"] is False
        assert result["teacher_conflict"] is False
        assert result["member_conflict"] is False
        assert len(result["conflict_details"]) == 0

    def test_check_time_conflict_teacher_conflict(self, test_session: Session, created_booked_class,
                                                created_teacher, created_admin_user):
        """测试教师时间冲突"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 使用与现有课程相同的时间
        conflict_time = created_booked_class["class_datetime"]

        result = service.check_time_conflict(
            teacher_id=created_teacher["id"],
            member_id=None,
            class_datetime=conflict_time,
            duration_minutes=25
        )

        assert result["has_conflict"] is True
        assert result["teacher_conflict"] is True
        assert len(result["conflict_details"]) > 0

    def test_get_teacher_schedule(self, test_session: Session, created_booked_class,
                                created_teacher, created_admin_user):
        """测试获取教师时间安排"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        date_from = datetime.now()
        date_to = datetime.now() + timedelta(days=1)

        schedule = service.get_teacher_schedule(
            teacher_id=created_teacher["id"],
            date_from=date_from,
            date_to=date_to
        )

        assert len(schedule) > 0
        assert all("class_id" in item for item in schedule)
        assert all("start_time" in item for item in schedule)
        assert all("end_time" in item for item in schedule)

    def test_find_available_time_slots(self, test_session: Session, created_teacher, created_admin_user):
        """测试查找可用时间段"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        date_from = datetime.now() + timedelta(days=1, hours=8)  # 明天8点
        date_to = datetime.now() + timedelta(days=1, hours=18)   # 明天18点

        available_slots = service.find_available_time_slots(
            teacher_id=created_teacher["id"],
            date_from=date_from,
            date_to=date_to,
            duration_minutes=25,
            interval_minutes=30
        )

        assert len(available_slots) > 0
        assert all(isinstance(slot, datetime) for slot in available_slots)

    def test_suggest_alternative_times(self, test_session: Session, created_teacher, created_admin_user):
        """测试建议替代时间"""
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        original_time = datetime.now() + timedelta(days=1, hours=10)

        alternatives = service.suggest_alternative_times(
            teacher_id=created_teacher["id"],
            original_datetime=original_time,
            duration_minutes=25,
            search_days=3
        )

        assert len(alternatives) >= 0  # 可能没有可用时间
        assert all(isinstance(alt, datetime) for alt in alternatives)


class TestScheduledClassMemberCardIntegration:
    """课程预约会员卡集成测试 - 任务7.4.5"""

    def test_book_class_with_sufficient_balance_should_deduct_fee(self, test_session: Session,
                                                                create_class_factory, created_member, created_admin_user):
        """测试预约课程时余额充足应该自动扣费"""
        # Arrange - 准备数据
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 创建一个付费课程（100元）
        scheduled_class = create_class_factory(hours_offset=3, is_booked=False, price=100)

        # 获取会员卡初始余额
        card_service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        initial_card = card_service.get_card(created_member["primary_member_card_id"])
        initial_balance = initial_card.balance

        booking_data = MemberClassBooking(
            class_id=scheduled_class["id"],
            member_card_id=created_member["primary_member_card_id"],
            member_card_name=created_member["primary_member_card_name"],
            material_name="测试教材",
            booking_remark="测试预约扣费"
        )

        # Act - 执行预约
        booked_class = service.book_member_class(
            member_id=created_member["id"],
            member_booking_data=booking_data,
            created_by=created_admin_user["id"]
        )

        # Assert - 验证结果
        # 1. 课程预约成功
        assert booked_class.status == ClassStatus.BOOKED
        assert booked_class.member_id == created_member["id"]
        assert booked_class.member_card_id == created_member["primary_member_card_id"]

        # 2. 会员卡余额被扣除
        updated_card = card_service.get_card(created_member["primary_member_card_id"])
        assert updated_card.balance == initial_balance - 100

        # 3. 创建了扣费操作记录
        deduction_operations = test_session.exec(
            select(MemberCardOperation).where(
                and_(
                    MemberCardOperation.tenant_id == created_admin_user["tenant_id"],
                    MemberCardOperation.member_card_id == created_member["primary_member_card_id"],
                    MemberCardOperation.scheduled_class_id == scheduled_class["id"],
                    MemberCardOperation.operation_type == MemberCardOperationType.DIRECT_BOOKING,
                    MemberCardOperation.amount_change == -100
                )
            )
        ).all()

        assert len(deduction_operations) == 1
        operation = deduction_operations[0]
        assert operation.balance_before == initial_balance
        assert operation.balance_after == initial_balance - 100
        assert "课程预约扣费" in operation.operation_description

    def test_book_class_with_insufficient_balance_should_raise_error(self, test_session: Session,
                                                                   create_class_factory, created_member, created_admin_user):
        """测试预约课程时余额不足应该抛出异常"""
        # Arrange - 准备数据
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 创建一个高价课程（超过会员卡余额）
        scheduled_class = create_class_factory(hours_offset=3, is_booked=False, price=2000)  # 2000元，超过默认1000元余额

        booking_data = MemberClassBooking(
            class_id=scheduled_class["id"],
            member_card_id=created_member["primary_member_card_id"],
            member_card_name=created_member["primary_member_card_name"],
            material_name="测试教材",
            booking_remark="测试余额不足"
        )

        # Act & Assert - 执行预约应该抛出异常
        with pytest.raises(BusinessException) as exc_info:
            service.book_member_class(
                member_id=created_member["id"],
                member_booking_data=booking_data,
                created_by=created_admin_user["id"]
            )

        assert "余额不足" in str(exc_info.value)

    def test_book_free_class_should_not_deduct_fee(self, test_session: Session,
                                                  create_class_factory, created_member, created_admin_user):
        """测试预约免费课程不应该扣费"""
        # Arrange - 准备数据
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 创建免费课程
        scheduled_class = create_class_factory(hours_offset=3, is_booked=False, price=None)

        # 获取会员卡初始余额
        card_service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        initial_card = card_service.get_card(created_member["primary_member_card_id"])
        initial_balance = initial_card.balance

        booking_data = MemberClassBooking(
            class_id=scheduled_class["id"],
            member_card_id=created_member["primary_member_card_id"],
            member_card_name=created_member["primary_member_card_name"],
            material_name="测试教材",
            booking_remark="测试免费课程"
        )

        # Act - 执行预约
        booked_class = service.book_member_class(
            member_id=created_member["id"],
            member_booking_data=booking_data,
            created_by=created_admin_user["id"]
        )

        # Assert - 验证结果
        # 1. 课程预约成功
        assert booked_class.status == ClassStatus.BOOKED
        assert booked_class.member_id == created_member["id"]

        # 2. 会员卡余额未变化
        updated_card = card_service.get_card(created_member["primary_member_card_id"])
        assert updated_card.balance == initial_balance

        # 3. 没有创建扣费操作记录
        deduction_operations = test_session.exec(
            select(MemberCardOperation).where(
                and_(
                    MemberCardOperation.tenant_id == created_admin_user["tenant_id"],
                    MemberCardOperation.member_card_id == created_member["primary_member_card_id"],
                    MemberCardOperation.scheduled_class_id == scheduled_class["id"],
                    MemberCardOperation.operation_type == MemberCardOperationType.DIRECT_BOOKING
                )
            )
        ).all()

        assert len(deduction_operations) == 0

    def test_cancel_booking_with_paid_class_should_refund_fee(self, test_session: Session,
                                                            create_class_factory, created_member, created_admin_user):
        """测试取消付费课程预约应该退费"""
        # Arrange - 准备数据
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 1. 先预约一个付费课程
        scheduled_class = create_class_factory(hours_offset=3, is_booked=False, price=150)

        # 获取会员卡初始余额
        card_service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        initial_card = card_service.get_card(created_member["primary_member_card_id"])
        initial_balance = initial_card.balance

        booking_data = MemberClassBooking(
            class_id=scheduled_class["id"],
            member_card_id=created_member["primary_member_card_id"],
            member_card_name=created_member["primary_member_card_name"],
            material_name="测试教材",
            booking_remark="测试预约后取消"
        )

        # 预约课程（会扣费）
        booked_class = service.book_member_class(
            member_id=created_member["id"],
            member_booking_data=booking_data,
            created_by=created_admin_user["id"]
        )

        # 验证扣费成功
        after_booking_card = card_service.get_card(created_member["primary_member_card_id"])
        assert after_booking_card.balance == initial_balance - 150

        # Act - 取消预约
        cancellation_data = ClassCancellation(
            cancellation_reason="测试取消退费"
        )

        cancelled_class = service.cancel_booking(
            class_id=booked_class.id,
            cancellation_data=cancellation_data,
            operator_name="测试操作员"
        )

        # Assert - 验证结果
        # 1. 课程取消成功
        assert cancelled_class.status == ClassStatus.AVAILABLE
        assert cancelled_class.member_id is None
        assert cancelled_class.member_card_id is None

        # 2. 会员卡余额被退还
        after_cancel_card = card_service.get_card(created_member["primary_member_card_id"])
        assert after_cancel_card.balance == initial_balance  # 余额恢复到初始状态

        # 3. 创建了退费操作记录
        refund_operations = test_session.exec(
            select(MemberCardOperation).where(
                and_(
                    MemberCardOperation.tenant_id == created_admin_user["tenant_id"],
                    MemberCardOperation.member_card_id == created_member["primary_member_card_id"],
                    MemberCardOperation.operation_type == MemberCardOperationType.REFUND,
                    MemberCardOperation.amount_change == 150  # 退费是正数
                )
            )
        ).all()

        assert len(refund_operations) == 1
        refund_operation = refund_operations[0]
        assert refund_operation.balance_before == initial_balance - 150
        assert refund_operation.balance_after == initial_balance
        assert "课程取消退费" in refund_operation.operation_description

    def test_cancel_booking_with_free_class_should_not_refund(self, test_session: Session,
                                                            create_class_factory, created_member, created_admin_user):
        """测试取消免费课程预约不应该退费"""
        # Arrange - 准备数据
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 1. 先预约一个免费课程
        scheduled_class = create_class_factory(hours_offset=3, is_booked=False, price=None)

        # 获取会员卡初始余额
        card_service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        initial_card = card_service.get_card(created_member["primary_member_card_id"])
        initial_balance = initial_card.balance

        booking_data = MemberClassBooking(
            class_id=scheduled_class["id"],
            member_card_id=created_member["primary_member_card_id"],
            member_card_name=created_member["primary_member_card_name"],
            material_name="测试教材",
            booking_remark="测试免费课程取消"
        )

        # 预约免费课程（不会扣费）
        booked_class = service.book_member_class(
            member_id=created_member["id"],
            member_booking_data=booking_data,
            created_by=created_admin_user["id"]
        )

        # 验证没有扣费
        after_booking_card = card_service.get_card(created_member["primary_member_card_id"])
        assert after_booking_card.balance == initial_balance

        # Act - 取消预约
        cancelled_class = service.cancel_booking(
            class_id=booked_class.id,
            operator_name="测试操作员"
        )

        # Assert - 验证结果
        # 1. 课程取消成功
        assert cancelled_class.status == ClassStatus.AVAILABLE
        assert cancelled_class.member_id is None

        # 2. 会员卡余额未变化
        after_cancel_card = card_service.get_card(created_member["primary_member_card_id"])
        assert after_cancel_card.balance == initial_balance

        # 3. 没有创建退费操作记录
        refund_operations = test_session.exec(
            select(MemberCardOperation).where(
                and_(
                    MemberCardOperation.tenant_id == created_admin_user["tenant_id"],
                    MemberCardOperation.member_card_id == created_member["primary_member_card_id"],
                    MemberCardOperation.operation_type == MemberCardOperationType.REFUND
                )
            )
        ).all()

        assert len(refund_operations) == 0

    def test_booking_transaction_consistency_on_failure(self, test_session: Session,
                                                       create_class_factory, created_member, created_admin_user):
        """测试预约失败时事务一致性 - 扣费应该回滚"""
        # Arrange - 准备数据
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 创建一个付费课程
        scheduled_class = create_class_factory(hours_offset=3, is_booked=False, price=100)

        # 获取会员卡初始余额
        card_service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        initial_card = card_service.get_card(created_member["primary_member_card_id"])
        initial_balance = initial_card.balance

        # 先预约课程，使其变为已预约状态
        booking_data = MemberClassBooking(
            class_id=scheduled_class["id"],
            member_card_id=created_member["primary_member_card_id"],
            member_card_name=created_member["primary_member_card_name"],
            material_name="测试教材",
            booking_remark="第一次预约"
        )

        service.book_member_class(
            member_id=created_member["id"],
            member_booking_data=booking_data,
            created_by=created_admin_user["id"]
        )

        # 验证第一次预约成功，余额被扣除
        after_first_booking = card_service.get_card(created_member["primary_member_card_id"])
        assert after_first_booking.balance == initial_balance - 100

        # Act & Assert - 尝试再次预约同一课程（应该失败）
        booking_data_2 = MemberClassBooking(
            class_id=scheduled_class["id"],
            member_card_id=created_member["primary_member_card_id"],
            member_card_name=created_member["primary_member_card_name"],
            material_name="测试教材",
            booking_remark="第二次预约（应该失败）"
        )

        with pytest.raises(ScheduledClassBusinessException):
            service.book_member_class(
                member_id=created_member["id"],
                member_booking_data=booking_data_2,
                created_by=created_admin_user["id"]
            )

        # 验证失败后余额没有再次被扣除（事务回滚）
        after_failed_booking = card_service.get_card(created_member["primary_member_card_id"])
        assert after_failed_booking.balance == initial_balance - 100  # 只扣除了第一次的费用

        # 验证只有一条扣费记录
        deduction_operations = test_session.exec(
            select(MemberCardOperation).where(
                and_(
                    MemberCardOperation.tenant_id == created_admin_user["tenant_id"],
                    MemberCardOperation.member_card_id == created_member["primary_member_card_id"],
                    MemberCardOperation.scheduled_class_id == scheduled_class["id"],
                    MemberCardOperation.operation_type == MemberCardOperationType.DIRECT_BOOKING,
                    MemberCardOperation.amount_change == -100
                )
            )
        ).all()

        assert len(deduction_operations) == 1  # 只有第一次预约的扣费记录

    def test_balance_validation_logic(self, test_session: Session,
                                    create_class_factory, created_member, created_admin_user):
        """测试余额验证逻辑的准确性"""
        # Arrange - 准备数据
        service = ScheduledClassService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 获取会员卡当前余额
        card_service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        initial_card = card_service.get_card(created_member["primary_member_card_id"])
        current_balance = initial_card.balance

        # 创建一个价格等于当前余额的课程（边界情况）
        scheduled_class = create_class_factory(hours_offset=3, is_booked=False, price=current_balance)

        booking_data = MemberClassBooking(
            class_id=scheduled_class["id"],
            member_card_id=created_member["primary_member_card_id"],
            member_card_name=created_member["primary_member_card_name"],
            material_name="测试教材",
            booking_remark="测试余额边界"
        )

        # Act - 预约课程（余额刚好够）
        booked_class = service.book_member_class(
            member_id=created_member["id"],
            member_booking_data=booking_data,
            created_by=created_admin_user["id"]
        )

        # Assert - 验证结果
        # 1. 预约成功
        assert booked_class.status == ClassStatus.BOOKED

        # 2. 余额变为0
        final_card = card_service.get_card(created_member["primary_member_card_id"])
        assert final_card.balance == 0

        # 3. 创建了正确的扣费记录
        deduction_operations = test_session.exec(
            select(MemberCardOperation).where(
                and_(
                    MemberCardOperation.tenant_id == created_admin_user["tenant_id"],
                    MemberCardOperation.member_card_id == created_member["primary_member_card_id"],
                    MemberCardOperation.scheduled_class_id == scheduled_class["id"],
                    MemberCardOperation.operation_type == MemberCardOperationType.DIRECT_BOOKING,
                    MemberCardOperation.amount_change == -current_balance
                )
            )
        ).all()

        assert len(deduction_operations) == 1
        operation = deduction_operations[0]
        assert operation.balance_before == current_balance
        assert operation.balance_after == 0
