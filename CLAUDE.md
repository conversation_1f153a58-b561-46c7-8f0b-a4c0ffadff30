# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Development Commands

### Quick Start

```bash
python quick_start.py

# Production mode (PostgreSQL)
python run.py

# Manual startup
uvicorn app.main:app --host 0.0.0.0 --port 8012 --reload
```

### Testing

```bash
# Run all tests
python scripts/test.py all

# Run specific test types
python scripts/test.py unit          # Unit tests only
python scripts/test.py api           # API integration tests
python scripts/test.py coverage      # With coverage report
python scripts/test.py quick         # Skip slow tests

# Database-specific testing
python scripts/test.py api --db=postgres

# Run specific test patterns
python scripts/test.py all -k "test_create"
```

只要没有明确指示，永远不要进行 all 测试，总是优先 unit 和 api 就可以

### Database Management

```bash
# Initialize database (handled automatically on startup)
python scripts/init_database.py
```

## 🏗️ Architecture Overview

### Multi-tenant SaaS Architecture

- **Strategy**: Shared Database, Shared Schema with Tenant ID
- **Key entities**: Tenant (机构) → User (管理员) → Member (客户)
- **Database**: PostgreSQL (production)
- **Framework**: FastAPI + SQLModel + Pydantic

### Project Structure

```
app/
├── main.py                 # FastAPI application entry point
├── core/                   # Core configuration and dependencies
├── db/                     # Database connection and session management
├── features/               # Feature-based modules (DDD style)
│   ├── auth/              # Authentication
│   ├── tenants/           # Multi-tenant management
│   ├── users/             # User management (admins)
│   ├── members/           # Member management (customers)
│   ├── teachers/          # Teacher management
│   ├── courses/           # Course configuration
│   └── tags/              # Tag system
├── api/                   # API routes and endpoints
│   ├── common/            # Shared API utilities
│   └── v1/                # API version 1
├── models/shared/         # Global shared models
└── utils/                 # Utility functions
```

### Feature Module Pattern

Each feature follows this structure:

- `models.py` - SQLModel database models
- `schemas.py` - Pydantic request/response schemas
- `service.py` - Business logic layer
- `router.py` - FastAPI route definitions
- `exceptions.py` - Feature-specific exceptions

## 🧪 Testing Architecture

### Test Structure

```
tests/
├── unit/                  # Pure business logic tests
├── integration/           # API and database integration tests
├── e2e/                   # End-to-end scenarios
├── fixtures/              # Reusable test data
│   ├── database.py        # DB session fixtures
│   ├── client.py          # API client fixtures
│   └── business/          # Business entity fixtures
└── conftest.py           # Global test configuration
```

### Test Markers

- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.api` - API endpoint tests
- `@pytest.mark.auth` - Authentication tests
- `@pytest.mark.slow` - Long-running tests
- `@pytest.mark.manual` - Manual test scenarios

## 🔑 Key Development Patterns

### Authentication Flow

1. JWT-based authentication with tenant context
2. Login endpoints: `/api/v1/auth/admin/login` (admin users)
3. Token includes tenant_id for multi-tenant isolation
4. Protected routes use `get_current_user` dependency

### Multi-tenant Data Isolation

- All business entities include `tenant_id` field
- Database queries automatically filter by tenant context
- Service layer enforces tenant boundaries
- API endpoints validate tenant access

### Error Handling

- Custom exception hierarchy in `features/*/exceptions.py`
- Consistent error responses via `app.api.common.exceptions`
- HTTP status codes follow REST conventions

### Database Models

- SQLModel for type-safe ORM with Pydantic validation
- Audit fields in `app.models.shared.audit.py`
- Multi-tenant base model with `tenant_id`

## 🔧 Development Workflow

### Adding New Features

1. Create feature module in `app/features/`
2. Define models, schemas, services, routes
3. Add comprehensive tests (unit + integration)
4. Register routes in `app/api/v1/api.py`
5. Run test suite before committing

### Database Changes

1. Models are auto-created on startup via SQLModel
2. For production: use Alembic migrations (alembic==1.13.1)

### API Development

- All APIs under `/api/v1/` prefix
- OpenAPI docs auto-generated at `/docs`
- Follow RESTful conventions
- Include proper error responses and validation

## 📱 API Endpoints Structure

### Core Endpoints

- `/` - Root with service info
- `/health` - Health check
- `/docs` - OpenAPI documentation
- `/api/v1/auth/*` - Authentication
- `/api/v1/tenants/*` - Tenant management
- `/api/v1/users/*` - User management
- `/api/v1/members/*` - Member management

### Testing Endpoints

- `/api/v1/test/create-demo-data` - Create test data
- Manual test scenarios in `tests/e2e/scenarios/`

## 🛠️ Configuration

### Environment Variables

- `DATABASE_URL` - Database connection string
- `SECRET_KEY` - JWT signing key
- `DEBUG` - Debug mode flag

### Default Configurations

- Testing: Configurable database, isolated test data
- Production: PostgreSQL, strong secrets required

## 🔍 Debugging & Troubleshooting

### Common Issues

1. **404 errors**: Check route registration in `api.py`
2. **422 validation**: Verify request schema matches Pydantic models
3. **Database errors**: Ensure proper tenant context and foreign keys
4. **Auth failures**: Verify token format and tenant permissions

### Logging

- FastAPI auto-logs requests in debug mode
- Add custom logging as needed for complex business logic
- Test failures show full traceback with `-v` flag

## 📋 Code Standards

### Naming Conventions

- Files: snake_case
- Classes: PascalCase
- Functions/variables: snake_case
- Constants: UPPER_SNAKE_CASE

### Import Organization

1. Standard library
2. Third-party packages
3. App modules (relative imports)

### Type Safety

- Use SQLModel for database models (includes Pydantic validation)
- Type hints required for public functions
- Pydantic schemas for API request/response validation
