#!/usr/bin/env python3
"""
调试API测试数据库连接问题的脚本

主要问题分析：
1. FastAPI lifespan事件是否在测试中被触发
2. 测试数据库连接是否被正确覆盖
3. RLS策略是否在测试数据库中正确设置
"""
import os
import sys
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.testclient import TestClient
from sqlmodel import Session, text, create_engine
import pytest

# 设置测试环境变量
os.environ["TESTING"] = "true"

def test_database_connection_override():
    """测试数据库连接覆盖是否正常工作"""
    print("=== 测试数据库连接覆盖 ===")
    
    # 1. 检查原始数据库连接
    from app.db.session import engine as original_engine
    print(f"原始数据库URL: {original_engine.url}")
    
    # 2. 创建测试数据库引擎
    TEST_DATABASE_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
    test_engine = create_engine(TEST_DATABASE_URL, echo=False)
    print(f"测试数据库URL: {test_engine.url}")
    
    # 3. 测试依赖覆盖
    from app.main import app
    from app.db.session import get_global_session
    
    def get_test_session():
        with Session(test_engine) as session:
            yield session
    
    # 覆盖依赖
    app.dependency_overrides[get_global_session] = get_test_session
    
    # 4. 创建测试客户端
    with TestClient(app) as client:
        print("TestClient创建成功")
        
        # 检查lifespan是否被触发
        print("检查FastAPI lifespan是否被触发...")
        
        # 尝试一个简单的API调用
        try:
            response = client.get("/api/v1/admin/health")
            print(f"Health check response: {response.status_code}")
        except Exception as e:
            print(f"Health check failed: {e}")
    
    # 清理
    app.dependency_overrides.clear()


def test_lifespan_behavior():
    """测试FastAPI lifespan行为"""
    print("\n=== 测试FastAPI Lifespan行为 ===")
    
    # 创建一个简化的FastAPI应用来测试lifespan
    lifespan_called = {"startup": False, "shutdown": False}
    
    @asynccontextmanager
    async def test_lifespan(app: FastAPI):
        print("🚀 测试lifespan启动...")
        lifespan_called["startup"] = True
        
        # 检查数据库连接
        from app.db.session import engine
        print(f"Lifespan中的数据库URL: {engine.url}")
        
        yield
        
        print("👋 测试lifespan关闭...")
        lifespan_called["shutdown"] = True
    
    test_app = FastAPI(lifespan=test_lifespan)
    
    @test_app.get("/test")
    def test_endpoint():
        return {"message": "test"}
    
    with TestClient(test_app) as client:
        response = client.get("/test")
        print(f"Test endpoint response: {response.status_code}")
    
    print(f"Lifespan startup called: {lifespan_called['startup']}")
    print(f"Lifespan shutdown called: {lifespan_called['shutdown']}")


def test_rls_in_test_database():
    """测试RLS在测试数据库中的设置"""
    print("\n=== 测试RLS在测试数据库中的设置 ===")
    
    TEST_DATABASE_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
    
    try:
        test_engine = create_engine(TEST_DATABASE_URL, echo=False)
        
        with Session(test_engine) as session:
            # 检查RLS是否启用
            result = session.exec(text("""
                SELECT relname, relrowsecurity 
                FROM pg_class 
                WHERE relname IN ('member_fixed_slot_locks', 'users', 'members')
                ORDER BY relname
            """)).all()
            
            print("RLS状态:")
            for table_name, rls_enabled in result:
                print(f"  {table_name}: {'启用' if rls_enabled else '禁用'}")
            
            # 检查RLS策略
            policies = session.exec(text("""
                SELECT tablename, policyname, qual 
                FROM pg_policies 
                WHERE tablename IN ('member_fixed_slot_locks', 'users', 'members')
                ORDER BY tablename, policyname
            """)).all()
            
            print("\nRLS策略:")
            for table_name, policy_name, qual in policies:
                print(f"  {table_name}.{policy_name}: {qual}")
            
            # 测试RLS上下文设置
            print("\n测试RLS上下文:")
            
            # 全局模式
            session.exec(text("RESET app.current_tenant_id"))
            current_setting = session.exec(text("SELECT current_setting('app.current_tenant_id', true)")).first()
            print(f"全局模式设置: '{current_setting}'")
            
            # 租户模式
            session.exec(text("SET app.current_tenant_id = '1'"))
            current_setting = session.exec(text("SELECT current_setting('app.current_tenant_id', true)")).first()
            print(f"租户模式设置: '{current_setting}'")
            
            # 检查数据
            count_global = session.exec(text("SELECT COUNT(*) FROM member_fixed_slot_locks")).first()
            print(f"租户1模式下的记录数: {count_global}")
            
            session.exec(text("SET app.current_tenant_id = '2'"))
            count_tenant2 = session.exec(text("SELECT COUNT(*) FROM member_fixed_slot_locks")).first()
            print(f"租户2模式下的记录数: {count_tenant2}")
            
    except Exception as e:
        print(f"测试数据库连接失败: {e}")


def test_dependency_override_in_testclient():
    """测试TestClient中的依赖覆盖"""
    print("\n=== 测试TestClient中的依赖覆盖 ===")
    
    from app.main import app
    from app.db.session import get_global_session, engine as original_engine
    
    # 创建测试引擎
    TEST_DATABASE_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
    test_engine = create_engine(TEST_DATABASE_URL, echo=False)
    
    print(f"原始引擎URL: {original_engine.url}")
    print(f"测试引擎URL: {test_engine.url}")
    
    # 创建测试session生成器
    def get_test_session():
        print("🔧 使用测试数据库session")
        with Session(test_engine) as session:
            yield session
    
    # 覆盖依赖
    app.dependency_overrides[get_global_session] = get_test_session
    
    # 添加一个测试端点来验证数据库连接
    @app.get("/debug/db-info")
    def debug_db_info(session: Session = get_global_session()):
        # 获取当前数据库名称
        db_name = session.exec(text("SELECT current_database()")).first()
        return {"database": db_name}
    
    try:
        with TestClient(app) as client:
            response = client.get("/debug/db-info")
            print(f"数据库信息响应: {response.json()}")
            
            if response.status_code == 200:
                db_name = response.json().get("database")
                if "test" in db_name:
                    print("✅ 成功使用测试数据库")
                else:
                    print(f"❌ 仍在使用生产数据库: {db_name}")
            else:
                print(f"❌ API调用失败: {response.status_code}")
                
    except Exception as e:
        print(f"TestClient测试失败: {e}")
    finally:
        # 清理
        app.dependency_overrides.clear()


if __name__ == "__main__":
    print("🔍 开始调试API测试数据库连接问题...\n")
    
    test_database_connection_override()
    test_lifespan_behavior()
    test_rls_in_test_database()
    test_dependency_override_in_testclient()
    
    print("\n🏁 调试完成")
