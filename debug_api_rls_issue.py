#!/usr/bin/env python3
"""
直接测试API测试中的RLS问题

重点验证：
1. 依赖覆盖是否生效
2. RLS策略是否正确工作
3. 租户隔离是否有效
"""
import os
from fastapi.testclient import TestClient
from sqlmodel import Session, text, create_engine

# 设置测试环境变量
os.environ["TESTING"] = "true"

def test_dependency_override_effectiveness():
    """测试依赖覆盖的有效性"""
    print("=== 测试依赖覆盖有效性 ===")
    
    TEST_DATABASE_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
    test_engine = create_engine(TEST_DATABASE_URL, echo=False)
    
    from app.main import app
    from app.db.session import get_global_session
    
    def get_test_session():
        print("🔧 使用测试数据库session")
        with Session(test_engine) as session:
            # 重置租户上下文为全局模式
            session.exec(text("RESET app.current_tenant_id"))
            yield session
    
    # 覆盖依赖
    app.dependency_overrides[get_global_session] = get_test_session
    
    # 添加一个调试端点
    @app.get("/debug/current-db")
    def debug_current_db(session: Session = get_global_session()):
        db_name = session.exec(text("SELECT current_database()")).first()
        tenant_setting = session.exec(text("SELECT current_setting('app.current_tenant_id', true)")).first()
        return {
            "database": db_name,
            "tenant_context": tenant_setting,
            "message": "依赖覆盖生效"
        }
    
    try:
        with TestClient(app) as client:
            response = client.get("/debug/current-db")
            print(f"调试端点响应: {response.json()}")
            
            if response.status_code == 200:
                data = response.json()
                if "test" in data["database"]:
                    print("✅ 依赖覆盖生效，使用测试数据库")
                    return True
                else:
                    print(f"❌ 依赖覆盖失败，仍使用: {data['database']}")
                    return False
            else:
                print(f"❌ 调试端点失败: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"依赖覆盖测试失败: {e}")
        return False
    finally:
        app.dependency_overrides.clear()


def test_rls_policies_directly():
    """直接测试RLS策略"""
    print("\n=== 直接测试RLS策略 ===")
    
    TEST_DATABASE_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
    test_engine = create_engine(TEST_DATABASE_URL, echo=False)
    
    with Session(test_engine) as session:
        # 1. 检查RLS是否启用
        rls_status = session.exec(text("""
            SELECT relname, relrowsecurity 
            FROM pg_class 
            WHERE relname = 'member_fixed_slot_locks'
        """)).first()
        
        if rls_status:
            table_name, rls_enabled = rls_status
            print(f"表 {table_name} RLS状态: {'启用' if rls_enabled else '禁用'}")
        else:
            print("❌ 表 member_fixed_slot_locks 不存在")
            return False
        
        # 2. 检查RLS策略
        policies = session.exec(text("""
            SELECT policyname, qual 
            FROM pg_policies 
            WHERE tablename = 'member_fixed_slot_locks'
        """)).all()
        
        print(f"RLS策略数量: {len(policies)}")
        for policy_name, qual in policies:
            print(f"  策略: {policy_name}")
            print(f"  条件: {qual}")
        
        # 3. 插入测试数据
        try:
            # 全局模式插入数据
            session.exec(text("RESET app.current_tenant_id"))
            session.exec(text("""
                INSERT INTO member_fixed_slot_locks 
                (tenant_id, member_id, teacher_fixed_slot_id, teacher_id, weekday, start_time, end_time, status, locked_at, created_by)
                VALUES 
                (1, 1, 1, 1, 1, '09:00:00', '09:25:00', 'active', NOW(), 1),
                (2, 2, 2, 2, 2, '10:00:00', '10:25:00', 'active', NOW(), 2)
                ON CONFLICT DO NOTHING
            """))
            session.commit()
            print("测试数据插入成功")
        except Exception as e:
            print(f"测试数据插入失败: {e}")
        
        # 4. 测试RLS隔离
        print("\n测试RLS隔离效果:")
        
        # 全局模式
        session.exec(text("RESET app.current_tenant_id"))
        global_count = session.exec(text("SELECT COUNT(*) FROM member_fixed_slot_locks")).first()
        print(f"全局模式记录数: {global_count}")
        
        # 租户1模式
        session.exec(text("SET app.current_tenant_id = '1'"))
        tenant1_count = session.exec(text("SELECT COUNT(*) FROM member_fixed_slot_locks")).first()
        print(f"租户1模式记录数: {tenant1_count}")
        
        # 租户2模式
        session.exec(text("SET app.current_tenant_id = '2'"))
        tenant2_count = session.exec(text("SELECT COUNT(*) FROM member_fixed_slot_locks")).first()
        print(f"租户2模式记录数: {tenant2_count}")
        
        # 验证隔离效果
        if global_count > tenant1_count and global_count > tenant2_count:
            print("✅ RLS隔离正常工作")
            return True
        else:
            print("❌ RLS隔离可能有问题")
            return False


def test_api_with_auth_tokens():
    """使用认证token测试API"""
    print("\n=== 使用认证token测试API ===")
    
    TEST_DATABASE_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
    test_engine = create_engine(TEST_DATABASE_URL, echo=False)
    
    from app.main import app
    from app.db.session import get_global_session, get_tenant_session
    
    def get_test_global_session():
        with Session(test_engine) as session:
            session.exec(text("RESET app.current_tenant_id"))
            yield session
    
    def get_test_tenant_session(tenant_id: int):
        with Session(test_engine) as session:
            session.exec(text(f"SET app.current_tenant_id = '{tenant_id}'"))
            yield session
    
    # 覆盖依赖
    app.dependency_overrides[get_global_session] = get_test_global_session
    app.dependency_overrides[get_tenant_session] = get_test_tenant_session
    
    try:
        with TestClient(app) as client:
            # 创建一个简单的测试token（模拟）
            # 注意：这里需要根据实际的认证逻辑调整
            
            # 测试无认证的API调用
            response = client.get("/api/v1/admin/members/fixed-locks/")
            print(f"无认证API响应: {response.status_code}")
            
            if response.status_code == 401:
                print("✅ 认证检查正常")
            elif response.status_code == 200:
                data = response.json()
                print(f"API响应数据: {data}")
                print("✅ API调用成功（可能是测试环境跳过了认证）")
            else:
                print(f"❌ 意外的响应状态: {response.status_code}")
                print(f"响应内容: {response.text}")
                
    except Exception as e:
        print(f"API测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        app.dependency_overrides.clear()


def main():
    print("🔍 开始调试API测试中的RLS问题...\n")
    
    # 1. 测试依赖覆盖
    override_ok = test_dependency_override_effectiveness()
    
    # 2. 测试RLS策略
    rls_ok = test_rls_policies_directly()
    
    # 3. 测试API调用
    test_api_with_auth_tokens()
    
    print(f"\n📊 测试结果总结:")
    print(f"  依赖覆盖: {'✅' if override_ok else '❌'}")
    print(f"  RLS策略: {'✅' if rls_ok else '❌'}")
    
    if override_ok and rls_ok:
        print("\n🎉 核心功能正常，API测试失败可能是认证或其他业务逻辑问题")
    else:
        print("\n⚠️  发现核心问题，需要进一步修复")
    
    print("\n🏁 调试完成")


if __name__ == "__main__":
    main()
