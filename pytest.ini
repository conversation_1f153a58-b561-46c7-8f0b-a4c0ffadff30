[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
markers =
    unit: 单元测试
    integration: 集成测试
    api: API测试
    auth: 认证相关测试
    manual: 手动测试场景
    slow: 慢速测试
    database: 需要数据库的测试
    postgres: 使用PostgreSQL数据库的测试
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning 