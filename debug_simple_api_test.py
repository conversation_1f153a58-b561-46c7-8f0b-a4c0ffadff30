#!/usr/bin/env python3
"""
简化的API测试调试脚本
"""
import os
import sys
from fastapi.testclient import TestClient
from sqlmodel import Session, text, create_engine

# 设置测试环境变量
os.environ["TESTING"] = "true"

def create_test_database():
    """手动创建测试数据库"""
    import psycopg2
    from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

    # 连接到postgres数据库
    conn = psycopg2.connect(
        host="localhost",
        port=5432,
        user="admin_alice",
        password="123qwe1985alice",
        database="postgres"
    )
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()

    # 检查数据库是否存在
    cursor.execute("SELECT 1 FROM pg_database WHERE datname = 'course_booking_test_db'")
    exists = cursor.fetchone()

    if exists:
        print("测试数据库已存在，直接使用")
        cursor.close()
        conn.close()
        return True

    # 创建新的测试数据库
    try:
        cursor.execute("CREATE DATABASE course_booking_test_db")
        print("创建测试数据库成功")
    except Exception as e:
        print(f"创建数据库失败: {e}")
        cursor.close()
        conn.close()
        return False

    cursor.close()
    conn.close()
    return True


def setup_test_database():
    """设置测试数据库表结构"""
    TEST_DATABASE_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
    test_engine = create_engine(TEST_DATABASE_URL, echo=False)

    # 创建表结构
    from sqlmodel import SQLModel
    import app.models  # 导入所有模型

    try:
        SQLModel.metadata.create_all(test_engine)
        print("测试数据库表结构创建完成")

        # 验证表是否创建成功
        with Session(test_engine) as session:
            tables = session.exec(text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                ORDER BY table_name
            """)).all()
            print(f"创建的表: {list(tables)}")

    except Exception as e:
        print(f"表结构创建失败: {e}")
        return None

    # 设置RLS
    try:
        from app.db.rls_v2 import RLSPolicyManager
        manager = RLSPolicyManager(test_engine)
        manager.cleanup_old_policies()
        manager.setup_rls_policies_v2("null")
        print("测试数据库RLS策略设置完成")
    except Exception as e:
        print(f"RLS策略设置失败: {e}")

    return test_engine


def test_simple_api_call():
    """测试简单的API调用"""
    print("\n=== 测试简单API调用 ===")
    
    # 1. 创建测试数据库
    if not create_test_database():
        print("❌ 无法创建测试数据库")
        return
    
    # 2. 设置测试数据库
    test_engine = setup_test_database()
    
    # 3. 设置依赖覆盖
    from app.main import app
    from app.db.session import get_global_session, get_session, get_tenant_session
    
    def get_test_session():
        with Session(test_engine) as session:
            session.exec(text("RESET app.current_tenant_id"))  # 全局模式
            yield session
    
    def get_test_tenant_session(tenant_id: int):
        with Session(test_engine) as session:
            session.exec(text(f"SET app.current_tenant_id = '{tenant_id}'"))
            yield session
    
    # 覆盖所有数据库依赖
    app.dependency_overrides[get_global_session] = get_test_session
    app.dependency_overrides[get_session] = get_test_session
    app.dependency_overrides[get_tenant_session] = get_test_tenant_session
    
    # 4. 创建测试客户端并测试
    try:
        with TestClient(app) as client:
            print("TestClient创建成功")
            
            # 测试一个简单的API端点
            response = client.get("/api/v1/admin/members/fixed-locks/")
            print(f"API响应状态码: {response.status_code}")
            
            if response.status_code == 401:
                print("需要认证，这是正常的")
            elif response.status_code == 200:
                data = response.json()
                print(f"API响应数据: {data}")
            else:
                print(f"API响应内容: {response.text}")
                
    except Exception as e:
        print(f"API测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理依赖覆盖
        app.dependency_overrides.clear()


def test_database_isolation():
    """测试数据库隔离"""
    print("\n=== 测试数据库隔离 ===")
    
    TEST_DATABASE_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
    PROD_DATABASE_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_db"
    
    try:
        # 连接测试数据库
        test_engine = create_engine(TEST_DATABASE_URL, echo=False)
        with Session(test_engine) as test_session:
            test_db = test_session.exec(text("SELECT current_database()")).first()
            print(f"测试数据库名称: {test_db}")
        
        # 连接生产数据库
        prod_engine = create_engine(PROD_DATABASE_URL, echo=False)
        with Session(prod_engine) as prod_session:
            prod_db = prod_session.exec(text("SELECT current_database()")).first()
            print(f"生产数据库名称: {prod_db}")
        
        if test_db != prod_db:
            print("✅ 数据库隔离正常")
        else:
            print("❌ 数据库隔离失败")
            
    except Exception as e:
        print(f"数据库隔离测试失败: {e}")


def check_lifespan_database_usage():
    """检查lifespan中使用的数据库"""
    print("\n=== 检查Lifespan数据库使用 ===")
    
    # 检查app.db.session中的engine
    from app.db.session import engine
    print(f"app.db.session.engine URL: {engine.url}")
    
    # 检查settings中的数据库URL
    from app.core.config import settings
    print(f"settings.DATABASE_URL: {settings.DATABASE_URL}")
    
    # 检查环境变量
    print(f"DATABASE_URL环境变量: {os.getenv('DATABASE_URL', '未设置')}")
    print(f"TESTING环境变量: {os.getenv('TESTING', '未设置')}")


if __name__ == "__main__":
    print("🔍 开始简化的API测试调试...\n")
    
    check_lifespan_database_usage()
    test_database_isolation()
    test_simple_api_call()
    
    print("\n🏁 调试完成")
