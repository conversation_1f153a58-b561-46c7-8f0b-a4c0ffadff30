"""
调试dependency_overrides的作用范围
"""
import os
import sys
sys.path.append('.')

from fastapi import FastAPI, Depends
from fastapi.testclient import TestClient
from sqlmodel import Session, create_engine, text
from contextlib import asynccontextmanager

# 模拟数据库依赖
def get_original_session():
    print("🔴 使用原始数据库session")
    return "original_session"

def get_test_session():
    print("🟢 使用测试数据库session")
    return "test_session"

# 模拟lifespan中的数据库操作
@asynccontextmanager
async def lifespan(app: FastAPI):
    print("🚀 FastAPI应用启动中...")
    
    # 在lifespan中直接调用依赖
    session = get_original_session()
    print(f"lifespan中获取的session: {session}")
    
    # 尝试通过app获取依赖（这不会工作，因为没有请求上下文）
    try:
        # 这种方式在lifespan中不会使用dependency_overrides
        print("lifespan中无法直接使用dependency injection")
    except Exception as e:
        print(f"lifespan中使用依赖注入失败: {e}")
    
    yield
    print("👋 FastAPI应用关闭")

# 创建FastAPI应用
app = FastAPI(lifespan=lifespan)

@app.get("/test")
def test_endpoint(session=Depends(get_original_session)):
    print(f"API端点中获取的session: {session}")
    return {"session": session}

def test_dependency_override():
    """测试dependency_overrides的作用范围"""
    print("=" * 50)
    print("测试dependency_overrides的作用范围")
    print("=" * 50)
    
    # 设置dependency override
    app.dependency_overrides[get_original_session] = get_test_session
    
    print("\n1. 创建TestClient（会触发lifespan）:")
    with TestClient(app) as client:
        print("\n2. 调用API端点:")
        response = client.get("/test")
        print(f"API响应: {response.json()}")
    
    print("\n3. 清理dependency overrides:")
    app.dependency_overrides.clear()

if __name__ == "__main__":
    test_dependency_override()
