"""
API重构示例 - 基于你的项目
"""
from fastapi import APIRouter, Depends, Query
from sqlmodel import Session
from typing import List, Optional
from datetime import date

from app.db.session import get_session
from app.api.common.responses import DataResponse, PageResponse, success_response, page_response
from app.features.courses.scheduled_classes_service import ScheduledClassService
from app.features.courses.scheduled_classes_schemas import (
    ScheduledClassList, ScheduledClassRead, MemberClassBooking
)
from design_examples.auth_dependencies import (
    AuthContext, get_admin_context, get_member_context, UserRole
)

# ==================== 管理端课程API ====================
admin_courses_router = APIRouter(prefix="/admin/courses", tags=["管理端-课程管理"])

@admin_courses_router.get("/", response_model=PageResponse[ScheduledClassList])
def admin_get_courses(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    teacher_id: Optional[int] = Query(None),
    member_id: Optional[int] = Query(None),
    status: Optional[str] = Query(None),
    auth: AuthContext = get_admin_context(),
    session: Session = Depends(get_session)
):
    """管理员获取课程列表 - 可以看到所有课程详细信息"""
    service = ScheduledClassService(session, auth.tenant_id)
    
    # 管理员可以查看所有课程
    courses, total = service.get_courses_for_admin(
        page=page, size=size,
        teacher_id=teacher_id,
        member_id=member_id,
        status=status
    )
    
    return page_response(courses, total, page, size, "获取课程列表成功")

@admin_courses_router.post("/{class_id}/assign", response_model=DataResponse[ScheduledClassRead])
def admin_assign_course(
    class_id: int,
    member_id: int,
    auth: AuthContext = get_admin_context(),
    session: Session = Depends(get_session)
):
    """管理员分配课程给会员"""
    service = ScheduledClassService(session, auth.tenant_id)
    
    # 管理员代为预约，记录操作者信息
    course = service.admin_assign_course(
        class_id=class_id,
        member_id=member_id,
        operator_id=auth.user_id,
        operator_name=auth.user_info.get("name")
    )
    
    return success_response(course, "课程分配成功")

# ==================== 会员端课程API ====================
member_courses_router = APIRouter(prefix="/member/courses", tags=["会员端-课程预约"])

@member_courses_router.get("/available", response_model=PageResponse[ScheduledClassList])
def member_get_available_courses(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    teacher_id: Optional[int] = Query(None),
    date_from: Optional[date] = Query(None),
    date_to: Optional[date] = Query(None),
    max_price: Optional[int] = Query(None),
    auth: AuthContext = get_member_context(),
    session: Session = Depends(get_session)
):
    """会员获取可预约课程 - 只显示可预约的课程"""
    service = ScheduledClassService(session, auth.tenant_id)
    
    # 会员只能看到可预约的课程
    courses, total = service.get_available_courses_for_member(
        member_id=auth.user_id,  # 用于检查会员卡余额等
        page=page, size=size,
        teacher_id=teacher_id,
        date_from=date_from,
        date_to=date_to,
        max_price=max_price
    )
    
    return page_response(courses, total, page, size, "获取可预约课程成功")

@member_courses_router.get("/my", response_model=PageResponse[ScheduledClassList])
def member_get_my_courses(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    auth: AuthContext = get_member_context(),
    session: Session = Depends(get_session)
):
    """会员获取自己的课程"""
    service = ScheduledClassService(session, auth.tenant_id)
    
    courses, total = service.get_member_courses(
        member_id=auth.user_id,
        page=page, size=size,
        status=status
    )
    
    return page_response(courses, total, page, size, "获取我的课程成功")

@member_courses_router.post("/{class_id}/book", response_model=DataResponse[ScheduledClassRead])
def member_book_course(
    class_id: int,
    booking_data: MemberClassBooking,
    auth: AuthContext = get_member_context(),
    session: Session = Depends(get_session)
):
    """会员预约课程"""
    service = ScheduledClassService(session, auth.tenant_id)
    
    # 设置课程ID和会员ID
    booking_data.class_id = class_id
    
    course = service.book_course_for_member(
        member_id=auth.user_id,
        booking_data=booking_data
    )
    
    return success_response(course, "课程预约成功")

# ==================== 主路由注册 ====================
def create_v1_router() -> APIRouter:
    """创建v1版本的API路由"""
    v1_router = APIRouter(prefix="/api/v1")
    
    # 注册各角色的路由
    v1_router.include_router(admin_courses_router)
    v1_router.include_router(member_courses_router)
    
    # 未来可以轻松添加
    # v1_router.include_router(teacher_courses_router)
    # v1_router.include_router(super_admin_router)
    
    return v1_router

# ==================== 服务层增强示例 ====================
class EnhancedScheduledClassService(ScheduledClassService):
    """增强的课程服务，支持角色区分"""
    
    def get_courses_for_admin(self, **filters):
        """管理员获取课程 - 包含所有详细信息"""
        # 管理员可以看到收入、成本等敏感信息
        courses = self.get_all_courses_with_analytics(**filters)
        return courses
    
    def get_available_courses_for_member(self, member_id: int, **filters):
        """会员获取可预约课程 - 只显示相关信息"""
        # 检查会员卡余额，过滤不可预约的课程
        member_card = self.get_member_primary_card(member_id)
        
        courses = self.get_available_courses(**filters)
        
        # 过滤掉余额不足的课程
        if filters.get('max_price') is None and member_card:
            courses = [c for c in courses if c.price <= member_card.balance]
        
        return courses
    
    def admin_assign_course(self, class_id: int, member_id: int, 
                          operator_id: int, operator_name: str):
        """管理员分配课程"""
        # 管理员操作，可以跳过某些限制
        return self.book_course_with_admin_privileges(
            class_id=class_id,
            member_id=member_id,
            operator_id=operator_id,
            operator_name=operator_name
        )
    
    def book_course_for_member(self, member_id: int, booking_data: MemberClassBooking):
        """会员预约课程"""
        # 会员操作，需要完整的验证流程
        return self.book_member_class(
            member_id=member_id,
            member_booking_data=booking_data,
            created_by=member_id
        )
