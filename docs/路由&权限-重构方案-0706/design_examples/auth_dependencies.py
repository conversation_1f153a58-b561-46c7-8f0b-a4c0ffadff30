"""
统一权限控制设计示例
"""
from enum import Enum
from typing import Optional, List
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer
from pydantic import BaseModel

class UserRole(str, Enum):
    """用户角色枚举"""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    TEACHER = "teacher"
    MEMBER = "member"

class AuthContext(BaseModel):
    """统一认证上下文"""
    user_id: int
    tenant_id: int
    role: UserRole
    permissions: List[str]
    user_info: dict
    tenant_info: dict

# 基础认证依赖
security = HTTPBearer()

async def get_auth_context(token: str = Depends(security)) -> AuthContext:
    """获取认证上下文（所有角色通用）"""
    # 解析token，获取用户信息
    # 这里是伪代码，实际需要实现token解析逻辑
    user_data = decode_token(token.credentials)
    
    return AuthContext(
        user_id=user_data["user_id"],
        tenant_id=user_data["tenant_id"],
        role=UserRole(user_data["role"]),
        permissions=user_data["permissions"],
        user_info=user_data["user_info"],
        tenant_info=user_data["tenant_info"]
    )

# 角色特定的依赖
def require_role(*allowed_roles: UserRole):
    """角色权限装饰器"""
    def dependency(auth_context: AuthContext = Depends(get_auth_context)) -> AuthContext:
        if auth_context.role not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"需要以下角色之一: {[role.value for role in allowed_roles]}"
            )
        return auth_context
    return dependency

def require_permission(*required_permissions: str):
    """权限检查装饰器"""
    def dependency(auth_context: AuthContext = Depends(get_auth_context)) -> AuthContext:
        missing_permissions = set(required_permissions) - set(auth_context.permissions)
        if missing_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"缺少权限: {list(missing_permissions)}"
            )
        return auth_context
    return dependency

# 便捷的角色依赖
def get_admin_context() -> AuthContext:
    """管理员上下文"""
    return Depends(require_role(UserRole.ADMIN, UserRole.SUPER_ADMIN))

def get_member_context() -> AuthContext:
    """会员上下文"""
    return Depends(require_role(UserRole.MEMBER))

def get_teacher_context() -> AuthContext:
    """教师上下文"""
    return Depends(require_role(UserRole.TEACHER))

def get_any_authenticated_context() -> AuthContext:
    """任何已认证用户"""
    return Depends(get_auth_context)

# 使用示例
"""
@router.get("/admin/users")
def get_users(auth: AuthContext = get_admin_context()):
    # 只有管理员可以访问
    pass

@router.get("/member/profile")
def get_profile(auth: AuthContext = get_member_context()):
    # 只有会员可以访问
    pass

@router.get("/courses")
def get_courses(auth: AuthContext = require_permission("course:read")()):
    # 需要特定权限
    pass
"""
