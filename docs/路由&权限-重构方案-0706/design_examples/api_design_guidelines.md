# API设计一致性指南

## 1. URL命名规范

### 路径结构
```
/api/v1/{role}/{resource}/{action}
```

### 示例
```
# 管理端
GET    /api/v1/admin/courses              # 获取课程列表
POST   /api/v1/admin/courses              # 创建课程
GET    /api/v1/admin/courses/{id}         # 获取课程详情
PUT    /api/v1/admin/courses/{id}         # 更新课程
DELETE /api/v1/admin/courses/{id}         # 删除课程
POST   /api/v1/admin/courses/{id}/assign  # 分配课程

# 会员端
GET    /api/v1/member/courses/available   # 获取可预约课程
GET    /api/v1/member/courses/my          # 获取我的课程
POST   /api/v1/member/courses/{id}/book   # 预约课程
DELETE /api/v1/member/courses/{id}/book   # 取消预约

# 教师端（未来）
GET    /api/v1/teacher/courses/my         # 获取我的课程
POST   /api/v1/teacher/courses            # 创建课程
PUT    /api/v1/teacher/courses/{id}       # 更新课程
```

## 2. 响应格式统一

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {...},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "获取列表成功",
  "data": {
    "items": [...],
    "total": 100,
    "page": 1,
    "size": 20,
    "pages": 5
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "请求参数错误",
  "detail": "具体错误信息",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 3. 权限设计模式

### 基于角色的访问控制(RBAC)
```python
# 角色定义
class UserRole(str, Enum):
    SUPER_ADMIN = "super_admin"    # 超级管理员
    ADMIN = "admin"                # 机构管理员
    TEACHER = "teacher"            # 教师
    MEMBER = "member"              # 会员

# 权限定义
class Permission(str, Enum):
    # 课程权限
    COURSE_CREATE = "course:create"
    COURSE_READ = "course:read"
    COURSE_UPDATE = "course:update"
    COURSE_DELETE = "course:delete"
    COURSE_ASSIGN = "course:assign"
    
    # 会员权限
    MEMBER_CREATE = "member:create"
    MEMBER_READ = "member:read"
    MEMBER_UPDATE = "member:update"
    MEMBER_DELETE = "member:delete"
```

### 角色权限映射
```python
ROLE_PERMISSIONS = {
    UserRole.SUPER_ADMIN: [
        # 拥有所有权限
        Permission.COURSE_CREATE,
        Permission.COURSE_READ,
        Permission.COURSE_UPDATE,
        Permission.COURSE_DELETE,
        Permission.COURSE_ASSIGN,
        Permission.MEMBER_CREATE,
        Permission.MEMBER_READ,
        Permission.MEMBER_UPDATE,
        Permission.MEMBER_DELETE,
    ],
    UserRole.ADMIN: [
        Permission.COURSE_CREATE,
        Permission.COURSE_READ,
        Permission.COURSE_UPDATE,
        Permission.COURSE_ASSIGN,
        Permission.MEMBER_CREATE,
        Permission.MEMBER_READ,
        Permission.MEMBER_UPDATE,
    ],
    UserRole.TEACHER: [
        Permission.COURSE_CREATE,  # 只能创建自己的课程
        Permission.COURSE_READ,    # 只能查看自己的课程
        Permission.COURSE_UPDATE,  # 只能更新自己的课程
    ],
    UserRole.MEMBER: [
        Permission.COURSE_READ,    # 只能查看可预约的课程
    ]
}
```

## 4. 数据过滤策略

### 基于角色的数据过滤
```python
class CourseFilter:
    @staticmethod
    def filter_for_admin(courses: List[Course]) -> List[Course]:
        """管理员可以看到所有课程"""
        return courses
    
    @staticmethod
    def filter_for_member(courses: List[Course], member_id: int) -> List[Course]:
        """会员只能看到可预约的课程"""
        return [
            course for course in courses
            if course.is_visible_to_member and course.status == "available"
        ]
    
    @staticmethod
    def filter_for_teacher(courses: List[Course], teacher_id: int) -> List[Course]:
        """教师只能看到自己的课程"""
        return [
            course for course in courses
            if course.teacher_id == teacher_id
        ]
```

## 5. 错误处理统一

### 业务异常定义
```python
class BusinessException(Exception):
    def __init__(self, message: str, code: str = None, detail: str = None):
        self.message = message
        self.code = code or "BUSINESS_ERROR"
        self.detail = detail

class PermissionDeniedError(BusinessException):
    def __init__(self, action: str):
        super().__init__(
            message=f"没有权限执行操作: {action}",
            code="PERMISSION_DENIED"
        )

class ResourceNotFoundError(BusinessException):
    def __init__(self, resource: str, resource_id: int):
        super().__init__(
            message=f"{resource} 不存在",
            code="RESOURCE_NOT_FOUND",
            detail=f"{resource} ID: {resource_id}"
        )
```

## 6. 文档和测试

### API文档标准
```python
@router.post("/courses/{class_id}/book")
async def book_course(
    class_id: int = Path(..., description="课程ID", example=123),
    booking_data: MemberClassBooking = Body(..., description="预约信息"),
    auth: AuthContext = Depends(get_member_context())
):
    """
    预约课程
    
    **业务规则:**
    - 只有会员可以预约课程
    - 需要有足够的会员卡余额
    - 课程必须是可预约状态
    - 不能重复预约同一课程
    
    **返回数据:**
    - 预约成功的课程详情
    - 包含扣费信息
    """
    pass
```

### 测试用例组织
```python
class TestAdminCourseAPI:
    """管理端课程API测试"""
    
    def test_admin_can_view_all_courses(self):
        """测试管理员可以查看所有课程"""
        pass
    
    def test_admin_can_assign_course(self):
        """测试管理员可以分配课程"""
        pass

class TestMemberCourseAPI:
    """会员端课程API测试"""
    
    def test_member_can_view_available_courses(self):
        """测试会员可以查看可预约课程"""
        pass
    
    def test_member_can_book_course(self):
        """测试会员可以预约课程"""
        pass
    
    def test_member_cannot_view_admin_data(self):
        """测试会员无法访问管理员数据"""
        pass
```
