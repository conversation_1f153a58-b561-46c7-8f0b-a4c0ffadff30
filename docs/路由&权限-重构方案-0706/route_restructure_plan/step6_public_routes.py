# Step 6: 创建公共API路由

# 1. 创建 app/api/v1/public/__init__.py
public_init_content = '''"""
公共API模块
"""
'''

# 2. 创建 app/api/v1/public/auth.py
public_auth_content = '''"""
公共API - 认证相关
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session

from app.api.v1.endpoints.auth import router as original_router
from app.db.session import get_session

# 直接使用原有的认证路由
router = original_router
'''

# 3. 创建 app/api/v1/public/info.py
public_info_content = '''"""
公共API - 公开信息
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session

from app.db.session import get_session
from app.api.common.responses import DataResponse, success_response

router = APIRouter()

@router.get("/health")
def health_check():
    """健康检查"""
    return {"status": "ok", "message": "服务正常运行"}

@router.get("/version")
def get_version():
    """获取API版本信息"""
    return success_response({
        "version": "1.0.0",
        "api_version": "v1",
        "description": "KS English Admin Backend API"
    }, "获取版本信息成功")

# 未来可以添加其他公开信息接口
# 如：获取公开的课程信息、教师信息等
'''

# 文件创建指令
files_to_create = {
    "app/api/v1/public/__init__.py": public_init_content,
    "app/api/v1/public/auth.py": public_auth_content,
    "app/api/v1/public/info.py": public_info_content,
}
