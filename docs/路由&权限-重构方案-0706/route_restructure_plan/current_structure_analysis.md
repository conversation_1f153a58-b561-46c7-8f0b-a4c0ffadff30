# 当前路由结构分析

## 现有API路由分布

### 1. 管理端API（在 features/ 下）
```
app/features/tenants/router.py          -> /api/v1/tenants/*
app/features/users/router.py            -> /api/v1/users/*
app/features/members/router.py          -> /api/v1/members/*
app/features/members/fixed_lock_router.py -> /api/v1/members/fixed-locks/*
app/features/tags/router.py             -> /api/v1/tags/*
app/features/teachers/router.py         -> /api/v1/teachers/*
app/features/courses/config_router.py   -> /api/v1/courses/*
app/features/courses/scheduled_classes_router.py -> /api/v1/courses/classes/*
```

### 2. 会员端API（在 endpoints/ 下）
```
app/api/v1/endpoints/member_app.py      -> /api/v1/member-app/*
```

### 3. 其他API
```
app/api/v1/endpoints/auth.py            -> /api/v1/auth/*
app/api/v1/endpoints/test.py            -> /api/v1/test/*
```

## 需要创建的会员卡API（任务7.5）

### 管理端会员卡API
- 会员卡模板管理 API
- 会员卡管理 API  
- 充值管理 API
- 消费记录查询 API

### 会员端会员卡API
- 我的会员卡查询
- 充值记录查询
- 消费记录查询

## 目标结构

### 管理端API
```
app/api/v1/admin/
├── tenants.py          # 租户管理
├── users.py            # 用户管理
├── members.py          # 会员管理
├── teachers.py         # 教师管理
├── tags.py             # 标签管理
├── courses.py          # 课程管理
├── member_cards.py     # 会员卡管理（新增）
└── analytics.py        # 数据分析（未来）
```

### 会员端API
```
app/api/v1/member/
├── profile.py          # 个人信息
├── courses.py          # 课程预约
├── cards.py            # 会员卡（新增）
└── records.py          # 记录查询（新增）
```

### 公共API
```
app/api/v1/public/
├── auth.py             # 认证
└── info.py             # 公开信息
```
