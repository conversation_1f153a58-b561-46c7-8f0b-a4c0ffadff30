# Step 7: 更新主路由注册文件

# 新的 app/api/v1/api.py 内容
new_api_content = '''"""
API v1 路由注册
"""
from fastapi import APIRouter

# 导入管理端路由
from .admin import (
    tenants, users, members, teachers, tags, courses, member_cards
)

# 导入会员端路由  
from .member import (
    profile, courses as member_courses, cards, records
)

# 导入公共路由
from .public import auth, info

# 导入测试路由（保持不变）
from .endpoints import test

def create_api_router() -> APIRouter:
    """创建API路由"""
    api_router = APIRouter()
    
    # ==================== 管理端路由组 ====================
    api_router.include_router(
        tenants.router, 
        prefix="/admin/tenants", 
        tags=["管理端-租户管理"]
    )
    api_router.include_router(
        users.router, 
        prefix="/admin/users", 
        tags=["管理端-用户管理"]
    )
    api_router.include_router(
        members.router, 
        prefix="/admin/members", 
        tags=["管理端-会员管理"]
    )
    api_router.include_router(
        teachers.router, 
        prefix="/admin/teachers", 
        tags=["管理端-教师管理"]
    )
    api_router.include_router(
        tags.router, 
        prefix="/admin/tags", 
        tags=["管理端-标签管理"]
    )
    api_router.include_router(
        courses.router, 
        prefix="/admin/courses", 
        tags=["管理端-课程管理"]
    )
    api_router.include_router(
        member_cards.router, 
        prefix="/admin/member-cards", 
        tags=["管理端-会员卡管理"]
    )
    
    # ==================== 会员端路由组 ====================
    api_router.include_router(
        profile.router, 
        prefix="/member/profile", 
        tags=["会员端-个人中心"]
    )
    api_router.include_router(
        member_courses.router, 
        prefix="/member/courses", 
        tags=["会员端-课程预约"]
    )
    api_router.include_router(
        cards.router, 
        prefix="/member/cards", 
        tags=["会员端-会员卡"]
    )
    api_router.include_router(
        records.router, 
        prefix="/member/records", 
        tags=["会员端-记录查询"]
    )
    
    # ==================== 公共路由组 ====================
    api_router.include_router(
        auth.router, 
        prefix="/auth", 
        tags=["认证"]
    )
    api_router.include_router(
        info.router, 
        prefix="/info", 
        tags=["公开信息"]
    )
    
    # ==================== 测试路由（保持不变） ====================
    api_router.include_router(
        test.router, 
        prefix="/test", 
        tags=["测试"]
    )
    
    return api_router

# 创建路由实例
api_router = create_api_router()
'''

# 备份原有的 api.py 文件内容
backup_api_content = '''"""
原有 API 路由注册（备份）
"""
from fastapi import APIRouter
from app.api.v1.endpoints import auth, member_app, test
from app.features.tenants.router import router as tenants_router
from app.features.users.router import router as users_router
from app.features.members.router import router as members_router
from app.features.members.fixed_lock_router import router as member_fixed_locks_router
from app.features.tags.router import router as tags_router
from app.features.teachers.router import router as teachers_router
from app.features.courses.config_router import router as course_config_router
from app.features.courses.scheduled_classes_router import router as scheduled_classes_router

api_router = APIRouter()

# 注册路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users_router, prefix="/users", tags=["用户管理"])
api_router.include_router(members_router, prefix="/members", tags=["会员管理"])
api_router.include_router(member_fixed_locks_router, prefix="/members/fixed-locks", tags=["会员固定课位锁定"])
api_router.include_router(tenants_router, prefix="/tenants", tags=["租户管理"])
api_router.include_router(tags_router, prefix="/tags", tags=["标签管理"])
api_router.include_router(teachers_router, prefix="/teachers", tags=["教师管理"])
api_router.include_router(course_config_router, prefix="/courses", tags=["课程系统配置"])
api_router.include_router(scheduled_classes_router, prefix="/courses/classes", tags=["已排课表"])
api_router.include_router(member_app.router, prefix="/member-app", tags=["会员端"])
api_router.include_router(test.router, prefix="/test", tags=["测试"])
'''

# 操作指令
operations = {
    "backup_original": "cp app/api/v1/api.py app/api/v1/api.py.backup",
    "create_new_api": new_api_content,
    "backup_content": backup_api_content
}
