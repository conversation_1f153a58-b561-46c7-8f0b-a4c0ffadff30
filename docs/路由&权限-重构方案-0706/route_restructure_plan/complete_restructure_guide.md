# 完整的路由结构重构操作指南

## 前置准备

### 1. 创建备份分支
```bash
git checkout -b backup-before-restructure
git push origin backup-before-restructure
git checkout feature/task-7.4-member-cards-integration
```

### 2. 创建新目录结构
```bash
mkdir -p app/api/v1/admin
mkdir -p app/api/v1/member
mkdir -p app/api/v1/public
```

## 第一阶段：创建新的路由文件

### Step 1: 创建管理端路由文件

```bash
# 创建管理端目录初始化文件
cat > app/api/v1/admin/__init__.py << 'EOF'
"""
管理端API模块
"""
EOF

# 创建租户管理路由
cat > app/api/v1/admin/tenants.py << 'EOF'
"""
管理端 - 租户管理API
"""
from app.features.tenants.router import router
EOF

# 创建用户管理路由
cat > app/api/v1/admin/users.py << 'EOF'
"""
管理端 - 用户管理API
"""
from app.features.users.router import router
EOF

# 创建会员管理路由
cat > app/api/v1/admin/members.py << 'EOF'
"""
管理端 - 会员管理API
"""
from fastapi import APIRouter
from app.features.members.router import router as members_router
from app.features.members.fixed_lock_router import router as fixed_locks_router

router = APIRouter()
router.include_router(members_router, prefix="", tags=["管理端-会员管理"])
router.include_router(fixed_locks_router, prefix="/fixed-locks", tags=["管理端-会员固定课位"])
EOF

# 创建教师管理路由
cat > app/api/v1/admin/teachers.py << 'EOF'
"""
管理端 - 教师管理API
"""
from app.features.teachers.router import router
EOF

# 创建标签管理路由
cat > app/api/v1/admin/tags.py << 'EOF'
"""
管理端 - 标签管理API
"""
from app.features.tags.router import router
EOF

# 创建课程管理路由
cat > app/api/v1/admin/courses.py << 'EOF'
"""
管理端 - 课程管理API
"""
from fastapi import APIRouter
from app.features.courses.config_router import router as config_router
from app.features.courses.scheduled_classes_router import router as classes_router

router = APIRouter()
router.include_router(config_router, prefix="/config", tags=["管理端-课程配置"])
router.include_router(classes_router, prefix="/classes", tags=["管理端-课程管理"])
EOF
```

### Step 2: 创建会员卡管理路由（任务7.5）

```bash
# 创建会员卡管理路由（这是任务7.5的新文件）
cat > app/api/v1/admin/member_cards.py << 'EOF'
"""
管理端 - 会员卡管理API
"""
from fastapi import APIRouter, Depends, Query, status
from sqlmodel import Session
from typing import List, Optional

from app.db.session import get_session
from app.core.dependencies import get_user_context, UserContext
from app.api.common.responses import DataResponse, PageResponse, success_response, page_response

# 会员卡相关导入
from app.features.member_cards.template_service import MemberCardTemplateService
from app.features.member_cards.card_service import MemberCardService
from app.features.member_cards.recharge_service import RechargeService
from app.features.member_cards.consumption_service import ConsumptionService
from app.features.member_cards.schemas import (
    # 模板相关
    MemberCardTemplateCreate, MemberCardTemplateUpdate, MemberCardTemplateRead, 
    MemberCardTemplateList, MemberCardTemplateQuery,
    # 卡片相关
    MemberCardCreate, MemberCardUpdate, MemberCardRead, MemberCardList, MemberCardQuery,
    # 充值相关
    RechargeRequest, RechargeResponse,
    # 操作记录相关
    MemberCardOperationRead, MemberCardOperationQuery
)

router = APIRouter()

# ==================== 会员卡模板管理 ====================

@router.get("/templates", response_model=PageResponse[MemberCardTemplateList])
def get_card_templates(
    query: MemberCardTemplateQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡模板列表"""
    service = MemberCardTemplateService(session, user_context.tenant_context.tenant_id)
    templates, total = service.get_templates(query)
    return page_response(templates, total, query.page, query.size, "获取模板列表成功")

@router.post("/templates", response_model=DataResponse[MemberCardTemplateRead], status_code=status.HTTP_201_CREATED)
def create_card_template(
    template_data: MemberCardTemplateCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建会员卡模板"""
    service = MemberCardTemplateService(session, user_context.tenant_context.tenant_id)
    template = service.create_template(template_data, created_by=user_context.user.id)
    return success_response(template, "创建模板成功")

# ==================== 会员卡管理 ====================

@router.get("/cards", response_model=PageResponse[MemberCardList])
def get_member_cards(
    query: MemberCardQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡列表"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    cards, total = service.get_cards(query)
    return page_response(cards, total, query.page, query.size, "获取会员卡列表成功")

@router.post("/cards", response_model=DataResponse[MemberCardRead], status_code=status.HTTP_201_CREATED)
def create_member_card(
    card_data: MemberCardCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建会员卡"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    card = service.create_card(card_data, created_by=user_context.user.id)
    return success_response(card, "创建会员卡成功")

# ==================== 充值管理 ====================

@router.post("/cards/{card_id}/recharge", response_model=DataResponse[RechargeResponse])
def recharge_member_card(
    card_id: int,
    recharge_data: RechargeRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """会员卡充值"""
    service = RechargeService(session, user_context.tenant_context.tenant_id)
    result = service.recharge(card_id, recharge_data, operator_id=user_context.user.id)
    return success_response(result, "充值成功")

# ==================== 操作记录查询 ====================

@router.get("/operations", response_model=PageResponse[MemberCardOperationRead])
def get_card_operations(
    query: MemberCardOperationQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡操作记录"""
    service = ConsumptionService(session, user_context.tenant_context.tenant_id)
    operations, total = service.get_operations(query)
    return page_response(operations, total, query.page, query.size, "获取操作记录成功")
EOF
```

### Step 3: 创建会员端路由文件

```bash
# 创建会员端目录初始化文件
cat > app/api/v1/member/__init__.py << 'EOF'
"""
会员端API模块
"""
EOF

# 创建个人信息管理路由
cat > app/api/v1/member/profile.py << 'EOF'
"""
会员端 - 个人信息管理API
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session

from app.db.session import get_session
from app.core.dependencies import get_member_context, MemberContext
from app.api.common.responses import DataResponse, success_response
from app.features.members.schemas import MemberRead, MemberUpdate
from app.features.members.service import get_member_service

router = APIRouter()

@router.get("/me", response_model=DataResponse[MemberRead])
def get_my_profile(member_context: MemberContext = Depends(get_member_context)):
    """获取我的个人信息"""
    return success_response(member_context.member, "获取个人信息成功")

@router.put("/me", response_model=DataResponse[MemberRead])
def update_my_profile(
    update_data: MemberUpdate,
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """更新我的个人信息"""
    service = get_member_service(session, member_context.tenant_context.tenant_id)
    updated_member = service.update_member(
        member_context.member.id, update_data, updated_by=member_context.member.id
    )
    return success_response(updated_member, "更新个人信息成功")
EOF

# 创建课程预约路由（从 member_app.py 迁移）
cat > app/api/v1/member/courses.py << 'EOF'
"""
会员端 - 课程预约API
"""
from fastapi import APIRouter, Depends, Query
from sqlmodel import Session
from typing import Optional
from datetime import date, time

from app.db.session import get_session
from app.core.dependencies import get_member_context, MemberContext
from app.api.common.responses import DataResponse, PageResponse, success_response, page_response
from app.features.courses.scheduled_classes_service import ScheduledClassService
from app.features.courses.scheduled_classes_schemas import (
    ScheduledClassList, ScheduledClassRead, MemberClassBooking
)

router = APIRouter()

@router.get("/available", response_model=PageResponse[ScheduledClassList])
def get_available_courses(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    teacher_id: Optional[int] = Query(None, gt=0, description="教师ID"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    max_price: Optional[int] = Query(None, ge=0, description="最高价格"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取可预约课程列表"""
    tenant_id = member_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    from datetime import datetime
    available_classes = service.get_available_classes(
        teacher_id=teacher_id,
        date_from=datetime.combine(date_from, time.min) if date_from else None,
        date_to=datetime.combine(date_to, time.max) if date_to else None
    )
    
    if max_price is not None:
        available_classes = [cls for cls in available_classes if cls.price is None or cls.price <= max_price]
    
    classes_response = [ScheduledClassList.model_validate(cls) for cls in available_classes]
    
    return page_response(classes_response, len(classes_response), page, size, "获取可预约课程列表成功")

@router.get("/my", response_model=PageResponse[ScheduledClassList])
def get_my_courses(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    status: Optional[str] = Query(None, description="课程状态"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的课程列表"""
    tenant_id = member_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    from datetime import datetime
    member_classes = service.get_member_classes(
        member_id=member_context.member.id,
        date_from=datetime.combine(date_from, time.min) if date_from else None,
        date_to=datetime.combine(date_to, time.max) if date_to else None,
        status=status
    )
    
    classes_response = [ScheduledClassList.model_validate(cls) for cls in member_classes]
    
    return page_response(classes_response, len(classes_response), page, size, "获取我的课程列表成功")

@router.post("/{class_id}/book", response_model=DataResponse[ScheduledClassRead])
def book_course(
    class_id: int,
    booking_data: MemberClassBooking,
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """预约课程 - 会员自主预约（支持会员卡自动扣费）"""
    tenant_id = member_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    booking_data.class_id = class_id
    
    scheduled_class = service.book_member_class(
        member_id=member_context.member.id,
        member_booking_data=booking_data,
        created_by=member_context.member.id
    )
    
    class_response = ScheduledClassRead.model_validate(scheduled_class)
    return success_response(class_response, "课程预约成功")
EOF

# 创建会员卡管理路由
cat > app/api/v1/member/cards.py << 'EOF'
"""
会员端 - 会员卡管理API
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session
from typing import List

from app.db.session import get_session
from app.core.dependencies import get_member_context, MemberContext
from app.api.common.responses import DataResponse, success_response
from app.features.member_cards.card_service import MemberCardService
from app.features.member_cards.schemas import MemberCardRead, MemberCardSummary

router = APIRouter()

@router.get("/my", response_model=DataResponse[List[MemberCardRead]])
def get_my_cards(
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的会员卡列表"""
    service = MemberCardService(session, member_context.tenant_context.tenant_id)
    cards = service.get_member_active_cards(member_context.member.id)
    cards_response = [MemberCardRead.model_validate(card) for card in cards]
    return success_response(cards_response, "获取我的会员卡成功")

@router.get("/my/primary", response_model=DataResponse[MemberCardRead])
def get_my_primary_card(
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的主要会员卡"""
    service = MemberCardService(session, member_context.tenant_context.tenant_id)
    card = service.get_card(member_context.member.primary_member_card_id)
    return success_response(card, "获取主要会员卡成功")
EOF

# 创建记录查询路由
cat > app/api/v1/member/records.py << 'EOF'
"""
会员端 - 记录查询API
"""
from fastapi import APIRouter, Depends, Query
from sqlmodel import Session
from typing import Optional
from datetime import date

from app.db.session import get_session
from app.core.dependencies import get_member_context, MemberContext
from app.api.common.responses import PageResponse, page_response
from app.features.member_cards.consumption_service import ConsumptionService
from app.features.member_cards.schemas import MemberCardOperationRead, MemberCardOperationQuery

router = APIRouter()

@router.get("/operations", response_model=PageResponse[MemberCardOperationRead])
def get_my_card_operations(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    operation_type: Optional[str] = Query(None, description="操作类型"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的会员卡操作记录"""
    service = ConsumptionService(session, member_context.tenant_context.tenant_id)
    
    query = MemberCardOperationQuery(
        member_id=member_context.member.id,
        operation_type=operation_type,
        date_from=date_from,
        date_to=date_to,
        page=page,
        size=size
    )
    
    operations, total = service.get_operations(query)
    return page_response(operations, total, page, size, "获取操作记录成功")
EOF
```

### Step 4: 创建公共API路由

```bash
# 创建公共API目录初始化文件
cat > app/api/v1/public/__init__.py << 'EOF'
"""
公共API模块
"""
EOF

# 创建认证路由
cat > app/api/v1/public/auth.py << 'EOF'
"""
公共API - 认证相关
"""
from app.api.v1.endpoints.auth import router
EOF

# 创建公开信息路由
cat > app/api/v1/public/info.py << 'EOF'
"""
公共API - 公开信息
"""
from fastapi import APIRouter
from app.api.common.responses import DataResponse, success_response

router = APIRouter()

@router.get("/health")
def health_check():
    """健康检查"""
    return {"status": "ok", "message": "服务正常运行"}

@router.get("/version")
def get_version():
    """获取API版本信息"""
    return success_response({
        "version": "1.0.0",
        "api_version": "v1",
        "description": "KS English Admin Backend API"
    }, "获取版本信息成功")
EOF
```

## 第二阶段：更新主路由注册

### Step 5: 备份并更新主路由文件

```bash
# 备份原有的 api.py 文件
cp app/api/v1/api.py app/api/v1/api.py.backup

# 创建新的 api.py 文件
cat > app/api/v1/api.py << 'EOF'
"""
API v1 路由注册
"""
from fastapi import APIRouter

# 导入管理端路由
from .admin import tenants, users, members, teachers, tags, courses, member_cards

# 导入会员端路由  
from .member import profile, courses as member_courses, cards, records

# 导入公共路由
from .public import auth, info

# 导入测试路由（保持不变）
from .endpoints import test

def create_api_router() -> APIRouter:
    """创建API路由"""
    api_router = APIRouter()
    
    # ==================== 管理端路由组 ====================
    api_router.include_router(tenants.router, prefix="/admin/tenants", tags=["管理端-租户管理"])
    api_router.include_router(users.router, prefix="/admin/users", tags=["管理端-用户管理"])
    api_router.include_router(members.router, prefix="/admin/members", tags=["管理端-会员管理"])
    api_router.include_router(teachers.router, prefix="/admin/teachers", tags=["管理端-教师管理"])
    api_router.include_router(tags.router, prefix="/admin/tags", tags=["管理端-标签管理"])
    api_router.include_router(courses.router, prefix="/admin/courses", tags=["管理端-课程管理"])
    api_router.include_router(member_cards.router, prefix="/admin/member-cards", tags=["管理端-会员卡管理"])
    
    # ==================== 会员端路由组 ====================
    api_router.include_router(profile.router, prefix="/member/profile", tags=["会员端-个人中心"])
    api_router.include_router(member_courses.router, prefix="/member/courses", tags=["会员端-课程预约"])
    api_router.include_router(cards.router, prefix="/member/cards", tags=["会员端-会员卡"])
    api_router.include_router(records.router, prefix="/member/records", tags=["会员端-记录查询"])
    
    # ==================== 公共路由组 ====================
    api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
    api_router.include_router(info.router, prefix="/info", tags=["公开信息"])
    
    # ==================== 测试路由（保持不变） ====================
    api_router.include_router(test.router, prefix="/test", tags=["测试"])
    
    return api_router

# 创建路由实例
api_router = create_api_router()
EOF
```

## 第三阶段：测试和验证

### Step 6: 运行测试验证

```bash
# 检查语法错误
python -m py_compile app/api/v1/api.py

# 运行基础测试
python -m pytest tests/unit/features/courses/test_scheduled_class_service.py::TestScheduledClassMemberCardIntegration::test_book_class_with_sufficient_balance_should_deduct_fee -v

# 启动服务器测试
python -m uvicorn app.main:app --reload --port 8000
```

### Step 7: 验证API文档

访问 `http://localhost:8000/docs` 检查：
1. 管理端API是否正确分组
2. 会员端API是否正确分组
3. 所有路由是否正常加载
4. 新的会员卡API是否出现

## 第四阶段：清理和提交

### Step 8: 清理旧文件（可选）

```bash
# 可以选择保留 member_app.py 作为备份，或者删除
# mv app/api/v1/endpoints/member_app.py app/api/v1/endpoints/member_app.py.backup
```

### Step 9: 提交更改

```bash
git add .
git commit -m "feat: 重构API路由结构，按角色分组管理

- 创建管理端API路由组 (/admin/*)
- 创建会员端API路由组 (/member/*)  
- 创建公共API路由组 (/auth, /info)
- 实现任务7.5会员卡管理API接口
- 保持向后兼容，所有现有功能正常工作"
```

## 注意事项和问题解决

### 可能遇到的问题：

1. **导入错误**：检查所有import路径是否正确
2. **路由冲突**：确保新旧路由没有重复注册
3. **依赖注入问题**：确保所有Depends()正确导入
4. **测试失败**：逐个检查每个模块的导入和功能

### 回滚方案：

如果出现问题，可以快速回滚：
```bash
git checkout backup-before-restructure
```

或者恢复原有的api.py：
```bash
cp app/api/v1/api.py.backup app/api/v1/api.py
```
