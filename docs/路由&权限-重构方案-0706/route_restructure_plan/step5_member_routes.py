# Step 5: 创建会员端路由文件

# 1. 创建 app/api/v1/member/__init__.py
member_init_content = '''"""
会员端API模块
"""
'''

# 2. 创建 app/api/v1/member/profile.py
member_profile_content = '''"""
会员端 - 个人信息管理API
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session
from typing import Optional

from app.db.session import get_session
from app.core.dependencies import get_member_context, MemberContext
from app.api.common.responses import DataResponse, success_response

from app.features.members.schemas import MemberRead, MemberUpdate
from app.features.members.service import get_member_service

router = APIRouter()

@router.get("/me", response_model=DataResponse[MemberRead])
def get_my_profile(
    member_context: MemberContext = Depends(get_member_context)
):
    """获取我的个人信息"""
    return success_response(member_context.member, "获取个人信息成功")

@router.put("/me", response_model=DataResponse[MemberRead])
def update_my_profile(
    update_data: MemberUpdate,
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """更新我的个人信息"""
    service = get_member_service(session, member_context.tenant_context.tenant_id)
    
    # 会员只能更新自己的信息
    updated_member = service.update_member(
        member_context.member.id, 
        update_data, 
        updated_by=member_context.member.id
    )
    
    return success_response(updated_member, "更新个人信息成功")

@router.get("/tenant-info")
def get_tenant_info(
    member_context: MemberContext = Depends(get_member_context)
):
    """获取租户信息"""
    tenant_info = {
        "tenant_id": member_context.tenant_context.tenant_id,
        "tenant_name": member_context.tenant_context.tenant_name,
        "member_id": member_context.member.id,
        "member_name": member_context.member.name
    }
    return success_response(tenant_info, "获取租户信息成功")
'''

# 3. 创建 app/api/v1/member/courses.py
member_courses_content = '''"""
会员端 - 课程预约API
"""
from fastapi import APIRouter, Depends, Query
from sqlmodel import Session
from typing import List, Optional
from datetime import date, time

from app.db.session import get_session
from app.core.dependencies import get_member_context, MemberContext
from app.api.common.responses import DataResponse, PageResponse, success_response, page_response

from app.features.courses.scheduled_classes_service import ScheduledClassService
from app.features.courses.scheduled_classes_schemas import (
    ScheduledClassList, ScheduledClassRead, MemberClassBooking
)

router = APIRouter()

@router.get("/available", response_model=PageResponse[ScheduledClassList])
def get_available_courses(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    teacher_id: Optional[int] = Query(None, gt=0, description="教师ID"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    time_from: Optional[time] = Query(None, description="时间段开始"),
    time_to: Optional[time] = Query(None, description="时间段结束"),
    max_price: Optional[int] = Query(None, ge=0, description="最高价格"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取可预约课程列表"""
    tenant_id = member_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 获取可用课程列表
    from datetime import datetime
    available_classes = service.get_available_classes(
        teacher_id=teacher_id,
        date_from=datetime.combine(date_from, time.min) if date_from else None,
        date_to=datetime.combine(date_to, time.max) if date_to else None
    )
    
    # 价格过滤
    if max_price is not None:
        available_classes = [cls for cls in available_classes if cls.price is None or cls.price <= max_price]
    
    # 转换为响应模型
    classes_response = [
        ScheduledClassList.model_validate(cls) 
        for cls in available_classes
    ]
    
    return page_response(
        items=classes_response,
        total=len(classes_response),
        page=page,
        size=size,
        message="获取可预约课程列表成功"
    )

@router.get("/my", response_model=PageResponse[ScheduledClassList])
def get_my_courses(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    status: Optional[str] = Query(None, description="课程状态"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的课程列表"""
    tenant_id = member_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 获取会员课程
    from datetime import datetime
    member_classes = service.get_member_classes(
        member_id=member_context.member.id,
        date_from=datetime.combine(date_from, time.min) if date_from else None,
        date_to=datetime.combine(date_to, time.max) if date_to else None,
        status=status
    )
    
    # 转换为响应模型
    classes_response = [
        ScheduledClassList.model_validate(cls) 
        for cls in member_classes
    ]
    
    return page_response(
        items=classes_response,
        total=len(classes_response),
        page=page,
        size=size,
        message="获取我的课程列表成功"
    )

@router.post("/{class_id}/book", response_model=DataResponse[ScheduledClassRead])
def book_course(
    class_id: int,
    booking_data: MemberClassBooking,
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """预约课程 - 会员自主预约（支持会员卡自动扣费）"""
    tenant_id = member_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 设置课程ID
    booking_data.class_id = class_id
    
    # 会员自主预约课程
    scheduled_class = service.book_member_class(
        member_id=member_context.member.id,
        member_booking_data=booking_data,
        created_by=member_context.member.id  # 会员自己操作
    )
    
    # 转换为响应模型
    class_response = ScheduledClassRead.model_validate(scheduled_class)
    
    return success_response(class_response, "课程预约成功")
'''

# 4. 创建 app/api/v1/member/cards.py
member_cards_content = '''"""
会员端 - 会员卡管理API
"""
from fastapi import APIRouter, Depends, Query
from sqlmodel import Session
from typing import List, Optional

from app.db.session import get_session
from app.core.dependencies import get_member_context, MemberContext
from app.api.common.responses import DataResponse, PageResponse, success_response, page_response

from app.features.member_cards.card_service import MemberCardService
from app.features.member_cards.schemas import (
    MemberCardRead, MemberCardList, MemberCardSummary
)

router = APIRouter()

@router.get("/my", response_model=DataResponse[List[MemberCardRead]])
def get_my_cards(
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的会员卡列表"""
    service = MemberCardService(session, member_context.tenant_context.tenant_id)
    
    # 获取会员的所有激活卡片
    cards = service.get_member_active_cards(member_context.member.id)
    
    # 转换为响应模型
    cards_response = [MemberCardRead.model_validate(card) for card in cards]
    
    return success_response(cards_response, "获取我的会员卡成功")

@router.get("/my/primary", response_model=DataResponse[MemberCardRead])
def get_my_primary_card(
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的主要会员卡"""
    service = MemberCardService(session, member_context.tenant_context.tenant_id)
    
    # 获取主要会员卡（通常是储值卡）
    card = service.get_card(member_context.member.primary_member_card_id)
    
    return success_response(card, "获取主要会员卡成功")

@router.get("/my/summary", response_model=DataResponse[MemberCardSummary])
def get_my_cards_summary(
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的会员卡汇总信息"""
    service = MemberCardService(session, member_context.tenant_context.tenant_id)
    
    # 获取会员卡汇总信息
    summary = service.get_member_cards_summary(member_context.member.id)
    
    return success_response(summary, "获取会员卡汇总成功")
'''

# 5. 创建 app/api/v1/member/records.py
member_records_content = '''"""
会员端 - 记录查询API
"""
from fastapi import APIRouter, Depends, Query
from sqlmodel import Session
from typing import Optional
from datetime import date

from app.db.session import get_session
from app.core.dependencies import get_member_context, MemberContext
from app.api.common.responses import PageResponse, page_response

from app.features.member_cards.consumption_service import ConsumptionService
from app.features.member_cards.schemas import (
    MemberCardOperationRead, MemberCardOperationQuery
)

router = APIRouter()

@router.get("/operations", response_model=PageResponse[MemberCardOperationRead])
def get_my_card_operations(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    operation_type: Optional[str] = Query(None, description="操作类型"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的会员卡操作记录"""
    service = ConsumptionService(session, member_context.tenant_context.tenant_id)
    
    # 构建查询参数（只查询自己的记录）
    query = MemberCardOperationQuery(
        member_id=member_context.member.id,  # 限制只能查看自己的记录
        operation_type=operation_type,
        date_from=date_from,
        date_to=date_to,
        page=page,
        size=size
    )
    
    operations, total = service.get_operations(query)
    
    return page_response(operations, total, page, size, "获取操作记录成功")

@router.get("/recharge-history", response_model=PageResponse[MemberCardOperationRead])
def get_my_recharge_history(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的充值记录"""
    service = ConsumptionService(session, member_context.tenant_context.tenant_id)
    
    # 构建查询参数（只查询充值记录）
    query = MemberCardOperationQuery(
        member_id=member_context.member.id,
        operation_type="RECHARGE",  # 只查询充值记录
        date_from=date_from,
        date_to=date_to,
        page=page,
        size=size
    )
    
    operations, total = service.get_operations(query)
    
    return page_response(operations, total, page, size, "获取充值记录成功")

@router.get("/consumption-history", response_model=PageResponse[MemberCardOperationRead])
def get_my_consumption_history(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的消费记录"""
    service = ConsumptionService(session, member_context.tenant_context.tenant_id)
    
    # 构建查询参数（只查询消费记录）
    query = MemberCardOperationQuery(
        member_id=member_context.member.id,
        operation_type="DIRECT_BOOKING,ADMIN_BOOKING",  # 查询消费相关记录
        date_from=date_from,
        date_to=date_to,
        page=page,
        size=size
    )
    
    operations, total = service.get_operations(query)
    
    return page_response(operations, total, page, size, "获取消费记录成功")
'''

# 文件创建指令
files_to_create = {
    "app/api/v1/member/__init__.py": member_init_content,
    "app/api/v1/member/profile.py": member_profile_content,
    "app/api/v1/member/courses.py": member_courses_content,
    "app/api/v1/member/cards.py": member_cards_content,
    "app/api/v1/member/records.py": member_records_content,
}
