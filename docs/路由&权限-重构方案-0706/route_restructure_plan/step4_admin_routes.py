# Step 4: 创建管理端路由文件

# 1. 创建 app/api/v1/admin/__init__.py
admin_init_content = '''"""
管理端API模块
"""
'''

# 2. 创建 app/api/v1/admin/tenants.py
admin_tenants_content = '''"""
管理端 - 租户管理API
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session

from app.features.tenants.router import router as original_router
from app.db.session import get_session
from app.core.dependencies import get_user_context

# 直接使用原有的路由，后续可以逐步重构
router = original_router
'''

# 3. 创建 app/api/v1/admin/users.py  
admin_users_content = '''"""
管理端 - 用户管理API
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session

from app.features.users.router import router as original_router
from app.db.session import get_session
from app.core.dependencies import get_user_context

# 直接使用原有的路由，后续可以逐步重构
router = original_router
'''

# 4. 创建 app/api/v1/admin/members.py
admin_members_content = '''"""
管理端 - 会员管理API
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session

from app.features.members.router import router as members_router
from app.features.members.fixed_lock_router import router as fixed_locks_router
from app.db.session import get_session
from app.core.dependencies import get_user_context

# 创建主路由
router = APIRouter()

# 包含会员基础管理路由
router.include_router(members_router, prefix="", tags=["管理端-会员管理"])

# 包含会员固定课位锁定路由  
router.include_router(fixed_locks_router, prefix="/fixed-locks", tags=["管理端-会员固定课位"])
'''

# 5. 创建 app/api/v1/admin/teachers.py
admin_teachers_content = '''"""
管理端 - 教师管理API
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session

from app.features.teachers.router import router as original_router
from app.db.session import get_session
from app.core.dependencies import get_user_context

# 直接使用原有的路由，后续可以逐步重构
router = original_router
'''

# 6. 创建 app/api/v1/admin/tags.py
admin_tags_content = '''"""
管理端 - 标签管理API
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session

from app.features.tags.router import router as original_router
from app.db.session import get_session
from app.core.dependencies import get_user_context

# 直接使用原有的路由，后续可以逐步重构
router = original_router
'''

# 7. 创建 app/api/v1/admin/courses.py
admin_courses_content = '''"""
管理端 - 课程管理API
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session

from app.features.courses.config_router import router as config_router
from app.features.courses.scheduled_classes_router import router as classes_router
from app.db.session import get_session
from app.core.dependencies import get_user_context

# 创建主路由
router = APIRouter()

# 包含课程配置路由
router.include_router(config_router, prefix="/config", tags=["管理端-课程配置"])

# 包含已排课程路由
router.include_router(classes_router, prefix="/classes", tags=["管理端-课程管理"])
'''

# 8. 创建 app/api/v1/admin/member_cards.py（任务7.5的新文件）
admin_member_cards_content = '''"""
管理端 - 会员卡管理API
"""
from fastapi import APIRouter, Depends, Query, status
from sqlmodel import Session
from typing import List, Optional

from app.db.session import get_session
from app.core.dependencies import get_user_context, UserContext
from app.api.common.responses import DataResponse, PageResponse, success_response, page_response

# 会员卡相关导入
from app.features.member_cards.template_service import MemberCardTemplateService
from app.features.member_cards.card_service import MemberCardService
from app.features.member_cards.recharge_service import RechargeService
from app.features.member_cards.consumption_service import ConsumptionService
from app.features.member_cards.schemas import (
    # 模板相关
    MemberCardTemplateCreate, MemberCardTemplateUpdate, MemberCardTemplateRead, MemberCardTemplateList, MemberCardTemplateQuery,
    # 卡片相关
    MemberCardCreate, MemberCardUpdate, MemberCardRead, MemberCardList, MemberCardQuery,
    # 充值相关
    RechargeRequest, RechargeResponse,
    # 操作记录相关
    MemberCardOperationRead, MemberCardOperationQuery
)

router = APIRouter()

# ==================== 会员卡模板管理 ====================

@router.get("/templates", response_model=PageResponse[MemberCardTemplateList])
def get_card_templates(
    query: MemberCardTemplateQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡模板列表"""
    service = MemberCardTemplateService(session, user_context.tenant_context.tenant_id)
    templates, total = service.get_templates(query)
    return page_response(templates, total, query.page, query.size, "获取模板列表成功")

@router.post("/templates", response_model=DataResponse[MemberCardTemplateRead], status_code=status.HTTP_201_CREATED)
def create_card_template(
    template_data: MemberCardTemplateCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建会员卡模板"""
    service = MemberCardTemplateService(session, user_context.tenant_context.tenant_id)
    template = service.create_template(template_data, created_by=user_context.user.id)
    return success_response(template, "创建模板成功")

@router.get("/templates/{template_id}", response_model=DataResponse[MemberCardTemplateRead])
def get_card_template(
    template_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡模板详情"""
    service = MemberCardTemplateService(session, user_context.tenant_context.tenant_id)
    template = service.get_template(template_id)
    return success_response(template, "获取模板详情成功")

@router.put("/templates/{template_id}", response_model=DataResponse[MemberCardTemplateRead])
def update_card_template(
    template_id: int,
    template_data: MemberCardTemplateUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新会员卡模板"""
    service = MemberCardTemplateService(session, user_context.tenant_context.tenant_id)
    template = service.update_template(template_id, template_data, updated_by=user_context.user.id)
    return success_response(template, "更新模板成功")

# ==================== 会员卡管理 ====================

@router.get("/cards", response_model=PageResponse[MemberCardList])
def get_member_cards(
    query: MemberCardQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡列表"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    cards, total = service.get_cards(query)
    return page_response(cards, total, query.page, query.size, "获取会员卡列表成功")

@router.post("/cards", response_model=DataResponse[MemberCardRead], status_code=status.HTTP_201_CREATED)
def create_member_card(
    card_data: MemberCardCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建会员卡"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    card = service.create_card(card_data, created_by=user_context.user.id)
    return success_response(card, "创建会员卡成功")

@router.get("/cards/{card_id}", response_model=DataResponse[MemberCardRead])
def get_member_card(
    card_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡详情"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    card = service.get_card(card_id)
    return success_response(card, "获取会员卡详情成功")

# ==================== 充值管理 ====================

@router.post("/cards/{card_id}/recharge", response_model=DataResponse[RechargeResponse])
def recharge_member_card(
    card_id: int,
    recharge_data: RechargeRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """会员卡充值"""
    service = RechargeService(session, user_context.tenant_context.tenant_id)
    result = service.recharge(card_id, recharge_data, operator_id=user_context.user.id)
    return success_response(result, "充值成功")

# ==================== 操作记录查询 ====================

@router.get("/operations", response_model=PageResponse[MemberCardOperationRead])
def get_card_operations(
    query: MemberCardOperationQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡操作记录"""
    service = ConsumptionService(session, user_context.tenant_context.tenant_id)
    operations, total = service.get_operations(query)
    return page_response(operations, total, query.page, query.size, "获取操作记录成功")
'''

# 文件创建指令
files_to_create = {
    "app/api/v1/admin/__init__.py": admin_init_content,
    "app/api/v1/admin/tenants.py": admin_tenants_content,
    "app/api/v1/admin/users.py": admin_users_content,
    "app/api/v1/admin/members.py": admin_members_content,
    "app/api/v1/admin/teachers.py": admin_teachers_content,
    "app/api/v1/admin/tags.py": admin_tags_content,
    "app/api/v1/admin/courses.py": admin_courses_content,
    "app/api/v1/admin/member_cards.py": admin_member_cards_content,
}
