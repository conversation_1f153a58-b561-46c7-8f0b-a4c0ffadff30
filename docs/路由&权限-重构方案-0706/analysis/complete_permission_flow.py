"""
完整的权限控制流程示例 - 从登录到操作执行的全链路
"""

# ==================== 1. 用户登录获取权限 ====================

@router.post("/auth/login")
async def login(credentials: LoginRequest):
    """用户登录"""
    # 1. 验证用户名密码
    user = authenticate_user(credentials.username, credentials.password)
    if not user:
        raise HTTPException(401, "用户名或密码错误")
    
    # 2. 获取用户权限信息
    permission_service = PermissionService(session, user.tenant_id)
    user_permissions = permission_service.get_user_permissions(user.id)
    
    # 3. 生成JWT Token（包含权限信息）
    token_payload = {
        "user_id": user.id,
        "tenant_id": user.tenant_id,
        "role": user.role,
        "permissions": user_permissions.permissions,
        "exp": datetime.utcnow() + timedelta(hours=24)
    }
    token = create_jwt_token(token_payload)
    
    # 4. 返回登录信息和权限数据
    return {
        "token": token,
        "user_info": {
            "id": user.id,
            "username": user.username,
            "role": user.role,
            "tenant_id": user.tenant_id
        },
        "permissions": user_permissions.dict()
    }

# ==================== 2. 前端权限初始化 ====================

class FrontendPermissionInit:
    """前端权限初始化"""
    
    @staticmethod
    def init_permissions_on_login(login_response):
        """登录后初始化权限"""
        # JavaScript/TypeScript 代码示例
        js_code = """
        // 1. 存储权限信息到本地
        localStorage.setItem('user_permissions', JSON.stringify(login_response.permissions));
        localStorage.setItem('token', login_response.token);
        
        // 2. 初始化权限检查器
        const permissionChecker = new PermissionChecker(login_response.permissions);
        
        // 3. 动态生成路由
        const allowedRoutes = generateRoutesBasedOnPermissions(login_response.permissions);
        router.addRoutes(allowedRoutes);
        
        // 4. 初始化菜单
        const allowedMenus = generateMenusBasedOnPermissions(login_response.permissions);
        store.commit('SET_MENUS', allowedMenus);
        """
        return js_code

# ==================== 3. 前端路由权限控制 ====================

class FrontendRouteGuard:
    """前端路由守卫"""
    
    @staticmethod
    def route_guard_example():
        """路由守卫示例"""
        js_code = """
        // Vue Router 路由守卫
        router.beforeEach((to, from, next) => {
            const token = localStorage.getItem('token');
            const permissions = JSON.parse(localStorage.getItem('user_permissions') || '{}');
            
            // 1. 检查是否已登录
            if (!token) {
                next('/login');
                return;
            }
            
            // 2. 检查路由权限
            const requiredPermission = to.meta.permission;
            if (requiredPermission && !permissions.permissions.includes(requiredPermission)) {
                next('/403'); // 无权限页面
                return;
            }
            
            // 3. 检查角色权限
            const requiredRoles = to.meta.roles;
            if (requiredRoles && !requiredRoles.includes(permissions.role)) {
                next('/403');
                return;
            }
            
            next();
        });
        """
        return js_code

# ==================== 4. 前端组件权限控制 ====================

class FrontendComponentPermission:
    """前端组件权限控制"""
    
    @staticmethod
    def permission_directive():
        """权限指令示例"""
        js_code = """
        // Vue 权限指令
        Vue.directive('permission', {
            inserted(el, binding) {
                const permissions = JSON.parse(localStorage.getItem('user_permissions') || '{}');
                const requiredPermission = binding.value;
                
                if (!permissions.permissions.includes(requiredPermission)) {
                    el.style.display = 'none';
                    // 或者 el.remove();
                }
            }
        });
        
        // 使用示例
        // <button v-permission="'course:create'">创建课程</button>
        // <button v-permission="'course:delete'">删除课程</button>
        """
        return js_code
    
    @staticmethod
    def permission_component():
        """权限组件示例"""
        vue_code = """
        <!-- Permission 组件 -->
        <template>
            <div v-if="hasPermission">
                <slot></slot>
            </div>
        </template>
        
        <script>
        export default {
            name: 'Permission',
            props: {
                permission: {
                    type: [String, Array],
                    required: true
                },
                mode: {
                    type: String,
                    default: 'any' // 'any' 或 'all'
                }
            },
            computed: {
                hasPermission() {
                    const userPermissions = this.$store.getters.permissions;
                    const required = Array.isArray(this.permission) ? this.permission : [this.permission];
                    
                    if (this.mode === 'all') {
                        return required.every(p => userPermissions.includes(p));
                    } else {
                        return required.some(p => userPermissions.includes(p));
                    }
                }
            }
        }
        </script>
        
        <!-- 使用示例 -->
        <Permission permission="course:create">
            <button>创建课程</button>
        </Permission>
        
        <Permission :permission="['course:edit', 'course:delete']" mode="any">
            <button>编辑或删除</button>
        </Permission>
        """
        return vue_code

# ==================== 5. 后端API权限验证 ====================

@router.get("/admin/courses")
async def get_courses(auth = Depends(require_permission("course:view"))):
    """获取课程列表 - 需要课程查看权限"""
    # 权限已在装饰器中验证，这里直接执行业务逻辑
    service = CourseService(session, auth.tenant_id)
    
    # 根据用户权限返回不同数据
    if auth.has_permission("course:view:all"):
        # 有查看所有课程权限
        courses = service.get_all_courses()
    else:
        # 只能查看自己相关的课程
        courses = service.get_courses_by_user(auth.user_id)
    
    return success_response(courses, "获取课程列表成功")

@router.post("/admin/courses")
async def create_course(
    course_data: CourseCreateRequest,
    auth = Depends(require_permission("course:create"))
):
    """创建课程 - 需要课程创建权限"""
    service = CourseService(session, auth.tenant_id)
    course = service.create_course(course_data, created_by=auth.user_id)
    return success_response(course, "创建课程成功")

@router.delete("/admin/courses/{course_id}")
async def delete_course(
    course_id: int,
    auth = Depends(require_permission("course:delete"))
):
    """删除课程 - 需要课程删除权限"""
    service = CourseService(session, auth.tenant_id)
    
    # 额外的权限检查：只能删除自己创建的课程（除非有管理员权限）
    if not auth.has_permission("course:delete:all"):
        course = service.get_course(course_id)
        if course.created_by != auth.user_id:
            raise PermissionDeniedError("只能删除自己创建的课程")
    
    service.delete_course(course_id)
    return success_response(None, "删除课程成功")

# ==================== 6. 权限一致性保证机制 ====================

class PermissionConsistencyGuard:
    """权限一致性保证"""
    
    @staticmethod
    def frontend_permission_sync():
        """前端权限同步机制"""
        js_code = """
        // 定期同步权限（防止权限变更后前端未更新）
        setInterval(async () => {
            try {
                const response = await api.get('/permissions/my');
                const newPermissions = response.data;
                
                // 比较权限是否有变化
                const currentPermissions = JSON.parse(localStorage.getItem('user_permissions') || '{}');
                if (JSON.stringify(currentPermissions) !== JSON.stringify(newPermissions)) {
                    // 权限有变化，更新本地存储并刷新页面
                    localStorage.setItem('user_permissions', JSON.stringify(newPermissions));
                    location.reload();
                }
            } catch (error) {
                console.error('权限同步失败:', error);
            }
        }, 5 * 60 * 1000); // 每5分钟同步一次
        """
        return js_code
    
    @staticmethod
    def backend_permission_cache():
        """后端权限缓存机制"""
        python_code = """
        # 权限缓存（Redis）
        class PermissionCache:
            def __init__(self, redis_client):
                self.redis = redis_client
                self.cache_ttl = 300  # 5分钟
            
            def get_user_permissions(self, user_id: int):
                cache_key = f"user_permissions:{user_id}"
                cached = self.redis.get(cache_key)
                
                if cached:
                    return json.loads(cached)
                
                # 从数据库获取权限
                permissions = self._fetch_from_db(user_id)
                
                # 缓存权限
                self.redis.setex(cache_key, self.cache_ttl, json.dumps(permissions))
                
                return permissions
            
            def invalidate_user_permissions(self, user_id: int):
                '''权限变更时清除缓存'''
                cache_key = f"user_permissions:{user_id}"
                self.redis.delete(cache_key)
        """
        return python_code

# ==================== 7. 完整流程示例 ====================

class CompletePermissionFlow:
    """完整权限控制流程"""
    
    def __init__(self):
        self.flow_steps = [
            "1. 用户登录，后端验证并返回权限信息",
            "2. 前端存储权限信息，初始化路由和菜单",
            "3. 用户访问页面，路由守卫检查权限",
            "4. 页面渲染时，组件根据权限显示/隐藏",
            "5. 用户点击操作按钮，前端再次检查权限",
            "6. 发送API请求，后端验证权限",
            "7. 执行业务逻辑，返回结果",
            "8. 定期同步权限，保持一致性"
        ]
    
    def get_security_considerations(self):
        """安全考虑"""
        return {
            "前端权限控制": "仅用于用户体验，不能作为安全依赖",
            "后端权限验证": "真正的安全屏障，必须严格验证",
            "权限同步": "防止权限变更后的不一致问题",
            "权限缓存": "提高性能，但要注意缓存失效",
            "审计日志": "记录所有权限相关操作，便于审计"
        }
