"""
项目结构分析 - 文件性质和路径对应关系
"""

# ==================== 当前结构 vs 新结构对比 ====================

# 当前结构（按业务功能分组）
"""
app/features/courses/
├── scheduled_classes_router.py    # 包含所有角色的课程路由
├── scheduled_classes_service.py   # 业务逻辑
├── scheduled_classes_models.py    # 数据模型
└── scheduled_classes_schemas.py   # API模型

路由注册：
/api/v1/courses/classes/search      # 管理员搜索课程
/api/v1/courses/classes/{id}/book   # 管理员预约课程
/api/v1/member-app/courses/available # 会员查看可预约课程
"""

# 新结构（按角色分组）
"""
app/api/v1/admin/
├── courses.py          # 管理员的课程相关路由
├── members.py          # 管理员的会员管理路由
└── analytics.py        # 管理员的数据分析路由

app/api/v1/member/
├── courses.py          # 会员的课程相关路由
├── profile.py          # 会员的个人信息路由
└── cards.py           # 会员的会员卡路由

app/features/courses/   # 业务逻辑层保持不变
├── service.py          # 统一的业务逻辑
├── models.py           # 数据模型
└── schemas.py          # 数据传输对象

路由注册：
/api/v1/admin/courses              # 管理员课程管理
/api/v1/admin/members              # 管理员会员管理
/api/v1/member/courses/available   # 会员查看课程
/api/v1/member/profile             # 会员个人信息
"""

# ==================== 文件性质说明 ====================

# 1. API层文件（纯路由文件）
# app/api/v1/admin/courses.py
from fastapi import APIRouter, Depends
from app.features.courses.service import CourseService  # 引用业务逻辑

admin_courses_router = APIRouter()

@admin_courses_router.get("/")
def get_courses_for_admin():
    """管理员获取课程列表 - 只负责HTTP请求处理"""
    # 调用业务逻辑层
    service = CourseService()
    return service.get_courses_for_admin()

# 2. 业务逻辑层文件（保持不变）
# app/features/courses/service.py
class CourseService:
    """课程业务逻辑 - 所有角色共享"""
    
    def get_courses_for_admin(self):
        """管理员获取课程的业务逻辑"""
        pass
    
    def get_courses_for_member(self):
        """会员获取课程的业务逻辑"""
        pass

# ==================== 路径模块对应关系 ====================

# 每个路由文件对应一个子路径模块
"""
文件路径                    -> URL路径前缀
app/api/v1/admin/courses.py -> /api/v1/admin/courses/*
app/api/v1/admin/members.py -> /api/v1/admin/members/*
app/api/v1/member/courses.py -> /api/v1/member/courses/*
app/api/v1/member/profile.py -> /api/v1/member/profile/*
"""

# 主路由注册文件
# app/api/v1/api.py
from app.api.v1.admin.courses import admin_courses_router
from app.api.v1.admin.members import admin_members_router
from app.api.v1.member.courses import member_courses_router
from app.api.v1.member.profile import member_profile_router

api_router = APIRouter()
api_router.include_router(admin_courses_router, prefix="/admin/courses", tags=["管理端-课程"])
api_router.include_router(admin_members_router, prefix="/admin/members", tags=["管理端-会员"])
api_router.include_router(member_courses_router, prefix="/member/courses", tags=["会员端-课程"])
api_router.include_router(member_profile_router, prefix="/member/profile", tags=["会员端-个人"])
