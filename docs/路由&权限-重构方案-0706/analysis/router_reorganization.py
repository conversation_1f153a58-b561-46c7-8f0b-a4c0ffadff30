"""
路由重组方案 - 从功能分组到角色分组的迁移
"""

# ==================== 当前路由分析 ====================

# 当前 app/features/courses/scheduled_classes_router.py 包含的路由：
current_routes = {
    "管理员路由": [
        "GET /courses/classes/search",           # 搜索课程
        "POST /courses/classes/{id}/book",       # 预约课程
        "POST /courses/classes/cancel/{id}",     # 取消预约
        "POST /courses/classes/batch/book",      # 批量预约
        "POST /courses/classes/teacher/create",  # 教师创建课程
    ],
    "会员路由": [
        "GET /member-app/courses/available",     # 获取可预约课程
        "GET /member-app/courses/my",            # 获取我的课程
        "POST /member-app/courses/{id}/book",    # 会员预约课程
    ],
    "公共路由": [
        "GET /courses/classes/available",        # 获取可预约课程
        "GET /courses/classes/teacher/{id}",     # 获取教师课程
        "GET /courses/classes/member/{id}",      # 获取会员课程
    ]
}

# ==================== 重组后的结构 ====================

# 1. app/api/v1/admin/courses.py - 管理员课程路由
from fastapi import APIRouter, Depends, Query
from app.features.courses.scheduled_classes_service import ScheduledClassService
from app.api.common.auth import get_admin_context

router = APIRouter()

@router.get("/")
def search_courses(
    # 从原来的 /courses/classes/search 迁移而来
    page: int = Query(1, ge=1),
    auth = Depends(get_admin_context)
):
    """管理员搜索课程"""
    pass

@router.post("/{class_id}/assign")
def assign_course(
    # 从原来的 /courses/classes/{id}/book 重命名而来
    class_id: int,
    member_id: int,
    auth = Depends(get_admin_context)
):
    """管理员分配课程给会员"""
    pass

@router.post("/batch/assign")
def batch_assign_courses(
    # 从原来的 /courses/classes/batch/book 重命名而来
    assignments: list,
    auth = Depends(get_admin_context)
):
    """批量分配课程"""
    pass

# 2. app/api/v1/member/courses.py - 会员课程路由
from app.api.common.auth import get_member_context

router = APIRouter()

@router.get("/available")
def get_available_courses(
    # 从原来的 /member-app/courses/available 迁移而来
    auth = Depends(get_member_context)
):
    """获取可预约课程"""
    pass

@router.get("/my")
def get_my_courses(
    # 从原来的 /member-app/courses/my 迁移而来
    auth = Depends(get_member_context)
):
    """获取我的课程"""
    pass

@router.post("/{class_id}/book")
def book_course(
    # 从原来的 /member-app/courses/{id}/book 迁移而来
    class_id: int,
    auth = Depends(get_member_context)
):
    """预约课程"""
    pass

# 3. app/api/v1/teacher/courses.py - 教师课程路由（新增）
from app.api.common.auth import get_teacher_context

router = APIRouter()

@router.post("/")
def create_course(
    # 从原来的 /courses/classes/teacher/create 迁移而来
    auth = Depends(get_teacher_context)
):
    """教师创建课程"""
    pass

@router.get("/my")
def get_my_courses(
    # 从原来的 /courses/classes/teacher/{id} 演化而来
    auth = Depends(get_teacher_context)
):
    """获取我的课程"""
    pass

# 4. app/api/v1/public/courses.py - 公开课程路由
router = APIRouter()

@router.get("/available")
def get_public_available_courses():
    """获取公开的可预约课程（无需认证）"""
    pass

# ==================== 迁移策略 ====================

class RouterMigrationStrategy:
    """路由迁移策略"""
    
    @staticmethod
    def extract_admin_routes():
        """提取管理员路由"""
        # 从 scheduled_classes_router.py 中提取需要管理员权限的路由
        # 重新组织到 admin/courses.py
        pass
    
    @staticmethod
    def extract_member_routes():
        """提取会员路由"""
        # 从 member_app.py 和相关文件中提取会员路由
        # 重新组织到 member/courses.py
        pass
    
    @staticmethod
    def create_teacher_routes():
        """创建教师路由"""
        # 为教师角色创建专门的路由
        pass
    
    @staticmethod
    def maintain_compatibility():
        """保持向后兼容"""
        # 在迁移期间保持旧路由可用
        # 通过路由别名或重定向实现
        pass

# ==================== 路由注册重组 ====================

# 新的主路由文件 app/api/v1/api.py
from fastapi import APIRouter

# 导入各角色的路由模块
from .admin import courses as admin_courses
from .admin import members as admin_members
from .member import courses as member_courses
from .member import profile as member_profile
from .teacher import courses as teacher_courses
from .public import courses as public_courses

def create_api_router() -> APIRouter:
    """创建API路由"""
    api_router = APIRouter()
    
    # 管理员路由组
    api_router.include_router(
        admin_courses.router, 
        prefix="/admin/courses", 
        tags=["管理端-课程管理"]
    )
    api_router.include_router(
        admin_members.router, 
        prefix="/admin/members", 
        tags=["管理端-会员管理"]
    )
    
    # 会员路由组
    api_router.include_router(
        member_courses.router, 
        prefix="/member/courses", 
        tags=["会员端-课程预约"]
    )
    api_router.include_router(
        member_profile.router, 
        prefix="/member/profile", 
        tags=["会员端-个人中心"]
    )
    
    # 教师路由组
    api_router.include_router(
        teacher_courses.router, 
        prefix="/teacher/courses", 
        tags=["教师端-课程管理"]
    )
    
    # 公开路由组
    api_router.include_router(
        public_courses.router, 
        prefix="/public/courses", 
        tags=["公开接口-课程信息"]
    )
    
    return api_router
