"""
前后端RBAC权限模型协作设计
"""

# ==================== 后端权限系统设计 ====================

from enum import Enum
from typing import List, Dict, Optional
from pydantic import BaseModel
from sqlmodel import SQLModel, Field, Relationship

class UserRole(str, Enum):
    """用户角色"""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin" 
    TEACHER = "teacher"
    MEMBER = "member"

class Permission(str, Enum):
    """权限枚举"""
    # 课程权限
    COURSE_VIEW = "course:view"
    COURSE_CREATE = "course:create"
    COURSE_EDIT = "course:edit"
    COURSE_DELETE = "course:delete"
    COURSE_ASSIGN = "course:assign"
    
    # 会员权限
    MEMBER_VIEW = "member:view"
    MEMBER_CREATE = "member:create"
    MEMBER_EDIT = "member:edit"
    MEMBER_DELETE = "member:delete"
    
    # 数据分析权限
    ANALYTICS_VIEW = "analytics:view"
    ANALYTICS_EXPORT = "analytics:export"

# 数据库模型
class Role(SQLModel, table=True):
    """角色表"""
    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(max_length=50, unique=True)
    display_name: str = Field(max_length=100)
    description: Optional[str] = Field(default=None)
    is_active: bool = Field(default=True)
    
    # 关联权限
    permissions: List["RolePermission"] = Relationship(back_populates="role")

class PermissionModel(SQLModel, table=True):
    """权限表"""
    __tablename__ = "permissions"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    code: str = Field(max_length=100, unique=True)  # 如 "course:view"
    name: str = Field(max_length=100)
    module: str = Field(max_length=50)  # 如 "course", "member"
    description: Optional[str] = Field(default=None)

class RolePermission(SQLModel, table=True):
    """角色权限关联表"""
    __tablename__ = "role_permissions"
    
    role_id: int = Field(foreign_key="role.id", primary_key=True)
    permission_id: int = Field(foreign_key="permissions.id", primary_key=True)
    
    role: Role = Relationship(back_populates="permissions")
    permission: PermissionModel = Relationship()

class UserPermissionInfo(BaseModel):
    """用户权限信息响应模型"""
    user_id: int
    role: str
    role_name: str
    permissions: List[str]
    menu_permissions: Dict[str, bool]
    button_permissions: Dict[str, bool]

# ==================== 权限服务 ====================

class PermissionService:
    """权限服务"""
    
    def __init__(self, session, tenant_id: int):
        self.session = session
        self.tenant_id = tenant_id
    
    def get_user_permissions(self, user_id: int) -> UserPermissionInfo:
        """获取用户权限信息"""
        # 查询用户角色和权限
        user = self.get_user_with_role(user_id)
        permissions = self.get_role_permissions(user.role)
        
        return UserPermissionInfo(
            user_id=user_id,
            role=user.role,
            role_name=user.role_display_name,
            permissions=permissions,
            menu_permissions=self._build_menu_permissions(permissions),
            button_permissions=self._build_button_permissions(permissions)
        )
    
    def _build_menu_permissions(self, permissions: List[str]) -> Dict[str, bool]:
        """构建菜单权限映射"""
        menu_mapping = {
            "course_management": "course:view" in permissions,
            "member_management": "member:view" in permissions,
            "teacher_management": "teacher:view" in permissions,
            "analytics": "analytics:view" in permissions,
        }
        return menu_mapping
    
    def _build_button_permissions(self, permissions: List[str]) -> Dict[str, bool]:
        """构建按钮权限映射"""
        button_mapping = {
            "course_create_btn": "course:create" in permissions,
            "course_edit_btn": "course:edit" in permissions,
            "course_delete_btn": "course:delete" in permissions,
            "member_create_btn": "member:create" in permissions,
            "member_edit_btn": "member:edit" in permissions,
            "analytics_export_btn": "analytics:export" in permissions,
        }
        return button_mapping

# ==================== 权限相关API接口 ====================

from fastapi import APIRouter, Depends
from app.api.common.responses import DataResponse, success_response

permission_router = APIRouter(prefix="/permissions", tags=["权限管理"])

@permission_router.get("/my", response_model=DataResponse[UserPermissionInfo])
def get_my_permissions(auth = Depends(get_auth_context)):
    """获取当前用户权限信息"""
    service = PermissionService(session, auth.tenant_id)
    permissions = service.get_user_permissions(auth.user_id)
    return success_response(permissions, "获取权限信息成功")

@permission_router.get("/roles", response_model=DataResponse[List[Dict]])
def get_roles(auth = Depends(require_permission("system:role:view"))):
    """获取角色列表（需要系统管理权限）"""
    service = PermissionService(session, auth.tenant_id)
    roles = service.get_all_roles()
    return success_response(roles, "获取角色列表成功")

@permission_router.post("/roles/{role_id}/permissions")
def update_role_permissions(
    role_id: int,
    permission_ids: List[int],
    auth = Depends(require_permission("system:role:edit"))
):
    """更新角色权限（需要系统管理权限）"""
    service = PermissionService(session, auth.tenant_id)
    service.update_role_permissions(role_id, permission_ids)
    return success_response(None, "更新角色权限成功")

# ==================== 前端权限控制支持 ====================

class FrontendPermissionSupport:
    """前端权限控制支持"""
    
    @staticmethod
    def generate_route_config():
        """生成前端路由配置"""
        return {
            "routes": [
                {
                    "path": "/course",
                    "name": "CourseManagement", 
                    "component": "CourseManagement",
                    "meta": {
                        "title": "课程管理",
                        "permission": "course:view",  # 需要的权限
                        "roles": ["admin", "super_admin"]  # 允许的角色
                    }
                },
                {
                    "path": "/member",
                    "name": "MemberManagement",
                    "component": "MemberManagement", 
                    "meta": {
                        "title": "会员管理",
                        "permission": "member:view",
                        "roles": ["admin", "super_admin"]
                    }
                }
            ]
        }
    
    @staticmethod
    def generate_menu_config():
        """生成前端菜单配置"""
        return {
            "menus": [
                {
                    "id": "course",
                    "title": "课程管理",
                    "icon": "course-icon",
                    "permission": "course:view",
                    "children": [
                        {
                            "id": "course-list",
                            "title": "课程列表", 
                            "path": "/course/list",
                            "permission": "course:view"
                        },
                        {
                            "id": "course-create",
                            "title": "创建课程",
                            "path": "/course/create", 
                            "permission": "course:create"
                        }
                    ]
                }
            ]
        }
    
    @staticmethod
    def generate_button_config():
        """生成前端按钮配置"""
        return {
            "buttons": {
                "course_page": [
                    {
                        "id": "create_btn",
                        "text": "创建课程",
                        "permission": "course:create",
                        "type": "primary"
                    },
                    {
                        "id": "edit_btn", 
                        "text": "编辑",
                        "permission": "course:edit",
                        "type": "default"
                    },
                    {
                        "id": "delete_btn",
                        "text": "删除", 
                        "permission": "course:delete",
                        "type": "danger"
                    }
                ]
            }
        }
