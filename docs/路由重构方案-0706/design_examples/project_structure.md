# 推荐的项目结构

```
app/
├── api/
│   ├── common/                    # 通用API组件
│   │   ├── exceptions.py
│   │   ├── responses.py
│   │   ├── pagination.py
│   │   └── dependencies.py       # 统一认证依赖
│   │
│   └── v1/
│       ├── __init__.py
│       ├── api.py                # 主路由注册
│       │
│       ├── admin/                # 管理端API
│       │   ├── __init__.py
│       │   ├── courses.py        # 课程管理
│       │   ├── members.py        # 会员管理
│       │   ├── teachers.py       # 教师管理
│       │   └── analytics.py      # 数据分析
│       │
│       ├── member/               # 会员端API
│       │   ├── __init__.py
│       │   ├── courses.py        # 课程预约
│       │   ├── profile.py        # 个人信息
│       │   └── cards.py          # 会员卡
│       │
│       ├── teacher/              # 教师端API（未来）
│       │   ├── __init__.py
│       │   ├── courses.py
│       │   └── schedule.py
│       │
│       └── public/               # 公开API
│           ├── __init__.py
│           ├── auth.py           # 认证相关
│           └── info.py           # 公开信息
│
├── features/                     # 业务功能模块（共享）
│   ├── courses/
│   │   ├── models.py
│   │   ├── service.py            # 统一服务层
│   │   ├── schemas.py
│   │   └── exceptions.py
│   │
│   ├── members/
│   └── teachers/
│
└── core/
    ├── auth/                     # 认证核心
    │   ├── models.py
    │   ├── service.py
    │   └── dependencies.py
    │
    └── permissions/              # 权限管理
        ├── models.py
        └── service.py
```

## 设计原则

### 1. 分离关注点
- **API层**：按角色分组，处理HTTP请求/响应
- **Service层**：业务逻辑，角色无关，可复用
- **Model层**：数据模型，全局共享

### 2. 服务层设计
```python
# 统一的服务层，通过参数区分调用者
class CourseService:
    def get_courses(self, 
                   user_id: int, 
                   user_role: UserRole,
                   filters: dict = None):
        """
        根据用户角色返回不同的课程数据
        - 管理员：看到所有课程
        - 会员：只看到可预约的课程
        - 教师：只看到自己的课程
        """
        if user_role == UserRole.ADMIN:
            return self._get_all_courses(filters)
        elif user_role == UserRole.MEMBER:
            return self._get_available_courses(user_id, filters)
        elif user_role == UserRole.TEACHER:
            return self._get_teacher_courses(user_id, filters)
```

### 3. Schema设计
```python
# 基础Schema
class CourseBase(BaseModel):
    title: str
    description: str

# 角色特定的响应Schema
class CourseAdminResponse(CourseBase):
    """管理员看到的课程信息"""
    id: int
    teacher_id: int
    member_count: int
    revenue: int
    created_at: datetime

class CourseMemberResponse(CourseBase):
    """会员看到的课程信息"""
    id: int
    teacher_name: str
    price: int
    available_slots: int
    can_book: bool
```
