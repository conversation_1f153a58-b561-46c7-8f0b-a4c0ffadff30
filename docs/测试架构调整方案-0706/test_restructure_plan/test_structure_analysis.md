# API集成测试用例调整方案

## 1. 测试目录结构分析

### 当前测试文件结构
```
tests/integration/api/v1/
├── __init__.py
├── test_auth_api.py                    # 认证API测试
├── test_course_config.py               # 课程配置API测试
├── test_member_api.py                  # 会员管理API测试
├── test_member_fixed_locks.py          # 会员固定课位API测试
├── test_scheduled_classes_api.py       # 已排课程API测试
├── test_tag_api.py                     # 标签管理API测试
├── test_teacher_api.py                 # 教师管理API测试
├── test_teacher_fixed_slot_api.py      # 教师固定时间段API测试
├── test_tenant_api.py                  # 租户管理API测试
└── test_user_api.py                    # 用户管理API测试
```

### 需要调整的API路径映射

#### 管理端API路径变更
| 原路径 | 新路径 | 测试文件 |
|--------|--------|----------|
| `/api/v1/tenants/*` | `/api/v1/admin/tenants/*` | test_tenant_api.py |
| `/api/v1/users/*` | `/api/v1/admin/users/*` | test_user_api.py |
| `/api/v1/members/*` | `/api/v1/admin/members/*` | test_member_api.py |
| `/api/v1/members/fixed-locks/*` | `/api/v1/admin/members/fixed-locks/*` | test_member_fixed_locks.py |
| `/api/v1/teachers/*` | `/api/v1/admin/teachers/*` | test_teacher_api.py |
| `/api/v1/teachers/fixed-slots/*` | `/api/v1/admin/teachers/fixed-slots/*` | test_teacher_fixed_slot_api.py |
| `/api/v1/tags/*` | `/api/v1/admin/tags/*` | test_tag_api.py |
| `/api/v1/courses/*` | `/api/v1/admin/courses/config/*` | test_course_config.py |
| `/api/v1/courses/classes/*` | `/api/v1/admin/courses/classes/*` | test_scheduled_classes_api.py |

#### 公共API路径变更
| 原路径 | 新路径 | 测试文件 |
|--------|--------|----------|
| `/api/v1/auth/*` | `/api/v1/auth/*` | test_auth_api.py (无变更) |

#### 新增会员端API (任务7.5)
| 新路径 | 功能 | 需要新建的测试文件 |
|--------|------|------------------|
| `/api/v1/member/profile/*` | 会员个人信息 | test_member_profile_api.py |
| `/api/v1/member/courses/*` | 会员课程预约 | test_member_courses_api.py |
| `/api/v1/member/cards/*` | 会员卡管理 | test_member_cards_api.py |
| `/api/v1/member/records/*` | 记录查询 | test_member_records_api.py |
| `/api/v1/admin/member-cards/*` | 管理端会员卡 | test_admin_member_cards_api.py |

## 2. 推荐的新测试目录结构

### 方案A：按角色分组（推荐）
```
tests/integration/api/v1/
├── __init__.py
├── admin/                              # 管理端API测试
│   ├── __init__.py
│   ├── test_tenants.py                 # 租户管理
│   ├── test_users.py                   # 用户管理
│   ├── test_members.py                 # 会员管理
│   ├── test_teachers.py                # 教师管理
│   ├── test_tags.py                    # 标签管理
│   ├── test_courses.py                 # 课程管理
│   └── test_member_cards.py            # 会员卡管理（新增）
├── member/                             # 会员端API测试
│   ├── __init__.py
│   ├── test_profile.py                 # 个人信息
│   ├── test_courses.py                 # 课程预约
│   ├── test_cards.py                   # 会员卡
│   └── test_records.py                 # 记录查询
├── public/                             # 公共API测试
│   ├── __init__.py
│   ├── test_auth.py                    # 认证
│   └── test_info.py                    # 公开信息
└── test_legacy.py                      # 遗留测试（临时保留）
```

### 方案B：保持扁平结构（备选）
```
tests/integration/api/v1/
├── __init__.py
├── test_admin_tenants.py               # 管理端-租户
├── test_admin_users.py                 # 管理端-用户
├── test_admin_members.py               # 管理端-会员
├── test_admin_teachers.py              # 管理端-教师
├── test_admin_tags.py                  # 管理端-标签
├── test_admin_courses.py               # 管理端-课程
├── test_admin_member_cards.py          # 管理端-会员卡
├── test_member_profile.py              # 会员端-个人信息
├── test_member_courses.py              # 会员端-课程
├── test_member_cards.py                # 会员端-会员卡
├── test_member_records.py              # 会员端-记录
├── test_auth.py                        # 认证
└── test_info.py                        # 公开信息
```

## 3. 文件命名规范

### 命名规则
- **管理端测试**：`test_admin_<module>.py` 或 `admin/test_<module>.py`
- **会员端测试**：`test_member_<module>.py` 或 `member/test_<module>.py`
- **公共API测试**：`test_<module>.py` 或 `public/test_<module>.py`

### 类命名规范
- **管理端测试类**：`TestAdmin<Module>API`
- **会员端测试类**：`TestMember<Module>API`
- **公共API测试类**：`Test<Module>API`

## 4. 需要更新的具体内容

### API路径更新统计
根据代码分析，需要更新的API调用数量：

| 测试文件 | 需要更新的API调用数量 | 主要路径变更 |
|----------|---------------------|-------------|
| test_scheduled_classes_api.py | 32个 | `/courses/classes/` → `/admin/courses/classes/` |
| test_member_api.py | 15个 | `/members/` → `/admin/members/` |
| test_teacher_api.py | 20个 | `/teachers/` → `/admin/teachers/` |
| test_tenant_api.py | 8个 | `/tenants/` → `/admin/tenants/` |
| test_user_api.py | 12个 | `/users/` → `/admin/users/` |
| test_tag_api.py | 18个 | `/tags/` → `/admin/tags/` |
| test_course_config.py | 6个 | `/courses/` → `/admin/courses/config/` |
| test_teacher_fixed_slot_api.py | 10个 | `/teachers/fixed-slots/` → `/admin/teachers/fixed-slots/` |
| test_member_fixed_locks.py | 8个 | `/members/fixed-locks/` → `/admin/members/fixed-locks/` |
| test_auth_api.py | 0个 | 无变更（已在 `/auth/` 下） |

**总计：约129个API调用需要更新路径**

## 5. 兼容性考虑

### 测试数据和Fixtures
- **保持不变**：所有现有的测试fixtures可以继续使用
- **导入路径**：测试文件的导入语句无需修改
- **测试逻辑**：业务逻辑测试保持不变，只需更新API路径

### 测试覆盖率
- **现有覆盖率**：重构后保持相同的测试覆盖率
- **新增覆盖**：为新的会员端API和会员卡API添加测试
- **回归测试**：确保所有现有功能的测试都能正常运行

### 依赖关系
- **测试顺序**：某些测试可能依赖于其他测试创建的数据
- **共享fixtures**：多个测试文件共享的fixtures需要保持兼容
- **数据库状态**：确保测试之间的数据库状态隔离
