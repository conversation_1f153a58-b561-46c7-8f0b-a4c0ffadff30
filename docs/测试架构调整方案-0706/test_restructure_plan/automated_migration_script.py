#!/usr/bin/env python3
"""
API集成测试自动迁移脚本
"""

import os
import shutil
import re
from pathlib import Path
from typing import Dict, List, Tuple


class TestMigrationScript:
    """测试迁移脚本"""
    
    def __init__(self, base_path: str = "tests/integration/api/v1"):
        self.base_path = Path(base_path)
        self.backup_path = self.base_path / "backup"
        self.admin_path = self.base_path / "admin"
        self.member_path = self.base_path / "member"
        self.public_path = self.base_path / "public"
        
        # API路径映射配置
        self.admin_api_mappings = {
            "/api/v1/tenants/": "/api/v1/admin/tenants/",
            "/api/v1/users/": "/api/v1/admin/users/",
            "/api/v1/members/": "/api/v1/admin/members/",
            "/api/v1/teachers/": "/api/v1/admin/teachers/",
            "/api/v1/tags/": "/api/v1/admin/tags/",
            "/api/v1/courses/classes/": "/api/v1/admin/courses/classes/",
            "/api/v1/courses/": "/api/v1/admin/courses/config/",
        }
        
        # 文件迁移映射
        self.file_mappings = [
            ("test_tenant_api.py", "admin/test_tenants.py", "TestTenantAPI", "TestAdminTenantAPI"),
            ("test_user_api.py", "admin/test_users.py", "TestUserAPI", "TestAdminUserAPI"),
            ("test_member_api.py", "admin/test_members.py", "TestMemberAPI", "TestAdminMemberAPI"),
            ("test_member_fixed_locks.py", "admin/test_member_fixed_locks.py", None, None),
            ("test_teacher_api.py", "admin/test_teachers.py", "TestTeacherAPI", "TestAdminTeacherAPI"),
            ("test_teacher_fixed_slot_api.py", "admin/test_teacher_fixed_slots.py", None, None),
            ("test_tag_api.py", "admin/test_tags.py", "TestTagCategoryAPI", "TestAdminTagCategoryAPI"),
            ("test_course_config.py", "admin/test_course_config.py", "TestCourseConfigAPI", "TestAdminCourseConfigAPI"),
            ("test_scheduled_classes_api.py", "admin/test_courses.py", "TestScheduledClassAPI", "TestAdminCourseAPI"),
            ("test_auth_api.py", "public/test_auth.py", None, None),
        ]
    
    def create_directories(self):
        """创建新的目录结构"""
        print("创建新的目录结构...")
        
        # 创建备份目录
        self.backup_path.mkdir(exist_ok=True)
        
        # 创建新的测试目录
        self.admin_path.mkdir(exist_ok=True)
        self.member_path.mkdir(exist_ok=True)
        self.public_path.mkdir(exist_ok=True)
        
        # 创建__init__.py文件
        for path in [self.admin_path, self.member_path, self.public_path]:
            init_file = path / "__init__.py"
            if not init_file.exists():
                init_file.write_text('"""API集成测试模块"""\n')
        
        print("✅ 目录结构创建完成")
    
    def backup_original_files(self):
        """备份原始测试文件"""
        print("备份原始测试文件...")
        
        test_files = list(self.base_path.glob("test_*.py"))
        for file_path in test_files:
            backup_file = self.backup_path / file_path.name
            shutil.copy2(file_path, backup_file)
            print(f"  备份: {file_path.name} -> backup/{file_path.name}")
        
        print(f"✅ 已备份 {len(test_files)} 个测试文件")
    
    def update_api_paths(self, content: str) -> str:
        """更新API路径"""
        updated_content = content
        
        for old_path, new_path in self.admin_api_mappings.items():
            # 使用正则表达式进行精确替换
            pattern = re.escape(old_path)
            updated_content = re.sub(pattern, new_path, updated_content)
        
        return updated_content
    
    def update_class_names(self, content: str, old_class: str, new_class: str) -> str:
        """更新类名"""
        if old_class and new_class:
            pattern = f"class {old_class}"
            replacement = f"class {new_class}"
            content = content.replace(pattern, replacement)
        
        return content
    
    def migrate_file(self, source_file: str, target_file: str, old_class: str = None, new_class: str = None):
        """迁移单个测试文件"""
        source_path = self.base_path / source_file
        target_path = self.base_path / target_file
        
        if not source_path.exists():
            print(f"⚠️  源文件不存在: {source_file}")
            return
        
        # 读取源文件内容
        content = source_path.read_text(encoding='utf-8')
        
        # 更新API路径
        content = self.update_api_paths(content)
        
        # 更新类名
        content = self.update_class_names(content, old_class, new_class)
        
        # 确保目标目录存在
        target_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入目标文件
        target_path.write_text(content, encoding='utf-8')
        
        print(f"  迁移: {source_file} -> {target_file}")
    
    def migrate_all_files(self):
        """迁移所有测试文件"""
        print("开始迁移测试文件...")
        
        for source_file, target_file, old_class, new_class in self.file_mappings:
            self.migrate_file(source_file, target_file, old_class, new_class)
        
        print("✅ 测试文件迁移完成")
    
    def create_new_test_files(self):
        """创建新的测试文件"""
        print("创建新的测试文件...")
        
        # 创建会员端个人信息测试
        member_profile_content = '''"""会员端个人信息API集成测试"""
import pytest
from fastapi.testclient import TestClient


class TestMemberProfileAPI:
    """会员端个人信息API测试"""
    
    def test_get_my_profile(self, client: TestClient, member_token):
        """测试获取我的个人信息"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get("/api/v1/member/profile/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "name" in data["data"]
    
    def test_update_my_profile(self, client: TestClient, member_token):
        """测试更新我的个人信息"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        update_data = {
            "name": "更新后的姓名",
            "address": "更新后的地址"
        }
        
        response = client.put(
            "/api/v1/member/profile/me",
            json=update_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["name"] == update_data["name"]
'''
        
        # 创建会员端课程测试
        member_courses_content = '''"""会员端课程API集成测试"""
import pytest
from fastapi.testclient import TestClient


class TestMemberCourseAPI:
    """会员端课程API测试"""
    
    def test_get_available_courses(self, client: TestClient, member_token):
        """测试获取可预约课程"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get(
            "/api/v1/member/courses/available?page=1&size=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "items" in data["data"]
    
    def test_get_my_courses(self, client: TestClient, member_token):
        """测试获取我的课程"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get(
            "/api/v1/member/courses/my?page=1&size=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
'''
        
        # 创建管理端会员卡测试
        admin_member_cards_content = '''"""管理端会员卡API集成测试"""
import pytest
from fastapi.testclient import TestClient


class TestAdminMemberCardAPI:
    """管理端会员卡API测试"""
    
    def test_get_card_templates(self, client: TestClient, admin_token):
        """测试获取会员卡模板列表"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get(
            "/api/v1/admin/member-cards/templates?page=1&size=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_create_card_template(self, client: TestClient, admin_token):
        """测试创建会员卡模板"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        template_data = {
            "name": "测试储值卡模板",
            "card_type": "VALUE_UNLIMITED",
            "description": "测试用储值卡模板",
            "is_active": True
        }
        
        response = client.post(
            "/api/v1/admin/member-cards/templates",
            json=template_data,
            headers=headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert data["data"]["name"] == template_data["name"]
'''
        
        # 写入新文件
        new_files = [
            (self.member_path / "test_profile.py", member_profile_content),
            (self.member_path / "test_courses.py", member_courses_content),
            (self.admin_path / "test_member_cards.py", admin_member_cards_content),
        ]
        
        for file_path, content in new_files:
            file_path.write_text(content, encoding='utf-8')
            print(f"  创建: {file_path.relative_to(self.base_path)}")
        
        print("✅ 新测试文件创建完成")
    
    def validate_migration(self):
        """验证迁移结果"""
        print("验证迁移结果...")
        
        # 检查新文件是否存在
        admin_files = list(self.admin_path.glob("test_*.py"))
        member_files = list(self.member_path.glob("test_*.py"))
        public_files = list(self.public_path.glob("test_*.py"))
        
        print(f"  管理端测试文件: {len(admin_files)} 个")
        print(f"  会员端测试文件: {len(member_files)} 个")
        print(f"  公共API测试文件: {len(public_files)} 个")
        
        # 检查API路径是否正确更新
        total_updates = 0
        for file_path in admin_files:
            content = file_path.read_text(encoding='utf-8')
            for new_path in self.admin_api_mappings.values():
                total_updates += content.count(new_path)
        
        print(f"  API路径更新数量: {total_updates} 处")
        print("✅ 迁移验证完成")
    
    def run_migration(self):
        """执行完整的迁移流程"""
        print("🚀 开始API集成测试迁移...")
        print("=" * 50)
        
        try:
            self.create_directories()
            self.backup_original_files()
            self.migrate_all_files()
            self.create_new_test_files()
            self.validate_migration()
            
            print("=" * 50)
            print("✅ 迁移完成！")
            print("\n下一步操作:")
            print("1. 运行测试验证: pytest tests/integration/api/v1/admin/ -v")
            print("2. 检查测试结果并修复任何问题")
            print("3. 删除原始测试文件: rm tests/integration/api/v1/test_*.py")
            
        except Exception as e:
            print(f"❌ 迁移失败: {e}")
            print("请检查错误并手动修复")


def main():
    """主函数"""
    script = TestMigrationScript()
    script.run_migration()


if __name__ == "__main__":
    main()
