# 测试用例调整验证指南

## 验证步骤概览

### 1. 迁移前验证
```bash
# 运行原有测试，记录基准结果
python -m pytest tests/integration/api/v1/ -v --tb=short > baseline_results.txt

# 统计测试数量
echo "原有测试统计:"
grep -c "PASSED\|FAILED\|ERROR" baseline_results.txt
```

### 2. 迁移后验证
```bash
# 运行新结构测试
python -m pytest tests/integration/api/v1/admin/ tests/integration/api/v1/member/ tests/integration/api/v1/public/ -v --tb=short > new_results.txt

# 统计新测试数量
echo "新结构测试统计:"
grep -c "PASSED\|FAILED\|ERROR" new_results.txt
```

### 3. 对比分析
```bash
# 对比测试结果
python test_restructure_plan/compare_test_results.py baseline_results.txt new_results.txt
```

## 详细验证清单

### ✅ 目录结构验证
- [ ] `tests/integration/api/v1/admin/` 目录存在
- [ ] `tests/integration/api/v1/member/` 目录存在  
- [ ] `tests/integration/api/v1/public/` 目录存在
- [ ] 各目录下有 `__init__.py` 文件
- [ ] 备份目录 `tests/integration/api/v1/backup/` 存在

### ✅ 文件迁移验证
- [ ] `test_tenant_api.py` → `admin/test_tenants.py`
- [ ] `test_user_api.py` → `admin/test_users.py`
- [ ] `test_member_api.py` → `admin/test_members.py`
- [ ] `test_teacher_api.py` → `admin/test_teachers.py`
- [ ] `test_tag_api.py` → `admin/test_tags.py`
- [ ] `test_course_config.py` → `admin/test_course_config.py`
- [ ] `test_scheduled_classes_api.py` → `admin/test_courses.py`
- [ ] `test_auth_api.py` → `public/test_auth.py`

### ✅ API路径更新验证
检查以下路径是否正确更新：

#### 管理端API路径
```bash
# 验证租户API路径
grep -r "/api/v1/admin/tenants/" tests/integration/api/v1/admin/test_tenants.py

# 验证用户API路径
grep -r "/api/v1/admin/users/" tests/integration/api/v1/admin/test_users.py

# 验证会员API路径
grep -r "/api/v1/admin/members/" tests/integration/api/v1/admin/test_members.py

# 验证教师API路径
grep -r "/api/v1/admin/teachers/" tests/integration/api/v1/admin/test_teachers.py

# 验证标签API路径
grep -r "/api/v1/admin/tags/" tests/integration/api/v1/admin/test_tags.py

# 验证课程API路径
grep -r "/api/v1/admin/courses/" tests/integration/api/v1/admin/test_courses.py
```

#### 会员端API路径
```bash
# 验证会员端课程API
grep -r "/api/v1/member/courses/" tests/integration/api/v1/member/test_courses.py

# 验证会员端个人信息API
grep -r "/api/v1/member/profile/" tests/integration/api/v1/member/test_profile.py
```

### ✅ 类名更新验证
```bash
# 检查管理端测试类名
grep -r "class TestAdmin" tests/integration/api/v1/admin/

# 检查会员端测试类名
grep -r "class TestMember" tests/integration/api/v1/member/

# 确保没有遗留的旧类名
grep -r "class TestTenantAPI\|class TestUserAPI\|class TestMemberAPI" tests/integration/api/v1/admin/
```

### ✅ 导入语句验证
```bash
# 检查是否有导入错误
python -c "
import sys
sys.path.append('.')
try:
    from tests.integration.api.v1.admin import test_tenants
    from tests.integration.api.v1.admin import test_users
    from tests.integration.api.v1.admin import test_members
    print('✅ 管理端测试导入正常')
except ImportError as e:
    print(f'❌ 管理端测试导入错误: {e}')

try:
    from tests.integration.api.v1.member import test_profile
    from tests.integration.api.v1.member import test_courses
    print('✅ 会员端测试导入正常')
except ImportError as e:
    print(f'❌ 会员端测试导入错误: {e}')
"
```

## 功能测试验证

### 1. 管理端API测试
```bash
# 分别测试各个管理端模块
echo "测试管理端租户API..."
python -m pytest tests/integration/api/v1/admin/test_tenants.py -v

echo "测试管理端用户API..."
python -m pytest tests/integration/api/v1/admin/test_users.py -v

echo "测试管理端会员API..."
python -m pytest tests/integration/api/v1/admin/test_members.py -v

echo "测试管理端教师API..."
python -m pytest tests/integration/api/v1/admin/test_teachers.py -v

echo "测试管理端课程API..."
python -m pytest tests/integration/api/v1/admin/test_courses.py -v
```

### 2. 会员端API测试
```bash
echo "测试会员端个人信息API..."
python -m pytest tests/integration/api/v1/member/test_profile.py -v

echo "测试会员端课程API..."
python -m pytest tests/integration/api/v1/member/test_courses.py -v
```

### 3. 公共API测试
```bash
echo "测试认证API..."
python -m pytest tests/integration/api/v1/public/test_auth.py -v
```

## 性能和覆盖率验证

### 1. 测试覆盖率对比
```bash
# 原有测试覆盖率
python -m pytest tests/integration/api/v1/backup/ --cov=app --cov-report=term-missing > old_coverage.txt

# 新结构测试覆盖率
python -m pytest tests/integration/api/v1/admin/ tests/integration/api/v1/member/ tests/integration/api/v1/public/ --cov=app --cov-report=term-missing > new_coverage.txt

# 对比覆盖率
echo "覆盖率对比:"
echo "原有覆盖率:"
tail -n 5 old_coverage.txt
echo "新结构覆盖率:"
tail -n 5 new_coverage.txt
```

### 2. 测试执行时间对比
```bash
# 原有测试执行时间
time python -m pytest tests/integration/api/v1/backup/ -q

# 新结构测试执行时间
time python -m pytest tests/integration/api/v1/admin/ tests/integration/api/v1/member/ tests/integration/api/v1/public/ -q
```

## 常见问题排查

### 1. API路径未正确更新
**症状**: 测试失败，404错误
**排查**:
```bash
# 查找未更新的API路径
grep -r "/api/v1/courses/classes/" tests/integration/api/v1/admin/
grep -r "/api/v1/members/" tests/integration/api/v1/admin/
```

**解决**: 手动更新遗漏的API路径

### 2. 导入错误
**症状**: ImportError或ModuleNotFoundError
**排查**:
```bash
# 检查__init__.py文件
ls -la tests/integration/api/v1/*/
```

**解决**: 确保所有目录都有__init__.py文件

### 3. 测试数据依赖问题
**症状**: 测试失败，数据不存在
**排查**:
```bash
# 检查fixtures是否正确导入
grep -r "def.*fixture" tests/integration/api/v1/admin/
```

**解决**: 确保fixtures在新的测试文件中正确导入

### 4. 权限认证问题
**症状**: 401认证错误
**排查**:
```bash
# 检查token使用
grep -r "admin_token\|member_token" tests/integration/api/v1/
```

**解决**: 确保使用正确的认证token

## 回滚方案

如果迁移出现严重问题，可以快速回滚：

### 1. 恢复原有测试文件
```bash
# 从备份恢复
cp tests/integration/api/v1/backup/*.py tests/integration/api/v1/

# 删除新结构目录
rm -rf tests/integration/api/v1/admin/
rm -rf tests/integration/api/v1/member/
rm -rf tests/integration/api/v1/public/
```

### 2. 验证回滚结果
```bash
# 运行原有测试确保正常
python -m pytest tests/integration/api/v1/ -v
```

## 最终验证清单

- [ ] 所有管理端测试通过
- [ ] 所有会员端测试通过  
- [ ] 所有公共API测试通过
- [ ] 测试覆盖率不低于原有水平
- [ ] 没有导入错误
- [ ] API路径全部正确更新
- [ ] 新增的会员卡API测试正常
- [ ] 测试执行时间在可接受范围内
- [ ] CI/CD配置已更新（如果有）

完成以上验证后，可以安全地删除备份文件并提交更改。
