# API集成测试用例调整操作指南

## 前置准备

### 1. 备份现有测试文件
```bash
# 创建测试备份目录
mkdir -p tests/integration/api/v1/backup
cp tests/integration/api/v1/test_*.py tests/integration/api/v1/backup/
```

### 2. 创建新的测试目录结构
```bash
# 创建按角色分组的测试目录
mkdir -p tests/integration/api/v1/admin
mkdir -p tests/integration/api/v1/member
mkdir -p tests/integration/api/v1/public

# 创建目录初始化文件
touch tests/integration/api/v1/admin/__init__.py
touch tests/integration/api/v1/member/__init__.py
touch tests/integration/api/v1/public/__init__.py
```

## 第一阶段：管理端API测试迁移

### Step 1: 迁移租户管理测试
```bash
# 复制并重命名文件
cp tests/integration/api/v1/test_tenant_api.py tests/integration/api/v1/admin/test_tenants.py

# 更新API路径（使用sed命令批量替换）
sed -i 's|/api/v1/tenants/|/api/v1/admin/tenants/|g' tests/integration/api/v1/admin/test_tenants.py

# 更新类名
sed -i 's/class TestTenantAPI/class TestAdminTenantAPI/g' tests/integration/api/v1/admin/test_tenants.py
```

### Step 2: 迁移用户管理测试
```bash
cp tests/integration/api/v1/test_user_api.py tests/integration/api/v1/admin/test_users.py
sed -i 's|/api/v1/users/|/api/v1/admin/users/|g' tests/integration/api/v1/admin/test_users.py
sed -i 's/class TestUserAPI/class TestAdminUserAPI/g' tests/integration/api/v1/admin/test_users.py
```

### Step 3: 迁移会员管理测试
```bash
cp tests/integration/api/v1/test_member_api.py tests/integration/api/v1/admin/test_members.py
sed -i 's|/api/v1/members/|/api/v1/admin/members/|g' tests/integration/api/v1/admin/test_members.py
sed -i 's/class TestMemberAPI/class TestAdminMemberAPI/g' tests/integration/api/v1/admin/test_members.py

# 处理会员固定课位测试
cp tests/integration/api/v1/test_member_fixed_locks.py tests/integration/api/v1/admin/test_member_fixed_locks.py
sed -i 's|/api/v1/members/fixed-locks/|/api/v1/admin/members/fixed-locks/|g' tests/integration/api/v1/admin/test_member_fixed_locks.py
```

### Step 4: 迁移教师管理测试
```bash
cp tests/integration/api/v1/test_teacher_api.py tests/integration/api/v1/admin/test_teachers.py
sed -i 's|/api/v1/teachers/|/api/v1/admin/teachers/|g' tests/integration/api/v1/admin/test_teachers.py
sed -i 's/class TestTeacherAPI/class TestAdminTeacherAPI/g' tests/integration/api/v1/admin/test_teachers.py

# 处理教师固定时间段测试
cp tests/integration/api/v1/test_teacher_fixed_slot_api.py tests/integration/api/v1/admin/test_teacher_fixed_slots.py
sed -i 's|/api/v1/teachers/fixed-slots/|/api/v1/admin/teachers/fixed-slots/|g' tests/integration/api/v1/admin/test_teacher_fixed_slots.py
```

### Step 5: 迁移标签管理测试
```bash
cp tests/integration/api/v1/test_tag_api.py tests/integration/api/v1/admin/test_tags.py
sed -i 's|/api/v1/tags/|/api/v1/admin/tags/|g' tests/integration/api/v1/admin/test_tags.py
sed -i 's/class TestTagCategoryAPI/class TestAdminTagCategoryAPI/g' tests/integration/api/v1/admin/test_tags.py
sed -i 's/class TestTagAPI/class TestAdminTagAPI/g' tests/integration/api/v1/admin/test_tags.py
```

### Step 6: 迁移课程管理测试
```bash
# 课程配置测试
cp tests/integration/api/v1/test_course_config.py tests/integration/api/v1/admin/test_course_config.py
sed -i 's|/api/v1/courses/|/api/v1/admin/courses/config/|g' tests/integration/api/v1/admin/test_course_config.py

# 已排课程测试（最复杂的一个）
cp tests/integration/api/v1/test_scheduled_classes_api.py tests/integration/api/v1/admin/test_courses.py
sed -i 's|/api/v1/courses/classes/|/api/v1/admin/courses/classes/|g' tests/integration/api/v1/admin/test_courses.py
sed -i 's/class TestScheduledClassAPI/class TestAdminCourseAPI/g' tests/integration/api/v1/admin/test_courses.py
```

## 第二阶段：公共API测试迁移

### Step 7: 迁移认证测试
```bash
# 认证API路径无变更，只需移动文件
cp tests/integration/api/v1/test_auth_api.py tests/integration/api/v1/public/test_auth.py
```

### Step 8: 创建公开信息测试
```bash
cat > tests/integration/api/v1/public/test_info.py << 'EOF'
"""公开信息API集成测试"""
import pytest
from fastapi.testclient import TestClient


class TestPublicInfoAPI:
    """公开信息API测试"""
    
    def test_health_check(self, client: TestClient):
        """测试健康检查接口"""
        response = client.get("/api/v1/info/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "ok"
        assert data["message"] == "服务正常运行"
    
    def test_get_version(self, client: TestClient):
        """测试获取版本信息"""
        response = client.get("/api/v1/info/version")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert data["data"]["api_version"] == "v1"
EOF
```

## 第三阶段：创建会员端API测试

### Step 9: 创建会员端个人信息测试
```bash
cat > tests/integration/api/v1/member/test_profile.py << 'EOF'
"""会员端个人信息API集成测试"""
import pytest
from fastapi.testclient import TestClient


class TestMemberProfileAPI:
    """会员端个人信息API测试"""
    
    def test_get_my_profile(self, client: TestClient, member_token):
        """测试获取我的个人信息"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get("/api/v1/member/profile/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "name" in data["data"]
    
    def test_update_my_profile(self, client: TestClient, member_token):
        """测试更新我的个人信息"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        update_data = {
            "name": "更新后的姓名",
            "address": "更新后的地址"
        }
        
        response = client.put(
            "/api/v1/member/profile/me",
            json=update_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["name"] == update_data["name"]
EOF
```

### Step 10: 创建会员端课程测试
```bash
cat > tests/integration/api/v1/member/test_courses.py << 'EOF'
"""会员端课程API集成测试"""
import pytest
from fastapi.testclient import TestClient


class TestMemberCourseAPI:
    """会员端课程API测试"""
    
    def test_get_available_courses(self, client: TestClient, member_token):
        """测试获取可预约课程"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get(
            "/api/v1/member/courses/available?page=1&size=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "items" in data["data"]
    
    def test_get_my_courses(self, client: TestClient, member_token):
        """测试获取我的课程"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get(
            "/api/v1/member/courses/my?page=1&size=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_book_course(self, client: TestClient, member_token, sample_available_class):
        """测试预约课程"""
        headers = {"Authorization": f"Bearer {member_token}"}
        class_id = sample_available_class["id"]
        
        booking_data = {
            "booking_remark": "会员自主预约"
        }
        
        response = client.post(
            f"/api/v1/member/courses/{class_id}/book",
            json=booking_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
EOF
```

### Step 11: 创建会员卡管理测试（任务7.5）
```bash
cat > tests/integration/api/v1/admin/test_member_cards.py << 'EOF'
"""管理端会员卡API集成测试"""
import pytest
from fastapi.testclient import TestClient


class TestAdminMemberCardAPI:
    """管理端会员卡API测试"""
    
    def test_get_card_templates(self, client: TestClient, admin_token):
        """测试获取会员卡模板列表"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get(
            "/api/v1/admin/member-cards/templates?page=1&size=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_create_card_template(self, client: TestClient, admin_token):
        """测试创建会员卡模板"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        template_data = {
            "name": "测试储值卡模板",
            "card_type": "VALUE_UNLIMITED",
            "description": "测试用储值卡模板",
            "is_active": True
        }
        
        response = client.post(
            "/api/v1/admin/member-cards/templates",
            json=template_data,
            headers=headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert data["data"]["name"] == template_data["name"]
    
    def test_get_member_cards(self, client: TestClient, admin_token):
        """测试获取会员卡列表"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get(
            "/api/v1/admin/member-cards/cards?page=1&size=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_recharge_member_card(self, client: TestClient, admin_token, created_member_card):
        """测试会员卡充值"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        card_id = created_member_card["id"]
        
        recharge_data = {
            "amount": 1000,
            "payment_method": "CASH",
            "remark": "测试充值"
        }
        
        response = client.post(
            f"/api/v1/admin/member-cards/cards/{card_id}/recharge",
            json=recharge_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
EOF

cat > tests/integration/api/v1/member/test_cards.py << 'EOF'
"""会员端会员卡API集成测试"""
import pytest
from fastapi.testclient import TestClient


class TestMemberCardAPI:
    """会员端会员卡API测试"""
    
    def test_get_my_cards(self, client: TestClient, member_token):
        """测试获取我的会员卡"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get("/api/v1/member/cards/my", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert isinstance(data["data"], list)
    
    def test_get_my_primary_card(self, client: TestClient, member_token):
        """测试获取我的主要会员卡"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get("/api/v1/member/cards/my/primary", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
EOF

cat > tests/integration/api/v1/member/test_records.py << 'EOF'
"""会员端记录查询API集成测试"""
import pytest
from fastapi.testclient import TestClient


class TestMemberRecordAPI:
    """会员端记录查询API测试"""
    
    def test_get_my_operations(self, client: TestClient, member_token):
        """测试获取我的操作记录"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get(
            "/api/v1/member/records/operations?page=1&size=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_get_my_recharge_history(self, client: TestClient, member_token):
        """测试获取我的充值记录"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get(
            "/api/v1/member/records/recharge-history?page=1&size=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
EOF
```

## 第四阶段：验证和清理

### Step 12: 运行测试验证
```bash
# 运行管理端测试
python -m pytest tests/integration/api/v1/admin/ -v

# 运行会员端测试
python -m pytest tests/integration/api/v1/member/ -v

# 运行公共API测试
python -m pytest tests/integration/api/v1/public/ -v

# 运行所有新结构的测试
python -m pytest tests/integration/api/v1/admin/ tests/integration/api/v1/member/ tests/integration/api/v1/public/ -v
```

### Step 13: 对比测试结果
```bash
# 运行原有测试（备份版本）
python -m pytest tests/integration/api/v1/backup/ -v > old_test_results.txt

# 运行新结构测试
python -m pytest tests/integration/api/v1/admin/ tests/integration/api/v1/member/ tests/integration/api/v1/public/ -v > new_test_results.txt

# 对比结果
diff old_test_results.txt new_test_results.txt
```

### Step 14: 清理旧文件（可选）
```bash
# 确认新测试都通过后，可以删除旧的测试文件
# rm tests/integration/api/v1/test_*.py

# 或者移动到archive目录
mkdir -p tests/integration/api/v1/archive
mv tests/integration/api/v1/test_*.py tests/integration/api/v1/archive/
```

## 第五阶段：更新测试配置

### Step 15: 更新pytest配置
```bash
# 如果有pytest.ini或pyproject.toml中的测试路径配置，需要更新
# 确保新的测试目录被包含在测试发现路径中
```

### Step 16: 更新CI/CD配置
```bash
# 如果有GitHub Actions或其他CI配置，更新测试命令
# 例如：pytest tests/integration/api/v1/admin/ tests/integration/api/v1/member/ tests/integration/api/v1/public/
```

## 注意事项

### 1. 测试数据依赖
- 确保所有fixtures在新的测试结构中仍然可用
- 检查测试之间的数据依赖关系
- 验证测试数据库的隔离性

### 2. 导入路径
- 新的测试文件可能需要调整导入路径
- 确保所有必要的模块都能正确导入

### 3. 测试覆盖率
- 运行覆盖率测试确保没有遗漏
- 新增的API需要添加相应的测试用例

### 4. 性能考虑
- 新的测试结构可能影响测试运行时间
- 考虑并行运行测试以提高效率
