#!/usr/bin/env python3
"""
测试结果对比脚本
"""

import re
import sys
from pathlib import Path
from typing import Dict, List, Tuple


class TestResultComparator:
    """测试结果对比器"""
    
    def __init__(self):
        self.test_pattern = re.compile(r'(tests/[^:]+)::\S+\s+(PASSED|FAILED|ERROR|SKIPPED)')
        self.summary_pattern = re.compile(r'=+ (\d+) (passed|failed|error|skipped)')
    
    def parse_test_results(self, file_path: str) -> Dict[str, List[str]]:
        """解析测试结果文件"""
        results = {
            'PASSED': [],
            'FAILED': [],
            'ERROR': [],
            'SKIPPED': []
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取每个测试的结果
            matches = self.test_pattern.findall(content)
            for test_path, status in matches:
                results[status].append(test_path)
            
            return results
        
        except FileNotFoundError:
            print(f"❌ 文件不存在: {file_path}")
            return results
        except Exception as e:
            print(f"❌ 解析文件失败: {e}")
            return results
    
    def extract_summary(self, file_path: str) -> Dict[str, int]:
        """提取测试摘要统计"""
        summary = {
            'passed': 0,
            'failed': 0,
            'error': 0,
            'skipped': 0
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找摘要行
            matches = self.summary_pattern.findall(content)
            for count, status in matches:
                summary[status] = int(count)
            
            return summary
        
        except Exception as e:
            print(f"❌ 提取摘要失败: {e}")
            return summary
    
    def compare_results(self, baseline_file: str, new_file: str):
        """对比两个测试结果文件"""
        print("🔍 开始对比测试结果...")
        print("=" * 60)
        
        # 解析测试结果
        baseline_results = self.parse_test_results(baseline_file)
        new_results = self.parse_test_results(new_file)
        
        # 提取摘要
        baseline_summary = self.extract_summary(baseline_file)
        new_summary = self.extract_summary(new_file)
        
        # 显示摘要对比
        self.print_summary_comparison(baseline_summary, new_summary)
        
        # 显示详细对比
        self.print_detailed_comparison(baseline_results, new_results)
        
        # 分析差异
        self.analyze_differences(baseline_results, new_results)
    
    def print_summary_comparison(self, baseline: Dict[str, int], new: Dict[str, int]):
        """打印摘要对比"""
        print("📊 测试摘要对比:")
        print("-" * 40)
        
        categories = ['passed', 'failed', 'error', 'skipped']
        
        print(f"{'状态':<10} {'原有':<8} {'新结构':<8} {'差异':<8}")
        print("-" * 40)
        
        for category in categories:
            old_count = baseline.get(category, 0)
            new_count = new.get(category, 0)
            diff = new_count - old_count
            
            diff_str = f"+{diff}" if diff > 0 else str(diff) if diff < 0 else "0"
            status_icon = "✅" if category == 'passed' else "❌" if category in ['failed', 'error'] else "⚠️"
            
            print(f"{status_icon} {category:<8} {old_count:<8} {new_count:<8} {diff_str:<8}")
        
        # 计算总数
        old_total = sum(baseline.values())
        new_total = sum(new.values())
        total_diff = new_total - old_total
        
        print("-" * 40)
        print(f"📈 总计      {old_total:<8} {new_total:<8} {'+' + str(total_diff) if total_diff > 0 else str(total_diff)}")
        print()
    
    def print_detailed_comparison(self, baseline: Dict[str, List[str]], new: Dict[str, List[str]]):
        """打印详细对比"""
        print("📋 详细测试对比:")
        print("-" * 40)
        
        # 对比通过的测试
        baseline_passed = set(baseline['PASSED'])
        new_passed = set(new['PASSED'])
        
        # 新增通过的测试
        newly_passed = new_passed - baseline_passed
        if newly_passed:
            print("✅ 新增通过的测试:")
            for test in sorted(newly_passed):
                print(f"  + {test}")
            print()
        
        # 不再通过的测试
        no_longer_passed = baseline_passed - new_passed
        if no_longer_passed:
            print("❌ 不再通过的测试:")
            for test in sorted(no_longer_passed):
                print(f"  - {test}")
            print()
        
        # 对比失败的测试
        baseline_failed = set(baseline['FAILED'] + baseline['ERROR'])
        new_failed = set(new['FAILED'] + new['ERROR'])
        
        # 新增失败的测试
        newly_failed = new_failed - baseline_failed
        if newly_failed:
            print("❌ 新增失败的测试:")
            for test in sorted(newly_failed):
                print(f"  + {test}")
            print()
        
        # 修复的测试
        fixed_tests = baseline_failed - new_failed
        if fixed_tests:
            print("🔧 已修复的测试:")
            for test in sorted(fixed_tests):
                print(f"  ✓ {test}")
            print()
    
    def analyze_differences(self, baseline: Dict[str, List[str]], new: Dict[str, List[str]]):
        """分析差异并给出建议"""
        print("🔍 差异分析和建议:")
        print("-" * 40)
        
        baseline_total = sum(len(tests) for tests in baseline.values())
        new_total = sum(len(tests) for tests in new.values())
        
        baseline_passed = len(baseline['PASSED'])
        new_passed = len(new['PASSED'])
        
        baseline_failed = len(baseline['FAILED']) + len(baseline['ERROR'])
        new_failed = len(new['FAILED']) + len(new['ERROR'])
        
        # 分析通过率
        baseline_pass_rate = (baseline_passed / baseline_total * 100) if baseline_total > 0 else 0
        new_pass_rate = (new_passed / new_total * 100) if new_total > 0 else 0
        
        print(f"📈 通过率对比:")
        print(f"  原有: {baseline_pass_rate:.1f}% ({baseline_passed}/{baseline_total})")
        print(f"  新结构: {new_pass_rate:.1f}% ({new_passed}/{new_total})")
        
        if new_pass_rate >= baseline_pass_rate:
            print("  ✅ 通过率保持或提升")
        else:
            print("  ⚠️ 通过率下降，需要关注")
        
        print()
        
        # 给出建议
        print("💡 建议:")
        
        if new_failed > baseline_failed:
            print("  ❌ 失败测试增加，建议:")
            print("    1. 检查API路径是否正确更新")
            print("    2. 验证测试数据和fixtures")
            print("    3. 确认认证token使用正确")
        
        if new_total > baseline_total:
            print("  ✅ 测试数量增加，说明新增了测试用例")
        
        if new_passed >= baseline_passed:
            print("  ✅ 通过测试数量保持或增加")
        
        if new_failed == 0:
            print("  🎉 所有测试都通过了！迁移成功！")
        
        print()
        print("🚀 下一步操作:")
        print("  1. 如果有失败测试，根据错误信息进行修复")
        print("  2. 运行覆盖率测试确保覆盖率不降低")
        print("  3. 更新CI/CD配置以使用新的测试路径")
        print("  4. 删除备份文件并提交更改")


def main():
    """主函数"""
    if len(sys.argv) != 3:
        print("用法: python compare_test_results.py <baseline_file> <new_file>")
        print("示例: python compare_test_results.py baseline_results.txt new_results.txt")
        sys.exit(1)
    
    baseline_file = sys.argv[1]
    new_file = sys.argv[2]
    
    # 检查文件是否存在
    if not Path(baseline_file).exists():
        print(f"❌ 基准文件不存在: {baseline_file}")
        sys.exit(1)
    
    if not Path(new_file).exists():
        print(f"❌ 新结果文件不存在: {new_file}")
        sys.exit(1)
    
    # 执行对比
    comparator = TestResultComparator()
    comparator.compare_results(baseline_file, new_file)


if __name__ == "__main__":
    main()
