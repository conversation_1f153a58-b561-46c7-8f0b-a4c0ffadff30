# 1.产品整体概述

目标：为教育机构/培训机构【租户】提供在线课程预约管理解决方案

核心价值：

- 学员自主预约课程
- 教师课时智能管理
- 机构运营效率提升
- 减少人工调度成本

# 2. 产品产出形态

## 1. 会员端-h5+小程序

H5 + 小程序

会员约课用

## 2. 管理端-cms 管理系统--机构（租户）用

网站，支持手机和 pc

## 3.server 端

提供接口给 约课前端 和 server 端

## 4.教师端-h5

教师用，功能最简单，只有三四个页面：

管理个人课表、设置可约时间、查看学员名单

# 3.用户角色

| 角色/系统  | 核心功能                                                                            | 对应系统                     | 重要             |
| ---------- | ----------------------------------------------------------------------------------- | ---------------------------- | ---------------- |
| 管理员     | 课程管理、用户管理、数据统计、系统配置                                              | 管理端                       | 属于机构（租户） |
| 代理       | 部分管理员权限，主要看自己邀请来的会员相关数据                                      | 管理端                       | 属于机构（租户） |
| 超级管理员 | 管理机构（租户）                                                                    | 管理端（暂定，先不考虑实现） |                  |
| 学员       | 查看教师，确认取消固定位，查看自己的固定课表，查看已排课表、预约/取消课程、评价课程 | 会员端                       | 属于机构（租户） |
| 教师       | 管理个人课表、设置可约时间、查看学员名单                                            | 教师端                       | 机构（租户）     |
|            |                                                                                     |                              |                  |

# 4.管理端-核心功能模块

## 0.最核心：1 对 1 课

课表开课时间：yyyy hh:mm 如 2025-06-28 18:30

通常 25 分钟一节课，休息 5 分钟

所以某个老师课表上同一天的课通常为

2025-06-28 18:30

2025-06-28 19:00

2025-06-28 19:30

如果有空课，则跳过半小时，如

2025-06-28 20:00

[空课]

2025-06-28 20:30

### 功能介绍

1 对 1 课有临时约课和固定课表约课两种方式

**临时约课**：老师或者管理员可以直接打开某天某个时间段的空课，对应的写入该记录到

**固定课表约课**规则如下：

**固定课表约课**逻辑描述：

1. 每个老师可以开放一个**[固定时间占位表]**，以自然周为维度，可以开放周 1-周天内，任何一个时间段的课

   - 同样以 25 分钟为一节课，然后间隔 5 分钟才可以排另一节课

   每个老师的固定时间位置数据对后端 api 来说实际为一个数组，或者 key 为 week1，week2.... week7 的字典，值为具体的

2. 会员可以对该表进行任意选择占位

3. 管理员会设定具体时间或者规则，触发排课，且可以设置排课周期为任意 x 周

4. 排课逻辑：按照该**[固定时间占位表]**，对所有老师已经被会员选定占位的课程进行排课，排课到**[已排课表]**

5. 每周一排课，排出未来 4 周的课表，已经被会员固定的位置会排给对应会员的课，没被固定的空位会排出空的临时课，可供会员预约

### 固定时间占位表

老师开放的时间段表，用来给会员提前占位，锁定固定位，详细见上面的功能介绍-固定课表约课逻辑描述

## 1. 会员相关

### 列表展示

```
微信头像	姓名	手机号	性别&生日	持有会员卡	1对1未上课节/总课节	会员类型 会员状态	来源&注册时间	销售/代理	备注
```

当前 member 表结构基本符合

### 消费记录

```
会员id 会员卡	操作时间	操作类型	余额变化	有效期变化	实收金额	操作人	备注	状态
```

操作类型有：约课扣费/手动扣费，取消约课，充值/首次绑卡

### 1 对 1 预约记录

```
操作时间	操作人	是否允许会员取消	备注	预约教材
```

实际数据可能对应的是 [表-教师已排课表]

### 1 对 1 固定课

```
老师[编号-姓名]	星期	时间	操作人	操作时间
```

### 其他

#### 1 对 1 可见老师设置

暂时忽略

#### 1 对 1 上课记录

```
预约老师[编号-姓名]	上课时间	使用的会员卡	扣除卡次	预约备注
```

#### 1 对 1 取消记录

```
取消预约操作时间	取消预约会员卡	老师[编号-姓名]	操作人	上课时间
```

## 2.会员卡

### 会员卡模板

可新增的会员卡模板，具体属性有：

```
会员卡名称	卡类型	售卖价格	可用余额（次or元）有效期	是否支持线上购卡	是否仅代理可售	是否支持重复购卡	是否支持续费
```

卡类型有:次卡-有期限，次卡-无期限，储值卡-有期限，储值卡-无期限

#### 支持设置多档续费奖励，比如：

可增删档次 1

续费价格：100 元，奖励：0 元，充值天数：50 天

则实际到账 100 元，会员卡顺延 50 天

可增删档次 2

续费价格：1000 元，奖励：35 元，充值天数：200 天

则实际到账 1035 元，会员卡顺延 200 天

可增删档次 3

续费价格：2000 元，奖励：80 元，充值天数：365 天

则实际到账 2080 元，会员卡顺延 365 天

### 会员卡

会员持有的具体某个会员卡

```
会员卡名称 卡类型 余额 到期时间 激活/停用
```

- 每个新会员默认生成一个余额为 0，有效期为 30 天的储值卡
- 每个会员有且只有一个储值卡
- 每个会员可以有多个次卡(次卡通常是拉新或者做活动奖励，优惠，赠送等)

## 3.老师相关

1. 管理老师个人课表固定位
2. 管理老师个人已开放课表
   1. 开放新位置
3. 设置可约时间
4. 查看老师已排课学员名单
5. 可以给教师设置标签，支持多标签（注意标签管理）
6. 支持灵活丰富的筛选功能
   1. 可以按照日程，预定量，空位，教材等筛选老师
   2. 可以按照标签筛选
   3. 按照有时间段内是空课情况
   4. 区域（欧美/菲/南非）
   5. 单节价格

### 教师列表

```
头像	老师姓名	性别 单节价格	手机号	邮箱	角色 	老师分类 标签[json或者数组，方便检索，可多选]	微信绑定	是否在会员端展示
```

### 1 对 1 上课记录

```
学生[会员] 上课时间 预约备注
```

### 1v1 已排课表

```
预约老师[编号-姓名] 老师id 上课时间(精确到分)	使用的会员卡 排课类型（固定|非固定）价格	预约备注 所选教材
```

也就是老师已经排好课的表，代表老师已经打开的所有可预约具体时间段的表，包含：

- 每一条记录代表某个老师，某天的某个时间段的某节课
- 包括被预定的会员（可选），是否可预定状态
- 是否对外开放
- 排课类型 （是否是固定课排课）
- 会员是否可取消
- 上课时间： 2025-06-28 18:30

### 3.4 通知提醒

- 通知形式可选：微信公众号 or 短信

通知节点：

- 预约成功提醒
- 上课前提醒（可配置提前时间）
- 取消课程通知

### 3.5 数据分析

- 课程出勤率统计
- 教师课时统计

# 5.管理端-其他功能

todo

# 6.痛点（根据好约课的功能迭代）

### 管理端

1. 清晰的固定课逻辑
2. 根据代理/员工角色不同配置不同的管理员权限
3. 会员营销功能 低优先级

### 会员端

1. 按照标签筛选老师
2. 老师展示视频功能：展示照片和视频
3. 会员营销功能 低优先级
