# 认证开发快速指南

## 🚀 核心概念

### JWT 结构

```
Header.Payload.Signature
```

### 用户类型

- **admin**: 管理员 (User 表) - 后台 CMS
- **member**: 会员 (Member 表) - 前端网站/移动端

## 📦 JWT 载荷设计

### 管理员 JWT

```json
{
  "sub": "user_uuid",
  "tenant_id": "tenant_uuid",
  "user_type": "admin",
  "role": "admin|super_admin|agent|sale",
  "email": "<EMAIL>",
  "exp": 1641081600
}
```

### 会员 JWT

```json
{
  "sub": "member_uuid",
  "tenant_id": "tenant_uuid",
  "user_type": "member",
  "membership_level": "vip|regular",
  "exp": 1641081600
}
```

## 🔐 认证端点设计

### 管理员认证

```python
# 登录
POST /api/v1/auth/admin/login
{
  "email": "<EMAIL>",
  "password": "password123"
}

# 响应
{
  "access_token": "jwt_token",
  "token_type": "bearer",
  "user": {...}
}
```

### 会员认证

```python
# 微信登录
POST /api/v1/auth/member/wechat-login
{
  "code": "wechat_auth_code",
  "tenant_id": "tenant_uuid"
}

# 手机号登录 (可选)
POST /api/v1/auth/member/phone-login
{
  "phone": "13800138000",
  "sms_code": "123456",
  "tenant_id": "tenant_uuid"
}
```

## 🛡️ 权限装饰器

### 基本用法

```python
from app.utils.auth import require_auth

@require_auth("admin")
async def admin_only_endpoint():
    pass

@require_auth("member")
async def member_only_endpoint():
    pass

@require_auth("admin", permissions=["member:manage"])
async def manage_members():
    pass
```

### 获取当前用户

```python
from app.core.dependencies import get_current_user

async def some_endpoint(current_user: User = Depends(get_current_user)):
    tenant_id = current_user.tenant_id
    user_id = current_user.id
```

## 📁 文件结构

```
app/
├── utils/
│   ├── auth.py           # JWT工具函数
│   └── security.py       # 密码加密等
├── services/
│   └── auth_service.py   # 认证业务逻辑
├── api/v1/endpoints/
│   └── auth.py           # 认证端点
├── models/
│   └── auth.py           # 认证相关模型
└── dependencies.py       # 依赖注入
```

## ⚙️ 配置参数

```python
# .env
JWT_SECRET_KEY=your-256-bit-secret
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_HOURS=2

# 微信配置
WECHAT_APP_ID=your_app_id
WECHAT_APP_SECRET=your_app_secret
```

## 🔧 核心实现

### 1. JWT 工具 (app/utils/auth.py)

```python
def create_access_token(data: dict, expires_delta: timedelta = None):
    """创建JWT令牌"""

def verify_token(token: str) -> dict:
    """验证JWT令牌"""

def get_password_hash(password: str) -> str:
    """密码加密"""

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """密码验证"""
```

### 2. 认证服务 (app/services/auth_service.py)

```python
async def authenticate_admin(email: str, password: str) -> User:
    """管理员认证"""

async def authenticate_member_wechat(code: str, tenant_id: str) -> Member:
    """会员微信认证"""

async def create_access_token_for_user(user: User) -> str:
    """为用户创建令牌"""
```

### 3. 依赖注入 (app/dependencies.py)

```python
async def get_current_user(token: str = Depends(oauth2_scheme)):
    """获取当前用户"""

async def get_current_admin(current_user = Depends(get_current_user)):
    """获取当前管理员"""

async def get_current_member(current_user = Depends(get_current_user)):
    """获取当前会员"""
```

## 🧪 测试要点

### 测试用例覆盖

```python
# 认证测试
def test_admin_login_success()
def test_admin_login_invalid_password()
def test_member_wechat_login_success()
def test_jwt_token_validation()
def test_expired_token_rejection()

# 权限测试
def test_admin_access_member_endpoint()
def test_member_access_admin_endpoint_forbidden()
def test_cross_tenant_access_forbidden()
```

## 🚨 安全检查清单

- [ ] JWT 密钥足够复杂 (256 位)
- [ ] 令牌过期时间合理 (user 2 小时/ member 7 天)
- [ ] 密码使用 bcrypt 加密
- [ ] HTTPS 强制传输
- [ ] 跨租户访问隔离
- [ ] 敏感端点权限校验
- [ ] 登录频次限制

## 📋 开发步骤

1. **配置环境变量** - JWT 密钥、微信配置
2. **实现 JWT 工具** - 令牌创建/验证
3. **创建认证服务** - 用户认证逻辑
4. **添加认证端点** - 登录/注销接口
5. **实现权限装饰器** - 访问控制
6. **编写测试用例** - 覆盖核心场景
7. **前端集成测试** - 端到端验证

## 💡 最佳实践

- **令牌存储**: 管理员用 localStorage，会员用 sessionStorage
- **错误处理**: 统一返回 401/403 状态码
- **日志记录**: 记录登录/权限验证日志
- **性能优化**: JWT 验证无需数据库查询
- **扩展性**: 预留 OAuth2.0 接口设计
