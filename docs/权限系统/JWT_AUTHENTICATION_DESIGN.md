# JWT 认证架构设计文档

## 1. JWT (JSON Web Tokens) 详解

### 1.1 JWT 基本结构

JWT 由三部分组成，用点(.)分隔：

```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

#### Header (头部)

```json
{
  "alg": "HS256", // 签名算法
  "typ": "JWT" // 令牌类型
}
```

#### Payload (载荷)

```json
{
  "sub": "user_id", // 主题 (用户ID)
  "tenant_id": "tenant_123", // 租户ID
  "user_type": "admin", // 用户类型 (admin/member)
  "phone": "xxxxxxxx",
  "username": "xxxx",
  "iat": 1516239022, // 签发时间
  "exp": 1516325422, // 过期时间
  "jti": "unique_token_id" // JWT ID (用于撤销)
}
```

#### Signature (签名)

```
HMACSHA256(
  base64UrlEncode(header) + "." +
  base64UrlEncode(payload),
  secret
)
```

### 1.2 JWT 优势

#### ✅ 优点

- **无状态**: 服务器不需要存储会话信息
- **跨域友好**: 支持 CORS，适合分布式系统
- **自包含**: 令牌包含所有必要信息
- **标准化**: 基于开放标准，生态丰富
- **性能好**: 无需数据库查询验证
- **移动友好**: 适合移动应用和 SPA

#### ⚠️ 注意事项

- **无法撤销**: 令牌在过期前始终有效
- **安全存储**: 需要安全存储在客户端
- **载荷大小**: 信息过多会增加请求大小
- **时钟同步**: 依赖服务器时钟同步

### 1.3 适用场景

JWT 特别适合以下场景：

- **SaaS 多租户系统** ✅
- **前后端分离架构** ✅
- **移动应用 API** ✅
- **微服务架构** ✅
- **第三方 API 集成** ✅

## 2. 多租户 SaaS 认证架构设计

### 2.1 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   管理员前端     │    │   会员前端       │    │   移动应用       │
│   (CMS)         │    │   (Website)     │    │   (Mobile)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   FastAPI       │
                    │   Auth Service  │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   PostgreSQL    │
                    │   Multi-Tenant  │
                    └─────────────────┘
```

### 2.2 用户类型设计

#### 管理员用户 (User)

- **角色**: 租户管理员、超级管理员
- **权限**: 管理会员、课程、系统配置
- **访问**: 后台 CMS 系统

#### 会员用户 (Member)

- **角色**: 普通会员、VIP 会员
- **权限**: 预约课程、查看记录、个人资料
- **访问**: 前端网站、移动应用

### 2.3 JWT 载荷设计

#### 管理员 JWT 载荷

```json
{
  "sub": "user_uuid",
  "tenant_id": "tenant_uuid",
  "user_type": "admin",
  "role": "super_admin|admin",
  "phone": "xxxxxxxx",
  "username": "xxxx",
  "permissions": ["user_manage", "member_manage", "course_manage"],
  "iat": 1640995200,
  "exp": 1641081600,
  "jti": "jwt_unique_id"
}
```

#### 会员 JWT 载荷

```json
{
  "sub": "member_uuid",
  "tenant_id": "tenant_uuid",
  "user_type": "member",
  "membership_level": "vip|regular",
  "wechat_openid": "wx_openid",
  "iat": 1640995200,
  "exp": 1641081600,
  "jti": "jwt_unique_id"
}
```

## 3. 认证流程设计

### 3.1 管理员登录流程

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant Frontend as CMS前端
    participant API as FastAPI后端
    participant DB as PostgreSQL

    Admin->>Frontend: 输入用户名(手机号) + 密码
    Frontend->>API: POST /api/v1/auth/admin/login
    API->>DB: 验证用户凭据
    DB-->>API: 返回用户信息
    API->>API: 生成JWT令牌
    API-->>Frontend: 返回JWT + 用户信息
    Frontend->>Frontend: 存储JWT到localStorage
    Frontend-->>Admin: 登录成功，跳转到仪表板
```

### 3.2 会员登录流程

```mermaid
sequenceDiagram
    participant Member as 会员
    participant Frontend as 网站前端
    participant API as FastAPI后端
    participant WeChat as 微信服务
    participant DB as PostgreSQL

    Member->>Frontend: 点击微信登录
    Frontend->>WeChat: 获取微信授权码
    WeChat-->>Frontend: 返回授权码
    Frontend->>API: POST /api/v1/auth/member/wechat-login
    API->>WeChat: 验证授权码，获取openid
    WeChat-->>API: 返回openid和用户信息
    API->>DB: 查询或创建会员记录
    DB-->>API: 返回会员信息
    API->>API: 生成JWT令牌
    API-->>Frontend: 返回JWT + 会员信息
    Frontend-->>Member: 登录成功
```

### 3.3 令牌验证流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as FastAPI后端
    participant Cache as Redis缓存

    Client->>API: 请求API (携带JWT)
    API->>API: 解析JWT Header
    API->>API: 验证JWT签名
    API->>Cache: 检查JWT是否在黑名单
    Cache-->>API: 返回检查结果

    alt JWT有效且未被撤销
        API->>API: 提取用户信息
        API->>API: 设置RLS上下文
        API->>API: 执行业务逻辑
        API-->>Client: 返回响应数据
    else JWT无效或已撤销
        API-->>Client: 返回401未授权
    end
```

## 4. 安全策略

### 4.1 令牌管理策略

#### 令牌过期时间

- **Access Token**: 2 小时 (管理员) / 24 小时 (会员)
- **Refresh Token**: 30 天 (存储在 HttpOnly Cookie)

#### 令牌撤销机制

```python
# JWT黑名单 (Redis存储)
jwt_blacklist = {
    "jwt_id_1": expiry_timestamp,
    "jwt_id_2": expiry_timestamp
}
```

### 4.2 安全最佳实践

#### 存储策略

- **管理员**: localStorage (短期) + HttpOnly Cookie (长期)
- **会员**: localStorage (移动端) / sessionStorage (网页端)

#### 传输安全

- **HTTPS**: 强制使用 HTTPS 传输
- **CORS**: 配置严格的 CORS 策略
- **CSP**: 内容安全策略防止 XSS

#### 防护措施

- **速率限制**: 登录接口限制频次
- **IP 白名单**: 管理员可配置 IP 限制
- **设备绑定**: 可选的设备指纹验证

## 5. 权限控制设计

### 5.1 基于角色的访问控制 (RBAC)

#### 角色定义

```python
class UserRole(str, Enum):
    SUPER_ADMIN = "super_admin"    # 超级管理员
    ADMIN = "admin"                # 租户管理员
    STAFF = "staff"                # 员工

class MemberLevel(str, Enum):
    VIP = "vip"                    # VIP会员
    REGULAR = "regular"            # 普通会员
    TRIAL = "trial"                # 试用会员
```

#### 权限矩阵

```python
PERMISSIONS = {
    "super_admin": ["*"],  # 所有权限
    "admin": [
        "tenant:manage",
        "user:manage",
        "member:manage",
        "course:manage"
    ],
    "staff": [
        "member:view",
        "course:view"
    ]
}
```

### 5.2 装饰器权限控制

```python
@require_auth(user_type="admin", permissions=["member:manage"])
async def create_member(request: Request, member_data: MemberCreate):
    pass

@require_auth(user_type="member", membership_level="vip")
async def book_vip_course(request: Request, course_id: str):
    pass
```

## 6. 实现要点

### 6.1 数据库设计

#### JWT 撤销表 (可选)

```sql
CREATE TABLE jwt_blacklist (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    jti VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID NOT NULL,
    user_type VARCHAR(20) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_jti (jti),
    INDEX idx_expires_at (expires_at)
);
```

### 6.2 环境配置

```python
# JWT配置
JWT_SECRET_KEY = "your-secret-key"
JWT_ALGORITHM = "HS256"
JWT_ACCESS_TOKEN_EXPIRE_HOURS = 2
JWT_REFRESH_TOKEN_EXPIRE_DAYS = 30

# 微信配置
WECHAT_APP_ID = "your-wechat-app-id"
WECHAT_APP_SECRET = "your-wechat-app-secret"
```

## 7. 总结

这套认证架构具有以下特点：

### ✅ 优势

- **简洁高效**: JWT 无状态，性能优秀
- **多端支持**: 支持 Web、移动端、小程序
- **多租户友好**: 天然支持租户隔离
- **扩展性好**: 易于添加新的认证方式
- **安全可靠**: 多层安全防护机制

### 🎯 适用场景

- 中小型 SaaS 系统
- 多租户应用
- 前后端分离架构
- 移动应用后端

### 📈 后续扩展

- OAuth2.0 集成 (Google, Facebook)
- 多因子认证 (MFA)
- 单点登录 (SSO)
- API 密钥认证
