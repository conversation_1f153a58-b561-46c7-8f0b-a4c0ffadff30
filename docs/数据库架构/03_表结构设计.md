# 表结构设计

本文档详细描述所有数据库表的结构设计，基于当前 SQLModel 实现。

## 1. 租户管理表

### 1.1 tenants (租户表)

**表名**: `tenants`  
**用途**: 存储所有租户（外教课机构）的基本信息  
**类型**: 全局表（无 RLS）

| 字段名                  | 类型          | 约束             | 默认值    | 说明                          |
| ----------------------- | ------------- | ---------------- | --------- | ----------------------------- |
| id                      | INTEGER       | PRIMARY KEY      | AUTO      | 租户 ID                       |
| name                    | VARCHAR(100)  | NOT NULL         | -         | 机构名称                      |
| code                    | VARCHAR(50)   | UNIQUE, NOT NULL | -         | 机构代码                      |
| display_name            | VARCHAR(100)  | -                | NULL      | 显示名称                      |
| description             | TEXT          | -                | NULL      | 机构描述                      |
| domain                  | VARCHAR(100)  | UNIQUE           | NULL      | 自定义域名                    |
| subdomain               | VARCHAR(50)   | UNIQUE           | NULL      | 子域名                        |
| logo_url                | VARCHAR(500)  | -                | NULL      | 机构 logo                     |
| favicon_url             | VARCHAR(500)  | -                | NULL      | 网站图标                      |
| contact_name            | VARCHAR(50)   | -                | NULL      | 联系人姓名                    |
| contact_phone           | VARCHAR(20)   | -                | NULL      | 联系电话                      |
| contact_email           | VARCHAR(100)  | -                | NULL      | 联系邮箱                      |
| address                 | TEXT          | -                | NULL      | 机构地址                      |
| plan_type               | VARCHAR(20)   | NOT NULL         | 'trial'   | 套餐类型                      |
| status                  | VARCHAR(20)   | NOT NULL         | 'trial'   | 租户状态                      |
| max_teachers            | INTEGER       | NOT NULL         | 5         | 最大教师数量                  |
| max_members             | INTEGER       | NOT NULL         | 50        | 最大会员数量                  |
| max_storage_gb          | INTEGER       | NOT NULL         | 1         | 最大存储空间(GB)              |
| billing_cycle           | VARCHAR(20)   | NOT NULL         | 'monthly' | 计费周期                      |
| price_per_class         | DECIMAL(10,2) | NOT NULL         | 0.00      | 按课时计费单价                |
| monthly_fee             | DECIMAL(10,2) | NOT NULL         | 0.00      | 月费                          |
| setup_fee               | DECIMAL(10,2) | NOT NULL         | 0.00      | 安装费                        |
| subscription_expires_at | TIMESTAMP     | -                | NULL      | 订阅到期时间                  |
| trial_expires_at        | TIMESTAMP     | -                | NULL      | 试用期结束时间                |
| settings                | JSON          | -                | NULL      | 机构个性化配置                |
| features                | JSON          | -                | NULL      | 功能开关配置                  |
| branding                | JSON          | -                | NULL      | 品牌定制配置                  |
| database_schema         | VARCHAR(50)   | -                | NULL      | 对应的数据库 schema（已弃用） |
| api_key                 | VARCHAR(100)  | UNIQUE           | NULL      | API 访问密钥                  |
| webhook_url             | VARCHAR(500)  | -                | NULL      | 回调地址                      |
| total_users             | INTEGER       | NOT NULL         | 0         | 总用户数                      |
| total_members           | INTEGER       | NOT NULL         | 0         | 总会员数                      |
| total_classes           | INTEGER       | NOT NULL         | 0         | 总课程数                      |
| total_revenue           | DECIMAL(12,2) | NOT NULL         | 0.00      | 总收入                        |
| created_at              | TIMESTAMP     | NOT NULL         | NOW()     | 创建时间                      |
| updated_at              | TIMESTAMP     | -                | NULL      | 更新时间                      |
| created_by              | VARCHAR(50)   | -                | NULL      | 创建者                        |
| last_login_at           | TIMESTAMP     | -                | NULL      | 最后登录时间                  |

**枚举值**:

- `plan_type`: trial, basic, standard, premium, enterprise
- `status`: trial, active, suspended, terminated, expired
- `billing_cycle`: monthly, quarterly, yearly

### 1.2 tenant_plan_templates (套餐模板表)

**表名**: `tenant_plan_templates`  
**用途**: 存储租户套餐模板配置  
**类型**: 全局表（无 RLS）

| 字段名          | 类型          | 约束             | 默认值 | 说明             |
| --------------- | ------------- | ---------------- | ------ | ---------------- |
| id              | INTEGER       | PRIMARY KEY      | AUTO   | 模板 ID          |
| plan_code       | VARCHAR(20)   | UNIQUE, NOT NULL | -      | 套餐代码         |
| plan_name       | VARCHAR(50)   | NOT NULL         | -      | 套餐名称         |
| description     | TEXT          | -                | NULL   | 套餐描述         |
| max_teachers    | INTEGER       | NOT NULL         | -      | 最大教师数       |
| max_members     | INTEGER       | NOT NULL         | -      | 最大会员数       |
| max_storage_gb  | INTEGER       | NOT NULL         | -      | 最大存储空间(GB) |
| monthly_price   | DECIMAL(10,2) | NOT NULL         | -      | 月费             |
| yearly_price    | DECIMAL(10,2) | -                | NULL   | 年费             |
| setup_fee       | DECIMAL(10,2) | NOT NULL         | 0.00   | 安装费           |
| price_per_class | DECIMAL(10,2) | NOT NULL         | 0.00   | 按课时计费       |
| features        | JSON          | -                | NULL   | 功能列表         |
| is_active       | BOOLEAN       | NOT NULL         | TRUE   | 是否激活         |
| sort_order      | INTEGER       | NOT NULL         | 0      | 排序             |
| created_at      | TIMESTAMP     | NOT NULL         | NOW()  | 创建时间         |
| updated_at      | TIMESTAMP     | -                | NULL   | 更新时间         |

## 2. 用户体系表

### 2.1 users (CMS 用户表)

**表名**: `users`  
**用途**: 存储 CMS 用户（租户管理员、代理、教师等）  
**类型**: 租户表（有 RLS，特殊处理 super_admin）

| 字段名                | 类型         | 约束             | 默认值   | 说明                           |
| --------------------- | ------------ | ---------------- | -------- | ------------------------------ |
| id                    | INTEGER      | PRIMARY KEY      | AUTO     | 用户 ID                        |
| tenant_id             | INTEGER      | FK(tenants.id)   | NULL     | 租户 ID（super_admin 为 null） |
| username              | VARCHAR(50)  | UNIQUE, NOT NULL | -        | 用户名                         |
| email                 | VARCHAR(100) | -                | NULL     | 邮箱                           |
| phone                 | VARCHAR(20)  | -                | NULL     | 手机号                         |
| password_hash         | VARCHAR(255) | NOT NULL         | -        | 密码哈希                       |
| real_name             | VARCHAR(50)  | -                | NULL     | 真实姓名                       |
| avatar_url            | VARCHAR(500) | -                | NULL     | 头像 URL                       |
| gender                | VARCHAR(10)  | -                | NULL     | 性别                           |
| birthday              | DATE         | -                | NULL     | 生日                           |
| role                  | VARCHAR(20)  | NOT NULL         | -        | 用户角色                       |
| status                | VARCHAR(20)  | NOT NULL         | 'active' | 用户状态                       |
| last_login_at         | TIMESTAMP    | -                | NULL     | 最后登录时间                   |
| login_count           | INTEGER      | NOT NULL         | 0        | 登录次数                       |
| failed_login_attempts | INTEGER      | NOT NULL         | 0        | 失败登录尝试次数               |
| locked_until          | TIMESTAMP    | -                | NULL     | 锁定至                         |
| created_at            | TIMESTAMP    | NOT NULL         | NOW()    | 创建时间                       |
| updated_at            | TIMESTAMP    | -                | NULL     | 更新时间                       |
| created_by            | INTEGER      | FK(users.id)     | NULL     | 创建者                         |

**约束**:

- `uq_username`: 全局用户名唯一
- `uq_tenant_email`: 租户内邮箱唯一 `(tenant_id, email)`
- `uq_tenant_phone`: 租户内手机号唯一 `(tenant_id, phone)`

**索引**:

- `idx_tenant_users`: `(tenant_id, id)`
- `idx_user_role`: `(tenant_id, role)`
- `idx_user_status`: `(tenant_id, status)`

**枚举值**:

- `role`: super_admin, admin, agent, teacher
- `status`: active, inactive, locked
- `gender`: male, female, other

### 2.2 user_sessions (用户会话表)

**表名**: `user_sessions`  
**用途**: 存储用户登录会话信息  
**类型**: 租户表（通过 users 表关联 RLS）

| 字段名           | 类型         | 约束                   | 默认值 | 说明         |
| ---------------- | ------------ | ---------------------- | ------ | ------------ |
| id               | INTEGER      | PRIMARY KEY            | AUTO   | 会话 ID      |
| user_id          | INTEGER      | FK(users.id), NOT NULL | -      | 用户 ID      |
| session_token    | VARCHAR(255) | UNIQUE, NOT NULL       | -      | 会话令牌     |
| refresh_token    | VARCHAR(255) | UNIQUE                 | NULL   | 刷新令牌     |
| expires_at       | TIMESTAMP    | NOT NULL               | -      | 过期时间     |
| device_type      | VARCHAR(20)  | -                      | NULL   | 设备类型     |
| device_info      | JSON         | -                      | NULL   | 设备信息     |
| ip_address       | VARCHAR(45)  | -                      | NULL   | IP 地址      |
| user_agent       | TEXT         | -                      | NULL   | 用户代理     |
| is_active        | BOOLEAN      | NOT NULL               | TRUE   | 是否激活     |
| last_activity_at | TIMESTAMP    | NOT NULL               | NOW()  | 最后活跃时间 |
| created_at       | TIMESTAMP    | NOT NULL               | NOW()  | 创建时间     |

**索引**:

- `idx_user_sessions`: `(user_id)`
- `idx_session_token`: `(session_token)`
- `idx_session_active`: `(is_active, expires_at)`

### 2.3 members (会员表)

**表名**: `members`  
**用途**: 存储租户的会员（客户）信息  
**类型**: 租户表（有 RLS）

| 字段名            | 类型          | 约束                     | 默认值   | 说明         |
| ----------------- | ------------- | ------------------------ | -------- | ------------ |
| id                | INTEGER       | PRIMARY KEY              | AUTO     | 会员 ID      |
| tenant_id         | INTEGER       | FK(tenants.id), NOT NULL | -        | 租户 ID      |
| name              | VARCHAR(50)   | NOT NULL                 | -        | 姓名         |
| phone             | VARCHAR(20)   | NOT NULL                 | -        | 手机号       |
| email             | VARCHAR(100)  | -                        | NULL     | 邮箱         |
| gender            | VARCHAR(10)   | -                        | NULL     | 性别         |
| birthday          | DATE          | -                        | NULL     | 生日         |
| avatar_url        | VARCHAR(500)  | -                        | NULL     | 头像 URL     |
| wechat_openid     | VARCHAR(100)  | -                        | NULL     | 微信 OpenID  |
| wechat_unionid    | VARCHAR(100)  | -                        | NULL     | 微信 UnionID |
| wechat_nickname   | VARCHAR(100)  | -                        | NULL     | 微信昵称     |
| wechat_avatar     | VARCHAR(500)  | -                        | NULL     | 微信头像     |
| member_type       | VARCHAR(20)   | NOT NULL                 | 'trial'  | 会员类型     |
| member_status     | VARCHAR(20)   | NOT NULL                 | 'active' | 会员状态     |
| source_channel    | VARCHAR(50)   | -                        | NULL     | 来源渠道     |
| sales_id          | INTEGER       | -                        | NULL     | 销售人员 ID  |
| agent_id          | INTEGER       | -                        | NULL     | 代理人员 ID  |
| address           | TEXT          | -                        | NULL     | 地址         |
| city              | VARCHAR(50)   | -                        | NULL     | 城市         |
| province          | VARCHAR(50)   | -                        | NULL     | 省份         |
| country           | VARCHAR(50)   | NOT NULL                 | 'China'  | 国家         |
| postal_code       | VARCHAR(20)   | -                        | NULL     | 邮编         |
| notes             | TEXT          | -                        | NULL     | 备注         |
| tags              | JSON          | NOT NULL                 | []       | 标签         |
| total_classes     | INTEGER       | NOT NULL                 | 0        | 总上课数     |
| completed_classes | INTEGER       | NOT NULL                 | 0        | 完成上课数   |
| cancelled_classes | INTEGER       | NOT NULL                 | 0        | 取消上课数   |
| no_show_classes   | INTEGER       | NOT NULL                 | 0        | 缺席上课数   |
| total_spent       | DECIMAL(10,2) | NOT NULL                 | 0.00     | 总消费金额   |
| avg_rating        | DECIMAL(3,2)  | NOT NULL                 | 0.00     | 平均评分     |
| rating_count      | INTEGER       | NOT NULL                 | 0        | 评价次数     |
| last_class_at     | TIMESTAMP     | -                        | NULL     | 最后上课时间 |
| last_login_at     | TIMESTAMP     | -                        | NULL     | 最后登录时间 |
| created_at        | TIMESTAMP     | NOT NULL                 | NOW()    | 创建时间     |
| updated_at        | TIMESTAMP     | -                        | NULL     | 更新时间     |
| registered_at     | TIMESTAMP     | NOT NULL                 | NOW()    | 注册时间     |
| created_by        | INTEGER       | FK(users.id)             | NULL     | 创建者       |

**约束**:

- `uq_tenant_member_email`: 租户内邮箱唯一 `(tenant_id, email)`
- `uq_wechat_openid`: 微信 OpenID 全局唯一

**索引**:

- `idx_tenant_member_phone`: `(tenant_id, phone)`
- `idx_tenant_members`: `(tenant_id, id)`
- `idx_member_status`: `(tenant_id, member_status)`
- `idx_member_type`: `(tenant_id, member_type)`
- `idx_member_created_by`: `(tenant_id, created_by)`
- `idx_member_sales`: `(tenant_id, sales_id)`
- `idx_member_agent`: `(tenant_id, agent_id)`

**枚举值**:

- `member_type`: trial, formal, vip
- `member_status`: active, silent, frozen, cancelled
- `gender`: male, female, other

## 3. 系统管理表

### 3.1 system_configs (系统配置表)

**表名**: `system_configs`  
**用途**: 存储系统级配置参数  
**类型**: 全局表（无 RLS）

| 字段名      | 类型         | 约束             | 默认值   | 说明     |
| ----------- | ------------ | ---------------- | -------- | -------- |
| id          | INTEGER      | PRIMARY KEY      | AUTO     | 配置 ID  |
| key         | VARCHAR(100) | UNIQUE, NOT NULL | -        | 配置键   |
| value       | TEXT         | -                | NULL     | 配置值   |
| description | TEXT         | -                | NULL     | 配置描述 |
| data_type   | VARCHAR(20)  | NOT NULL         | 'string' | 数据类型 |
| is_active   | BOOLEAN      | NOT NULL         | TRUE     | 是否激活 |
| created_at  | TIMESTAMP    | NOT NULL         | NOW()    | 创建时间 |
| updated_at  | TIMESTAMP    | -                | NULL     | 更新时间 |

### 3.2 system_audit_logs (系统审计日志表)

**表名**: `system_audit_logs`  
**用途**: 记录系统级操作日志  
**类型**: 全局表（无 RLS）

| 字段名        | 类型         | 约束           | 默认值 | 说明     |
| ------------- | ------------ | -------------- | ------ | -------- |
| id            | INTEGER      | PRIMARY KEY    | AUTO   | 日志 ID  |
| tenant_id     | INTEGER      | FK(tenants.id) | NULL   | 租户 ID  |
| user_id       | INTEGER      | -              | NULL   | 用户 ID  |
| action        | VARCHAR(100) | NOT NULL       | -      | 操作动作 |
| resource_type | VARCHAR(50)  | NOT NULL       | -      | 资源类型 |
| resource_id   | VARCHAR(50)  | -              | NULL   | 资源 ID  |
| details       | JSON         | -              | NULL   | 操作详情 |
| ip_address    | VARCHAR(45)  | -              | NULL   | IP 地址  |
| user_agent    | TEXT         | -              | NULL   | 用户代理 |
| status        | VARCHAR(20)  | NOT NULL       | -      | 操作状态 |
| error_message | TEXT         | -              | NULL   | 错误信息 |
| created_at    | TIMESTAMP    | NOT NULL       | NOW()  | 创建时间 |

### 3.3 tenant_usage_stats (租户使用统计表，暂时忽略不实现)

**表名**: `tenant_usage_stats`  
**用途**: 记录租户资源使用统计  
**类型**: 全局表（无 RLS）

| 字段名            | 类型          | 约束                     | 默认值 | 说明             |
| ----------------- | ------------- | ------------------------ | ------ | ---------------- |
| id                | INTEGER       | PRIMARY KEY              | AUTO   | 统计 ID          |
| tenant_id         | INTEGER       | FK(tenants.id), NOT NULL | -      | 租户 ID          |
| stat_date         | DATE          | NOT NULL                 | -      | 统计日期         |
| active_users      | INTEGER       | NOT NULL                 | 0      | 活跃用户数       |
| active_members    | INTEGER       | NOT NULL                 | 0      | 活跃会员数       |
| total_classes     | INTEGER       | NOT NULL                 | 0      | 总课程数         |
| completed_classes | INTEGER       | NOT NULL                 | 0      | 完成课程数       |
| storage_used_mb   | INTEGER       | NOT NULL                 | 0      | 已用存储空间(MB) |
| api_calls         | INTEGER       | NOT NULL                 | 0      | API 调用次数     |
| revenue           | DECIMAL(10,2) | NOT NULL                 | 0.00   | 当日收入         |
| created_at        | TIMESTAMP     | NOT NULL                 | NOW()  | 创建时间         |

**约束**:

- `uq_tenant_stat_date`: 租户每日统计唯一 `(tenant_id, stat_date)`

## 4. 审计字段说明

### 标准审计字段

所有业务表都包含以下审计字段：

| 字段名     | 类型      | 说明                                   |
| ---------- | --------- | -------------------------------------- |
| created_at | TIMESTAMP | 创建时间，默认当前时间                 |
| updated_at | TIMESTAMP | 更新时间，可为空                       |
| created_by | INTEGER   | 创建者 ID，外键关联 users,members 表等 |

### 软删除字段（可选）

部分表可能需要软删除功能：

| 字段名     | 类型      | 说明                 |
| ---------- | --------- | -------------------- |
| is_deleted | BOOLEAN   | 是否删除，默认 false |
| deleted_at | TIMESTAMP | 删除时间             |
| deleted_by | INTEGER   | 删除者 ID            |

## 5. 数据类型约定

### 字符串类型

- `VARCHAR(50)`: 短字符串（姓名、代码等）
- `VARCHAR(100)`: 中等字符串（邮箱、URL 等）
- `VARCHAR(500)`: 长字符串（URL、描述等）
- `TEXT`: 超长文本（备注、内容等）

### 数值类型

- `INTEGER`: 整数（ID、计数等）
- `DECIMAL(10,2)`: 金额（精确到分）
- `DECIMAL(12,2)`: 大金额（总收入等）

### 时间类型

- `TIMESTAMP`: 包含时区的时间戳
- `DATE`: 日期（生日、统计日期等）

### JSON 类型

- 用于存储配置、设置、标签等结构化数据
- 支持 PostgreSQL 原生 JSON 操作

---

**设计原则**:

1. **一致性**: 相同功能的字段使用相同的命名和类型
2. **扩展性**: 预留扩展字段，使用 JSON 存储灵活配置
3. **性能**: 合理的索引设计，避免冗余索引
4. **安全性**: 敏感信息加密存储，审计日志完整
5. **可维护性**: 清晰的约束和注释，便于理解和维护
