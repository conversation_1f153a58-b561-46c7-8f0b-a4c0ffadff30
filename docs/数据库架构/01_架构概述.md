# 数据库架构概述

## 总体架构

本项目采用 **共享数据库、共享 Schema + tenant_id** 多租户架构，结合 PostgreSQL 的行级安全策略 (RLS) 实现数据隔离。

### 技术栈

- **数据库**: PostgreSQL 12+
- **ORM**: SQLModel (SQLAlchemy + Pydantic)
- **Web 框架**: FastAPI
- **认证**: JWT
- **多租户**: 共享 Schema + tenant_id + RLS

## 架构特点

### 1. 多租户策略

```
┌─────────────────────────────────────────────────────────┐
│                    PostgreSQL Database                  │
│                                                         │
│  ┌─────────────────────────────────────────────────────┐ │
│  │                Public Schema                        │ │
│  │                                                     │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │ │
│  │  │   tenants   │ │ plan_temp   │ │system_config│   │ │
│  │  │    (租户)    │ │  (套餐模板)  │ │  (系统配置)  │   │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘   │ │
│  │                                                     │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │ │
│  │  │    users    │ │   members   │ │user_sessions│   │ │
│  │  │(CMS管理员)   │ │  (租户会员)  │ │  (用户会话)  │   │ │
│  │  │tenant_id    │ │ tenant_id   │ │    RLS      │   │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘   │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 2. 数据分类

#### 全局数据（无 tenant_id）

- `tenants` - 租户信息
- `tenant_plan_templates` - 套餐模板
- `system_configs` - 系统配置
- `system_admins` - 系统管理员
- `system_audit_logs` - 系统审计日志

#### 租户数据（有 tenant_id + RLS）

- `users` - CMS 用户（租户管理员、代理、教师等）
- `members` - 会员（租户的客户）
- `user_sessions` - 用户会话（通过 users 表关联）

### 3. 数据隔离机制

#### RLS (Row Level Security)

```sql
-- 用户表RLS策略（特殊处理super_admin）
CREATE POLICY users_tenant_isolation ON users
FOR ALL TO PUBLIC
USING (
    tenant_id IS NULL OR  -- super_admin可以访问
    tenant_id = current_setting('app.current_tenant_id')::int
);

-- 会员表RLS策略
CREATE POLICY members_tenant_isolation ON members
FOR ALL TO PUBLIC
USING (
    tenant_id = current_setting('app.current_tenant_id')::int
);
```

#### 上下文设置

```python
# 设置租户上下文
session.execute(text(f"SET app.current_tenant_id = '{tenant_id}'"))

# 清除上下文（超级管理员）
session.execute(text("RESET app.current_tenant_id"))
```

## 表结构分类

### 1. 租户管理表

- **tenants**: 租户基本信息
- **tenant_plan_templates**: 套餐模板

### 2. 用户体系表

- **users**: CMS 用户（管理员、代理、教师）
- **user_sessions**: 用户会话管理
- **members**: 会员（租户客户）

### 3. 系统管理表

- **system_configs**: 系统配置
- **system_admins**: 系统管理员
- **system_audit_logs**: 系统审计日志
- **tenant_usage_stats**: 租户使用统计

## 身份认证体系

### 1. 用户类型

```python
class UserRole(str, Enum):
    SUPER_ADMIN = "super_admin"  # 超级管理员（跨租户）
    ADMIN = "admin"              # 租户管理员
    AGENT = "agent"              # 代理人员
    TEACHER = "teacher"          # 教师
```

### 2. 认证流程

```
1. 用户登录 → 验证用户名密码
2. 生成JWT Token → 包含用户信息和租户信息
3. 设置RLS上下文 → 根据租户ID设置数据访问范围
4. API访问 → 自动应用数据隔离
```

### 3. 权限控制

- **超级管理员**: 可以管理所有租户，tenant_id 为 null
- **租户管理员**: 只能管理自己租户的数据
- **普通用户**: 根据角色控制功能权限

## 数据库连接管理

### 1. 连接池配置

```python
engine = create_engine(
    settings.DATABASE_URL,
    pool_size=20,
    max_overflow=30,
    pool_recycle=3600,
    connect_args={"options": "-c timezone=Asia/Shanghai"}  # 使用东八区
)
```

### 2. 会话管理

```python
# 普通会话
def get_session() -> Session

# 租户会话（设置RLS上下文）
def get_tenant_session(tenant_id: int) -> Session

# 全局会话（清除RLS上下文）
def get_global_session() -> Session
```

## 性能优化

### 1. 索引策略

- 每个租户数据表都有 `(tenant_id, id)` 复合索引
- 业务查询字段索引：`(tenant_id, status)`, `(tenant_id, role)` 等
- 唯一约束：`(tenant_id, email)`, `(tenant_id, phone)` 等

### 2. 查询优化

- RLS 自动添加 tenant_id 过滤条件
- 避免跨租户查询
- 合理使用连接池

## 扩展性考虑

### 1. 垂直扩展

- 支持增加新的租户业务表
- 统一的审计字段和 RLS 策略
- 灵活的权限控制体系

### 2. 水平扩展

- 当前单库架构，支持读写分离
- 可扩展为分库分表架构
- 支持缓存层扩展

## 安全性

### 1. 数据隔离

- RLS 强制数据隔离，即使对表所有者也生效
- 防止租户间数据泄露
- SQL 注入防护

### 2. 访问控制

- JWT Token 认证
- 角色权限控制
- API 访问日志

---

**优势**:

- 架构简单，易于理解和维护
- 数据隔离安全可靠
- 支持灵活的业务扩展
- 开发效率高

**限制**:

- 单一数据库实例，有性能上限
- 大租户可能影响其他租户性能
- 数据库层面的资源共享
