"""
调试RLS问题的真正原因
"""
import os
import sys
sys.path.append('.')

from sqlmodel import Session, create_engine, text
from app.core.config import settings

def check_rls_in_both_databases():
    """检查生产数据库和测试数据库中的RLS状态"""
    
    # 生产数据库
    prod_engine = create_engine(settings.DATABASE_URL)
    
    # 测试数据库
    test_db_url = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
    
    print("🔍 检查生产数据库中的RLS状态:")
    try:
        with Session(prod_engine) as session:
            # 检查users表的RLS
            users_rls = session.exec(text("""
                SELECT relrowsecurity FROM pg_class WHERE relname = 'users'
            """)).first()
            
            users_policies = session.exec(text("""
                SELECT policyname FROM pg_policies WHERE tablename = 'users'
            """)).all()
            
            # 检查member_fixed_slot_locks表的RLS
            locks_rls = session.exec(text("""
                SELECT relrowsecurity FROM pg_class WHERE relname = 'member_fixed_slot_locks'
            """)).first()
            
            locks_policies = session.exec(text("""
                SELECT policyname FROM pg_policies WHERE tablename = 'member_fixed_slot_locks'
            """)).all()
            
            print(f"  users表 RLS启用: {users_rls}")
            print(f"  users表 策略: {[p[0] for p in users_policies]}")
            print(f"  member_fixed_slot_locks表 RLS启用: {locks_rls}")
            print(f"  member_fixed_slot_locks表 策略: {[p[0] for p in locks_policies]}")
            
    except Exception as e:
        print(f"  生产数据库检查失败: {e}")
    
    print("\n🔍 模拟测试流程:")
    try:
        # 先创建测试数据库
        default_db_url = test_db_url.rsplit('/', 1)[0] + '/postgres'
        temp_engine = create_engine(default_db_url)
        with temp_engine.connect() as conn:
            conn.execute(text("COMMIT"))
            conn.execute(text("DROP DATABASE IF EXISTS course_booking_test_db"))
            conn.execute(text("CREATE DATABASE course_booking_test_db"))
        temp_engine.dispose()
        
        # 创建测试引擎
        test_engine = create_engine(test_db_url)
        
        # 模拟第一次初始化（test_engine fixture）
        print("  1. 第一次初始化（test_engine fixture）:")
        
        # 临时替换引擎
        import app.db.session
        original_engine = app.db.session.engine
        app.db.session.engine = test_engine
        
        try:
            from app.db.base import create_db_and_tables
            create_db_and_tables()
            print("    ✅ 第一次初始化成功")
        except Exception as e:
            print(f"    ❌ 第一次初始化失败: {e}")
        finally:
            app.db.session.engine = original_engine
        
        # 检查测试数据库中的RLS状态
        with Session(test_engine) as session:
            locks_rls = session.exec(text("""
                SELECT relrowsecurity FROM pg_class WHERE relname = 'member_fixed_slot_locks'
            """)).first()
            
            locks_policies = session.exec(text("""
                SELECT policyname FROM pg_policies WHERE tablename = 'member_fixed_slot_locks'
            """)).all()
            
            print(f"    测试数据库 - member_fixed_slot_locks表 RLS启用: {locks_rls}")
            print(f"    测试数据库 - member_fixed_slot_locks表 策略: {[p[0] for p in locks_policies]}")
        
        # 模拟第二次初始化（FastAPI lifespan）
        print("\n  2. 第二次初始化（FastAPI lifespan）:")
        try:
            from app.db.base import create_db_and_tables
            create_db_and_tables()  # 这次使用生产引擎
            print("    ✅ 第二次初始化成功")
        except Exception as e:
            print(f"    ❌ 第二次初始化失败: {e}")
        
        # 再次检查测试数据库中的RLS状态
        print("\n  3. 第二次初始化后的测试数据库状态:")
        with Session(test_engine) as session:
            locks_rls = session.exec(text("""
                SELECT relrowsecurity FROM pg_class WHERE relname = 'member_fixed_slot_locks'
            """)).first()
            
            locks_policies = session.exec(text("""
                SELECT policyname FROM pg_policies WHERE tablename = 'member_fixed_slot_locks'
            """)).all()
            
            print(f"    测试数据库 - member_fixed_slot_locks表 RLS启用: {locks_rls}")
            print(f"    测试数据库 - member_fixed_slot_locks表 策略: {[p[0] for p in locks_policies]}")
        
        test_engine.dispose()
        
    except Exception as e:
        print(f"  测试流程失败: {e}")

if __name__ == "__main__":
    check_rls_in_both_databases()
