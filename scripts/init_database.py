"""
数据库初始化脚本
运行此脚本来创建全局Schema和初始化数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.base import create_db_and_tables, init_global_data
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    """主函数"""
    try:
        logger.info("Starting database initialization...")

        # 创建表结构
        create_db_and_tables()

        # 初始化数据
        init_global_data()

        logger.info("Database initialization completed successfully!")

    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


if __name__ == "__main__":
    main() 