#!/usr/bin/env python3
"""
调试RLS策略脚本

这个脚本用于调试RLS策略的实际行为，帮助理解为什么租户隔离不工作。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlmodel import Session, text, select
from app.db.session import engine
from app.features.users.models import User, UserRole, UserStatus
from app.features.tenants.models import Tenant
from app.utils.security import get_password_hash
import uuid
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_rls_behavior():
    """测试RLS策略的实际行为"""
    
    with Session(engine) as session:
        # 1. 清理环境
        print("🧹 清理测试环境...")
        session.exec(text("DELETE FROM users WHERE username LIKE 'test_%'"))
        session.exec(text("DELETE FROM tenants WHERE name LIKE '测试租户%'"))
        session.commit()
        
        # 2. 创建测试租户
        print("🏢 创建测试租户...")
        tenant1 = Tenant(name="测试租户1", code=f"test1_{uuid.uuid4().hex[:8]}")
        tenant2 = Tenant(name="测试租户2", code=f"test2_{uuid.uuid4().hex[:8]}")
        
        session.add(tenant1)
        session.add(tenant2)
        session.commit()
        session.refresh(tenant1)
        session.refresh(tenant2)
        
        print(f"租户1 ID: {tenant1.id}")
        print(f"租户2 ID: {tenant2.id}")
        
        # 3. 创建测试用户
        print("👤 创建测试用户...")
        user1 = User(
            username=f"test_user1_{uuid.uuid4().hex[:8]}",
            email=f"test1_{uuid.uuid4().hex[:8]}@test.com",
            real_name="测试用户1",
            password_hash=get_password_hash("password123"),
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=tenant1.id
        )
        
        user2 = User(
            username=f"test_user2_{uuid.uuid4().hex[:8]}",
            email=f"test2_{uuid.uuid4().hex[:8]}@test.com",
            real_name="测试用户2",
            password_hash=get_password_hash("password123"),
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=tenant2.id
        )
        
        session.add(user1)
        session.add(user2)
        session.commit()
        session.refresh(user1)
        session.refresh(user2)
        
        print(f"用户1 ID: {user1.id}, 租户ID: {user1.tenant_id}")
        print(f"用户2 ID: {user2.id}, 租户ID: {user2.tenant_id}")
        
        # 4. 测试不同的查询方式
        print("\n" + "="*50)
        print("🔍 测试RLS策略行为")
        print("="*50)
        
        # 4.1 全局查询（无租户上下文）
        print("\n📋 测试1: 全局查询（无租户上下文）")
        session.exec(text("RESET app.current_tenant_id"))
        
        # 使用普通查询
        all_users_query = session.exec(select(User)).all()
        print(f"普通查询看到的用户数: {len(all_users_query)}")
        
        # 使用原生SQL
        all_users_sql = session.exec(text("SELECT id, username, tenant_id FROM users WHERE username LIKE 'test_%'")).all()
        print(f"原生SQL看到的用户数: {len(all_users_sql)}")
        for user in all_users_sql:
            print(f"  - ID: {user[0]}, 用户名: {user[1]}, 租户ID: {user[2]}")
        
        # 4.2 设置租户1上下文
        print(f"\n📋 测试2: 设置租户1上下文 (ID: {tenant1.id})")
        session.exec(text(f"SET app.current_tenant_id = '{tenant1.id}'"))
        
        # 检查当前设置
        current_setting = session.exec(text("SELECT current_setting('app.current_tenant_id', true)")).first()
        print(f"当前租户上下文: {current_setting}")
        
        # 使用普通查询
        tenant1_users_query = session.exec(select(User)).all()
        print(f"普通查询看到的用户数: {len(tenant1_users_query)}")
        for user in tenant1_users_query:
            print(f"  - ID: {user.id}, 用户名: {user.username}, 租户ID: {user.tenant_id}")
        
        # 使用原生SQL
        tenant1_users_sql = session.exec(text("SELECT id, username, tenant_id FROM users")).all()
        print(f"原生SQL看到的用户数: {len(tenant1_users_sql)}")
        for user in tenant1_users_sql:
            print(f"  - ID: {user[0]}, 用户名: {user[1]}, 租户ID: {user[2]}")
        
        # 使用session.get()方法测试
        print(f"\n🔍 测试session.get()方法:")
        user2_from_tenant1_context = session.get(User, user2.id)
        if user2_from_tenant1_context:
            print(f"  - 在租户1上下文中能看到用户2: ID={user2_from_tenant1_context.id}, 租户ID={user2_from_tenant1_context.tenant_id}")
        else:
            print(f"  - 在租户1上下文中看不到用户2 ✅")
        
        # 4.3 设置租户2上下文
        print(f"\n📋 测试3: 设置租户2上下文 (ID: {tenant2.id})")
        session.exec(text(f"SET app.current_tenant_id = '{tenant2.id}'"))
        
        # 检查当前设置
        current_setting = session.exec(text("SELECT current_setting('app.current_tenant_id', true)")).first()
        print(f"当前租户上下文: {current_setting}")
        
        # 使用普通查询
        tenant2_users_query = session.exec(select(User)).all()
        print(f"普通查询看到的用户数: {len(tenant2_users_query)}")
        for user in tenant2_users_query:
            print(f"  - ID: {user.id}, 用户名: {user.username}, 租户ID: {user.tenant_id}")
        
        # 使用session.get()方法测试
        print(f"\n🔍 测试session.get()方法:")
        user1_from_tenant2_context = session.get(User, user1.id)
        if user1_from_tenant2_context:
            print(f"  - 在租户2上下文中能看到用户1: ID={user1_from_tenant2_context.id}, 租户ID={user1_from_tenant2_context.tenant_id}")
        else:
            print(f"  - 在租户2上下文中看不到用户1 ✅")
        
        # 5. 查看RLS策略
        print(f"\n📋 测试4: 查看当前RLS策略")
        policies = session.exec(text("""
            SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
            FROM pg_policies 
            WHERE tablename = 'users'
        """)).all()
        
        for policy in policies:
            print(f"策略: {policy[2]}")
            print(f"  表: {policy[1]}")
            print(f"  条件: {policy[5]}")
            print(f"  USING: {policy[6]}")
            print(f"  WITH CHECK: {policy[7]}")
        
        # 6. 清理
        print(f"\n🧹 清理测试数据...")
        session.exec(text("RESET app.current_tenant_id"))
        session.exec(text("DELETE FROM users WHERE username LIKE 'test_%'"))
        session.exec(text("DELETE FROM tenants WHERE name LIKE '测试租户%'"))
        session.commit()
        
        print("✅ 测试完成")

def main():
    """主函数"""
    print("🔧 开始调试RLS策略...")
    print("=" * 60)
    
    try:
        test_rls_behavior()
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 