#!/usr/bin/env python3
"""
测试运行脚本
提供统一的测试执行入口，支持不同数据库和测试类型
"""
import subprocess
import sys
import os
from pathlib import Path
from typing import List, Optional


def setup_test_env():
    """设置测试环境变量"""
    os.environ["SECRET_KEY"] = "test-secret-key"
    os.environ["DEBUG"] = "True"
    os.environ["DATABASE_URL"] = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
        
    print(f"🔧 已配置PostgreSQL测试环境")


def run_command(cmd: list, description: str) -> bool:
    """运行命令并处理结果"""
    print(f"\n🔍 {description}...")
    print(f"执行命令: {' '.join(cmd)}")
    print("-" * 50)
    
    result = subprocess.run(cmd, capture_output=False)
    
    if result.returncode == 0:
        print(f"✅ {description} 成功完成")
        return True
    else:
        print(f"❌ {description} 失败")
        return False


def build_pytest_command(
    test_type: str,
    db_type: str,
    extra_args: Optional[List[str]] = None
) -> List[str]:
    """构建pytest命令"""
    cmd = ["python", "-m", "pytest", "-v", "--tb=short"]
    
    # 根据新的测试目录结构添加测试路径
    if test_type == "unit":
        cmd.extend(["tests/unit/"])
    elif test_type == "api":
        cmd.extend(["tests/integration/api/"])
    elif test_type == "integration":
        cmd.extend(["tests/integration/"])
    elif test_type == "e2e":
        cmd.extend(["tests/e2e/"])
    elif test_type == "manual":
        cmd.extend(["tests/e2e/scenarios/", "-s"])  # 手动测试需要显示输出
    elif test_type == "quick":
        cmd.extend(["-m", "not slow", "tests/"])
    elif test_type == "coverage":
        cmd.extend(["--cov=app", "--cov-report=html", "--cov-report=term", "tests/"])
    elif test_type == "all":
        cmd.extend(["tests/"])
    
    # 添加额外参数
    if extra_args:
        cmd.extend(extra_args)
    
    return cmd


def print_help():
    """显示帮助信息"""
    print("""
🔧 测试脚本使用说明:

python scripts/test.py [test_type] [--db=<db_type>] [extra_args...]

test_type 选项:
  all        - 运行所有测试 (默认)
  unit       - 只运行单元测试 (tests/unit/)
  api        - 只运行API集成测试 (tests/integration/api/)
  integration- 只运行集成测试 (tests/integration/)
  e2e        - 只运行端到端测试 (tests/e2e/)
  manual     - 运行手动测试场景 (tests/e2e/scenarios/)
  coverage   - 运行测试并生成覆盖率报告
  quick      - 快速测试（跳过慢速测试）
  help       - 显示此帮助信息

db_type 选项:
  --db=postgres - 使用PostgreSQL数据库 (默认)

extra_args:
  传递给pytest的其他参数，例如 -k "test_name"

示例:
  python scripts/test.py unit                    # 运行单元测试
  python scripts/test.py api                     # 运行API测试
  python scripts/test.py coverage                # 运行覆盖率测试
  python scripts/test.py manual                  # 运行手动测试场景
  python scripts/test.py all -k "test_create"    # 运行特定测试

测试目录结构:
  tests/unit/           - 单元测试 (services, models, utils)
  tests/integration/    - 集成测试 (API, database)
  tests/e2e/           - 端到端测试 (scenarios, performance)
    """)


def parse_args():
    """解析命令行参数"""
    args = sys.argv[1:]
    test_type = "all"
    db_type = "postgres"
    extra_args = []
    
    if args and args[0] in ["all", "unit", "api", "integration", "e2e", "manual", "coverage", "quick", "help"]:
        test_type = args.pop(0)
    
    if args and args[0].startswith("--db="):
        db_type = args.pop(0).split("=")[1]
        if db_type not in ["postgres"]:
            print(f"❌ 不支持的数据库类型: {db_type}")
            sys.exit(1)
    
    extra_args = args
    return test_type, db_type, extra_args


def main():
    """主函数"""
    # 确保在项目根目录
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    # 解析参数
    test_type, db_type, extra_args = parse_args()
    
    if test_type == "help":
        print_help()
        return
    
    print(f"🚀 开始运行测试套件... (数据库: {db_type})")
    
    # 设置环境
    setup_test_env()
    
    # 构建并运行测试命令
    cmd = build_pytest_command(test_type, db_type, extra_args)
    success = run_command(cmd, f"{test_type}测试")
    
    print("\n" + "="*60)
    if success:
        print("🎉 所有测试完成！")
    else:
        print("💥 部分测试失败，请检查上面的错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main() 