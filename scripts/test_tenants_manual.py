#!/usr/bin/env python3
"""
租户API手动测试脚本
用于快速验证租户相关功能
"""
import requests
import json
import time
from datetime import datetime


BASE_URL = "http://localhost:8012"
API_BASE = f"{BASE_URL}/api/v1"


def print_section(title: str):
    """打印测试章节标题"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)


def print_response(response: requests.Response, description: str):
    """打印响应信息"""
    print(f"\n📝 {description}")
    print(f"状态码: {response.status_code}")
    
    try:
        data = response.json()
        print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    except:
        print(f"响应内容: {response.text}")
    
    if response.status_code >= 400:
        print("❌ 请求失败")
    else:
        print("✅ 请求成功")


def test_health_check():
    """测试健康检查"""
    print_section("健康检查")
    
    response = requests.get(f"{BASE_URL}/health")
    print_response(response, "健康检查")
    
    return response.status_code == 200


def test_create_tenant():
    """测试创建租户"""
    print_section("创建租户")
    
    tenant_data = {
        "name": "测试教育机构",
        "code": f"test_org_{int(time.time())}",  # 使用时间戳避免重复
        "contact_name": "张三",
        "contact_phone": "13800138000",
        "contact_email": "<EMAIL>",
        "address": "北京市朝阳区测试街道123号",
        "description": "这是一个测试教育机构"
    }
    
    response = requests.post(f"{API_BASE}/tenants/", json=tenant_data)
    print_response(response, "创建租户")
    
    if response.status_code == 201:
        return response.json()
    return None


def test_get_tenants():
    """测试获取租户列表"""
    print_section("获取租户列表")
    
    response = requests.get(f"{API_BASE}/tenants/")
    print_response(response, "获取租户列表")
    
    if response.status_code == 200:
        tenants = response.json()
        print(f"\n📊 共找到 {len(tenants)} 个租户")
        return tenants
    return []


def test_get_tenant_by_id(tenant_id: int):
    """测试根据ID获取租户"""
    print_section(f"获取租户详情 (ID: {tenant_id})")
    
    response = requests.get(f"{API_BASE}/tenants/{tenant_id}")
    print_response(response, f"获取租户 {tenant_id}")
    
    return response.status_code == 200


def test_get_tenant_by_code(tenant_code: str):
    """测试根据代码获取租户"""
    print_section(f"根据代码获取租户 (Code: {tenant_code})")
    
    response = requests.get(f"{API_BASE}/tenants/code/{tenant_code}")
    print_response(response, f"获取租户 {tenant_code}")
    
    return response.status_code == 200


def test_update_tenant(tenant_id: int):
    """测试更新租户"""
    print_section(f"更新租户 (ID: {tenant_id})")
    
    update_data = {
        "name": "更新后的教育机构",
        "contact_name": "李四",
        "contact_phone": "13900139000",
        "description": "这是更新后的描述信息"
    }
    
    response = requests.post(f"{API_BASE}/tenants/{tenant_id}", json=update_data)
    print_response(response, f"更新租户 {tenant_id}")
    
    return response.status_code == 200


def test_get_plan_templates():
    """测试获取套餐模板"""
    print_section("获取套餐模板")
    
    response = requests.get(f"{API_BASE}/tenants/plans/templates")
    print_response(response, "获取套餐模板")
    
    if response.status_code == 200:
        templates = response.json()
        print(f"\n📊 共找到 {len(templates)} 个套餐模板")
        return templates
    return []


def test_apply_plan_template(tenant_id: int, plan_code: str):
    """测试应用套餐模板"""
    print_section(f"应用套餐模板 (租户: {tenant_id}, 套餐: {plan_code})")
    
    response = requests.post(f"{API_BASE}/tenants/{tenant_id}/apply-plan/{plan_code}")
    print_response(response, f"应用套餐 {plan_code}")
    
    return response.status_code == 200


def test_tenant_status_operations(tenant_id: int):
    """测试租户状态操作"""
    print_section(f"租户状态操作 (ID: {tenant_id})")
    
    # 激活租户
    print("\n🔄 激活租户...")
    response = requests.post(f"{API_BASE}/tenants/{tenant_id}/activate")
    print_response(response, "激活租户")
    
    time.sleep(1)
    
    # 暂停租户
    print("\n🔄 暂停租户...")
    response = requests.post(f"{API_BASE}/tenants/{tenant_id}/suspend?reason=测试暂停")
    print_response(response, "暂停租户")
    
    time.sleep(1)
    
    # 重新激活
    print("\n🔄 重新激活租户...")
    response = requests.post(f"{API_BASE}/tenants/{tenant_id}/activate")
    print_response(response, "重新激活租户")


def test_delete_tenant(tenant_id: int):
    """测试删除租户"""
    print_section(f"删除租户 (ID: {tenant_id})")
    
    response = requests.post(f"{API_BASE}/tenants/{tenant_id}/delete")
    print_response(response, f"删除租户 {tenant_id}")
    
    return response.status_code == 200


def test_error_cases():
    """测试错误情况"""
    print_section("错误情况测试")
    
    # 获取不存在的租户
    print("\n🔍 获取不存在的租户...")
    response = requests.get(f"{API_BASE}/tenants/99999")
    print_response(response, "获取不存在的租户")
    
    # 创建无效数据的租户
    print("\n🔍 创建无效数据的租户...")
    invalid_data = {
        "name": "",  # 空名称
        "code": "test",
        "contact_email": "invalid-email"
    }
    response = requests.post(f"{API_BASE}/tenants/", json=invalid_data)
    print_response(response, "创建无效数据的租户")


def main():
    """主测试流程"""
    print("🚀 开始租户API手动测试...")
    print(f"🔗 API地址: {API_BASE}")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 健康检查
        if not test_health_check():
            print("❌ 健康检查失败，请确保服务正在运行")
            return
        
        # 2. 创建租户
        tenant = test_create_tenant()
        if not tenant:
            print("❌ 创建租户失败，停止测试")
            return
        
        tenant_id = tenant["id"]
        tenant_code = tenant["code"]
        
        # 3. 获取租户列表
        test_get_tenants()
        
        # 4. 获取租户详情
        test_get_tenant_by_id(tenant_id)
        test_get_tenant_by_code(tenant_code)
        
        # 5. 更新租户
        test_update_tenant(tenant_id)
        
        # 6. 套餐模板相关
        templates = test_get_plan_templates()
        if templates:
            # 应用第一个套餐模板
            plan_code = templates[0]["plan_code"]
            test_apply_plan_template(tenant_id, plan_code)
        
        # 7. 租户状态操作
        test_tenant_status_operations(tenant_id)
        
        # 8. 错误情况测试
        test_error_cases()
        
        # 9. 清理：删除测试租户
        test_delete_tenant(tenant_id)
        
        print_section("测试完成")
        print("🎉 所有测试完成！")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保应用正在运行")
        print(f"   检查服务是否在 {BASE_URL} 上运行")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main() 