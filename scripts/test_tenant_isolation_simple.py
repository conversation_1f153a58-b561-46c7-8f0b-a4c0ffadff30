#!/usr/bin/env python3
"""
简单租户隔离测试运行脚本

这个脚本专门用于快速运行基本的租户隔离测试，
验证多租户系统的数据隔离机制是否正常工作。
"""

import sys
import subprocess
import os
from pathlib import Path

def run_simple_tenant_isolation_tests():
    """运行简单的租户隔离测试"""
    
    # 确保在项目根目录
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    print("🚀 开始运行简单租户隔离测试...")
    print("=" * 60)
    
    # 测试命令
    test_commands = [
        {
            "name": "基本用户隔离测试",
            "cmd": ["python", "-m", "pytest", 
                   "tests/integration/test_tenant_isolation_simple.py::TestTenantIsolationSimple::test_basic_user_isolation",
                   "-v", "-s", "--tb=short"]
        },
        {
            "name": "会员数据隔离测试", 
            "cmd": ["python", "-m", "pytest",
                   "tests/integration/test_tenant_isolation_simple.py::TestTenantIsolationSimple::test_member_data_isolation",
                   "-v", "-s", "--tb=short"]
        },
        {
            "name": "跨租户直接访问阻止测试",
            "cmd": ["python", "-m", "pytest",
                   "tests/integration/test_tenant_isolation_simple.py::TestTenantIsolationSimple::test_cross_tenant_direct_access_blocked",
                   "-v", "-s", "--tb=short"]
        },
        {
            "name": "超级管理员全局访问测试",
            "cmd": ["python", "-m", "pytest",
                   "tests/integration/test_tenant_isolation_simple.py::TestTenantIsolationSimple::test_super_admin_global_access",
                   "-v", "-s", "--tb=short"]
        },
        {
            "name": "RLS策略存在性检查",
            "cmd": ["python", "-m", "pytest",
                   "tests/integration/test_tenant_isolation_simple.py::TestTenantIsolationSimple::test_rls_policies_exist",
                   "-v", "-s", "--tb=short"]
        },
        {
            "name": "空租户上下文行为测试",
            "cmd": ["python", "-m", "pytest",
                   "tests/integration/test_tenant_isolation_simple.py::TestTenantIsolationSimple::test_empty_tenant_context_behavior",
                   "-v", "-s", "--tb=short"]
        },
        {
            "name": "会员固定课位锁定隔离测试",
            "cmd": ["python", "-m", "pytest",
                   "tests/integration/test_tenant_isolation_simple.py::TestTenantIsolationSimple::test_member_fixed_slot_locks_isolation",
                   "-v", "-s", "--tb=short"]
        }
    ]
    
    # 运行测试结果统计
    total_tests = len(test_commands)
    passed_tests = 0
    failed_tests = []
    
    for i, test in enumerate(test_commands, 1):
        print(f"\n📋 [{i}/{total_tests}] 运行: {test['name']}")
        print("-" * 40)
        
        try:
            result = subprocess.run(
                test["cmd"],
                capture_output=False,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0:
                print(f"✅ {test['name']} - 通过")
                passed_tests += 1
            else:
                print(f"❌ {test['name']} - 失败 (退出码: {result.returncode})")
                failed_tests.append(test['name'])
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {test['name']} - 超时")
            failed_tests.append(test['name'])
        except Exception as e:
            print(f"💥 {test['name']} - 异常: {e}")
            failed_tests.append(test['name'])
    
    # 输出测试结果汇总
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"通过: {passed_tests}")
    print(f"失败: {len(failed_tests)}")
    
    if failed_tests:
        print(f"\n❌ 失败的测试:")
        for test_name in failed_tests:
            print(f"  - {test_name}")
        print(f"\n🚨 租户隔离测试失败！请检查多租户配置。")
        return False
    else:
        print(f"\n🎉 所有租户隔离测试都通过了！")
        print(f"✅ 多租户数据隔离机制正常工作。")
        return True

def run_all_simple_tests():
    """运行所有简单测试"""
    print("🚀 运行所有简单租户隔离测试...")
    print("=" * 60)
    
    cmd = [
        "python", "-m", "pytest",
        "tests/integration/test_tenant_isolation_simple.py",
        "-v", "-s", "--tb=short"
    ]
    
    try:
        result = subprocess.run(cmd, timeout=600)  # 10分钟超时
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时")
        return False
    except Exception as e:
        print(f"💥 测试异常: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "all":
        success = run_all_simple_tests()
    else:
        success = run_simple_tenant_isolation_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main() 