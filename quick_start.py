#!/usr/bin/env python3
"""
快速启动脚本 - 使用PostgreSQL数据库
适合开发环境快速测试，使用PostgreSQL数据库
"""
import os
import uvicorn


def setup_db_env():
    """设置PostgreSQL环境变量"""
    os.environ["DATABASE_URL"] = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
    os.environ["SECRET_KEY"] = "dev-secret-key-change-in-production"
    os.environ["DEBUG"] = "True"
    print("🔧 已配置PostgreSQL数据库环境")


def main():
    """主函数"""
    setup_db_env()
    
    print("\n🚀 启动开发服务器...")
    print("=" * 50)
    print("📖 API文档: http://localhost:8012/docs")
    print("🔍 健康检查: http://localhost:8012/health")
    print("=" * 50)
    
    # 启动应用
    uvicorn.run(
        "app.main:app",
        host="127.0.0.1",
        port=8012,
        reload=True
    )


if __name__ == "__main__":
    main() 