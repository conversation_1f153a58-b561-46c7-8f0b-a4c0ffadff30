from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from app.core.config import settings
from app.core.logging import setup_logging, get_logger
from app.db.base import create_db_and_tables, init_global_data
from app.api.v1.api import api_router
from app.api.common.exceptions import (
    APIException,
    api_exception_handler,
    validation_exception_handler,
    general_exception_handler
)
from app.api.common.docs import get_openapi_config


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    print("🚀 FastAPI应用启动中...")

    # 初始化日志系统 - 智能检测环境
    import os

    # 检查是否在测试环境中，如果是，保持现有的日志配置
    if os.getenv("TESTING") == "true":
        # 测试环境：不重新初始化日志系统，使用测试的配置
        logger = get_logger("app")
        logger.info("检测到测试环境，保持现有日志配置")
    else:
        # 生产/开发环境：正常初始化日志系统
        logger = setup_logging()
        logger.info("日志系统初始化完成")

    create_db_and_tables()
    init_global_data()
    print("✅ 数据库初始化完成")
    logger.info("数据库初始化完成")

    yield

    # 关闭时清理（如果需要）
    print("👋 FastAPI应用关闭")
    logger.info("FastAPI应用关闭")


# 创建FastAPI应用实例
openapi_config = get_openapi_config()
app = FastAPI(
    title=openapi_config["title"],
    version=openapi_config["version"],
    description=openapi_config["description"],
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
    # openapi_tags=openapi_config["tags"],
    responses=openapi_config["responses"]  # 使用全局响应配置
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"欢迎使用 {settings.APP_NAME}",
        "version": settings.APP_VERSION,
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": settings.APP_NAME}


# 注册异常处理器
app.add_exception_handler(APIException, api_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# 注册API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8012,
        reload=settings.DEBUG
    ) 