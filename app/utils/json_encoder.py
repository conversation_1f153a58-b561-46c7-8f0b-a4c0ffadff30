import json
from decimal import Decimal
from datetime import datetime, date
from typing import Any, Dict


class DecimalEncoder(json.JSONEncoder):
    """自定义JSON编码器，支持Decimal类型"""
    
    def default(self, obj: Any) -> Any:
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super().default(obj)


def safe_json_encode(data: Any) -> Dict[str, Any]:
    """安全地将数据转换为可JSON序列化的格式"""
    if data is None:
        return None
    
    if isinstance(data, dict):
        return {key: safe_json_encode(value) for key, value in data.items()}
    elif isinstance(data, (list, tuple)):
        return [safe_json_encode(item) for item in data]
    elif isinstance(data, Decimal):
        return float(data)
    elif isinstance(data, (datetime, date)):
        return data.isoformat()
    else:
        return data


def prepare_audit_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """准备审计日志数据，确保可以JSON序列化"""
    if data is None:
        return None
    
    return safe_json_encode(data) 