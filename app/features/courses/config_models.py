"""
课程系统配置数据模型
"""
from datetime import time, datetime, timezone
from typing import Optional
from sqlmodel import SQLModel, Field, UniqueConstraint, Index


class CourseSystemConfigBase(SQLModel):
    """课程系统配置基础模型"""
    
    # 基础字段
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    
    # 课节基础配置
    default_slot_duration_minutes: int = Field(
        default=25, 
        ge=5, 
        le=120,
        description="默认课节时长(分钟)"
    )
    default_slot_interval_minutes: int = Field(
        default=5, 
        ge=0, 
        le=30,
        description="默认课节间隔(分钟)"
    )
    
    # 直接约课配置
    direct_booking_enabled: bool = Field(
        default=True, 
        description="是否启用直接约课"
    )
    max_advance_days: Optional[int] = Field(
        default=30, 
        ge=1, 
        le=365,
        description="会员最多可预约x天后的课程"
    )
    booking_deadline_hours: Optional[int] = Field(
        default=2, 
        ge=0, 
        le=72,
        description="预约截止时间：上课前x小时"
    )
    cancel_deadline_hours: Optional[int] = Field(
        default=2, 
        ge=0, 
        le=72,
        description="取消截止时间：上课前x小时"
    )
    booking_time_from: Optional[time] = Field(
        default=None,
        description="预约操作时间限制：从"
    )
    booking_time_to: Optional[time] = Field(
        default=None,
        description="预约操作时间限制：到"
    )
    require_material: bool = Field(
        default=True, 
        description="会员预约时是否必须选教材"
    )
    
    # 教师权限配置
    teacher_can_add_slots: bool = Field(
        default=True, 
        description="教师是否可自主增加课时"
    )
    teacher_can_delete_empty_slots: bool = Field(
        default=True, 
        description="教师是否可删除空课时"
    )
    teacher_can_cancel_booking: bool = Field(
        default=False, 
        description="教师是否可取消学生预约"
    )
    teacher_need_confirm: bool = Field(
        default=False, 
        description="学生预约后是否需要教师确认"
    )
    
    # 固定课表配置
    fixed_booking_enabled: bool = Field(
        default=True, 
        description="是否启用固定课表约课"
    )
    auto_schedule_enabled: bool = Field(
        default=True, 
        description="是否启用自动排课"
    )
    auto_schedule_day: int = Field(
        default=22, 
        ge=1, 
        le=28,
        description="自动排课日期(每月几号)"
    )
    auto_schedule_time: time = Field(
        default=time(14, 0), 
        description="自动排课时间"
    )
    
    # 排课配置
    default_schedule_weeks: int = Field(
        default=4, 
        ge=1, 
        le=12,
        description="默认排课周数"
    )
    interrupt_on_conflict: bool = Field(
        default=True, 
        description="遇到重复时间是否终止"
    )
    skip_insufficient_balance: bool = Field(
        default=True, 
        description="是否跳过余额不足的学生"
    )
    remove_insufficient_locks: bool = Field(
        default=True, 
        description="是否移除余额不足学生的固定位"
    )


class CourseSystemConfig(CourseSystemConfigBase, table=True):
    """课程系统配置表"""
    
    __tablename__ = "course_system_configs"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    created_by: Optional[int] = Field(default=None, foreign_key="users.id", description="创建者ID")
    updated_by: Optional[int] = Field(default=None, foreign_key="users.id", description="更新者ID")
    
    __table_args__ = (
        # 每个租户只能有一个配置记录
        UniqueConstraint('tenant_id', name='uq_tenant_course_config'),
        # 基础索引
        Index('idx_course_config_tenant', 'tenant_id'),
        Index('idx_course_config_created_by', 'created_by'),
        Index('idx_course_config_updated_by', 'updated_by'),
    )
    
    class Config:
        """模型配置"""
        json_schema_extra = {
            "example": {
                "tenant_id": 1,
                "default_slot_duration_minutes": 25,
                "default_slot_interval_minutes": 5,
                "direct_booking_enabled": True,
                "max_advance_days": 30,
                "booking_deadline_hours": 2,
                "cancel_deadline_hours": 2,
                "require_material": True,
                "teacher_can_add_slots": True,
                "teacher_can_delete_empty_slots": True,
                "teacher_can_cancel_booking": False,
                "teacher_need_confirm": False,
                "fixed_booking_enabled": True,
                "auto_schedule_enabled": True,
                "auto_schedule_day": 22,
                "auto_schedule_time": "14:00:00",
                "default_schedule_weeks": 4,
                "interrupt_on_conflict": True,
                "skip_insufficient_balance": True,
                "remove_insufficient_locks": True,
                "created_by": 1
            }
        } 