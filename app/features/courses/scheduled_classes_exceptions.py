"""已排课表业务异常模块"""

from enum import Enum
from typing import Optional, Dict, Any
from datetime import datetime

from app.api.common.exceptions import BusinessException, NotFoundError


class ScheduledClassErrorCode(str, Enum):
    """已排课表错误代码"""
    # 通用错误
    GENERAL_ERROR = "SCHEDULED_CLASS_GENERAL_ERROR"
    VALIDATION_ERROR = "SCHEDULED_CLASS_VALIDATION_ERROR"
    
    # 时间冲突错误
    TEACHER_TIME_CONFLICT = "TEACHER_TIME_CONFLICT"
    MEMBER_TIME_CONFLICT = "MEMBER_TIME_CONFLICT"
    
    # 状态转换错误
    INVALID_STATUS_TRANSITION = "INVALID_STATUS_TRANSITION"
    
    # 预约相关错误
    BOOKING_DEADLINE_PASSED = "BOOKING_DEADLINE_PASSED"
    CANCELLATION_DEADLINE_PASSED = "CANCELLATION_DEADLINE_PASSED"
    CLASS_ALREADY_BOOKED = "CLASS_ALREADY_BOOKED"
    CLASS_NOT_AVAILABLE = "CLASS_NOT_AVAILABLE"
    
    # 权限错误
    MEMBER_CANNOT_CANCEL = "MEMBER_CANNOT_CANCEL"
    CLASS_NOT_VISIBLE = "CLASS_NOT_VISIBLE"


class ScheduledClassBusinessException(BusinessException):
    """已排课表业务异常"""
    
    @classmethod
    def general_error(cls, message: str, details: Optional[Dict] = None):
        """通用错误"""
        return cls(
            message=message,
            code=ScheduledClassErrorCode.GENERAL_ERROR,
            details=details
        )
    
    @classmethod
    def validation_error(cls, message: str, details: Optional[Dict] = None):
        """验证错误"""
        return cls(
            message=f"数据验证失败: {message}",
            code=ScheduledClassErrorCode.VALIDATION_ERROR,
            details=details
        )
    
    @classmethod
    def teacher_time_conflict(cls, teacher_id: int, class_datetime: datetime, details: Optional[Dict] = None):
        """教师时间冲突"""
        return cls(
            message=f"教师(ID:{teacher_id})在{class_datetime.strftime('%Y-%m-%d %H:%M')}时间段已有课程安排",
            code=ScheduledClassErrorCode.TEACHER_TIME_CONFLICT,
            details={"teacher_id": teacher_id, "class_datetime": class_datetime.isoformat(), **(details or {})}
        )
    
    @classmethod
    def member_time_conflict(cls, member_id: int, class_datetime: datetime, details: Optional[Dict] = None):
        """会员时间冲突"""
        return cls(
            message=f"会员(ID:{member_id})在{class_datetime.strftime('%Y-%m-%d %H:%M')}时间段已有课程安排",
            code=ScheduledClassErrorCode.MEMBER_TIME_CONFLICT,
            details={"member_id": member_id, "class_datetime": class_datetime.isoformat(), **(details or {})}
        )
    
    @classmethod
    def invalid_status_transition(cls, current_status: str, new_status: str, details: Optional[Dict] = None):
        """无效的状态转换"""
        return cls(
            message=f"课程状态不能从'{current_status}'转换为'{new_status}'",
            code=ScheduledClassErrorCode.INVALID_STATUS_TRANSITION,
            details={"current_status": current_status, "new_status": new_status, **(details or {})}
        )
    
    @classmethod
    def booking_deadline_passed(cls, class_datetime: datetime, deadline_hours: int = 2, details: Optional[Dict] = None):
        """预约截止时间已过"""
        return cls(
            message=f"预约时间已过，需要在上课前{deadline_hours}小时预约",
            code=ScheduledClassErrorCode.BOOKING_DEADLINE_PASSED,
            details={"class_datetime": class_datetime.isoformat(), "deadline_hours": deadline_hours, **(details or {})}
        )
    
    @classmethod
    def cancellation_deadline_passed(cls, class_datetime: datetime, deadline_hours: int = 2, details: Optional[Dict] = None):
        """取消截止时间已过"""
        return cls(
            message=f"取消时间已过，需要在上课前{deadline_hours}小时取消",
            code=ScheduledClassErrorCode.CANCELLATION_DEADLINE_PASSED,
            details={"class_datetime": class_datetime.isoformat(), "deadline_hours": deadline_hours, **(details or {})}
        )
    
    @classmethod
    def class_already_booked(cls, class_id: int, details: Optional[Dict] = None):
        """课程已被预约"""
        return cls(
            message="课程已被预约，无法重复预约",
            code=ScheduledClassErrorCode.CLASS_ALREADY_BOOKED,
            details={"class_id": class_id, **(details or {})}
        )
    
    @classmethod
    def class_not_available(cls, class_id: int, current_status: str, details: Optional[Dict] = None):
        """课程不可预约"""
        return cls(
            message=f"课程当前状态为'{current_status}'，不可预约",
            code=ScheduledClassErrorCode.CLASS_NOT_AVAILABLE,
            details={"class_id": class_id, "current_status": current_status, **(details or {})}
        )
    
    @classmethod
    def member_cannot_cancel(cls, class_id: int, details: Optional[Dict] = None):
        """会员不能取消课程"""
        return cls(
            message="该课程不允许会员取消",
            code=ScheduledClassErrorCode.MEMBER_CANNOT_CANCEL,
            details={"class_id": class_id, **(details or {})}
        )
    
    @classmethod
    def class_not_visible(cls, class_id: int, details: Optional[Dict] = None):
        """课程对会员不可见"""
        return cls(
            message="课程对会员不可见",
            code=ScheduledClassErrorCode.CLASS_NOT_VISIBLE,
            details={"class_id": class_id, **(details or {})}
        )


class ScheduledClassNotFoundError(NotFoundError):
    """已排课表记录不存在异常"""
    
    def __init__(self, class_id: int):
        super().__init__(
            resource=f"课程记录(ID:{class_id})",
            details={"class_id": class_id}
        )
