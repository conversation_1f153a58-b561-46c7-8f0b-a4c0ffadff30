"""
课程系统配置API数据模式
"""
from sqlmodel import SQLModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime, time
from pydantic import field_validator, model_validator

from .config_models import CourseSystemConfigBase
from .config_utils import CourseConfigValidator


# 基础Schema模型
class CourseSystemConfigCreate(SQLModel):
    """创建课程系统配置请求模型"""
    
    # 课节基础配置
    default_slot_duration_minutes: Optional[int] = Field(
        default=25, 
        ge=5, 
        le=120,
        description="默认课节时长(分钟)"
    )
    default_slot_interval_minutes: Optional[int] = Field(
        default=5, 
        ge=0, 
        le=30,
        description="默认课节间隔(分钟)"
    )
    
    # 直接约课配置
    direct_booking_enabled: Optional[bool] = Field(
        default=True, 
        description="是否启用直接约课"
    )
    max_advance_days: Optional[int] = Field(
        default=30, 
        ge=1, 
        le=365,
        description="最大提前预约天数"
    )
    booking_deadline_hours: Optional[int] = Field(
        default=2, 
        ge=0, 
        le=72,
        description="约课截止时间(小时)"
    )
    cancel_deadline_hours: Optional[int] = Field(
        default=2, 
        ge=0, 
        le=72,
        description="取消约课截止时间(小时)"
    )
    booking_time_from: Optional[time] = Field(
        default=None,
        description="约课时间范围-开始时间"
    )
    booking_time_to: Optional[time] = Field(
        default=None,
        description="约课时间范围-结束时间"
    )
    
    # 课程材料配置
    require_material: Optional[bool] = Field(
        default=True, 
        description="是否必须选择教材"
    )
    
    # 教师权限配置
    teacher_can_add_slots: Optional[bool] = Field(
        default=True, 
        description="教师是否可以添加时间段"
    )
    teacher_can_delete_empty_slots: Optional[bool] = Field(
        default=True, 
        description="教师是否可以删除空时间段"
    )
    teacher_can_cancel_booking: Optional[bool] = Field(
        default=False, 
        description="教师是否可以取消预约"
    )
    teacher_need_confirm: Optional[bool] = Field(
        default=False, 
        description="教师是否需要确认预约"
    )
    
    # 固定课表配置
    fixed_booking_enabled: Optional[bool] = Field(
        default=True, 
        description="是否启用固定课表"
    )
    
    # 排课配置
    auto_schedule_enabled: Optional[bool] = Field(
        default=True, 
        description="是否启用自动排课"
    )
    auto_schedule_day: Optional[int] = Field(
        default=7, 
        ge=1, 
        le=7,
        description="自动排课提前天数"
    )
    auto_schedule_time: Optional[time] = Field(
        default=None,
        description="自动排课时间"
    )
    default_schedule_weeks: Optional[int] = Field(
        default=4, 
        ge=1, 
        le=52,
        description="默认排课周数"
    )
    
    @field_validator('booking_time_from', 'booking_time_to', 'auto_schedule_time')
    @classmethod
    def validate_time_fields(cls, v):
        """验证时间字段格式"""
        if v is not None:
            if isinstance(v, str):
                try:
                    # 尝试解析时间字符串
                    from datetime import datetime
                    parsed_time = datetime.strptime(v, '%H:%M').time()
                    return parsed_time
                except ValueError:
                    raise ValueError('时间格式错误，应为 HH:MM 格式')
        return v
    
    @model_validator(mode='after')
    def validate_config(self):
        """验证配置的完整性和一致性"""
        # 验证时间范围
        if self.booking_time_from and self.booking_time_to:
            if self.booking_time_from >= self.booking_time_to:
                raise ValueError('约课开始时间必须早于结束时间')
        
        # 验证截止时间逻辑
        if self.booking_deadline_hours and self.cancel_deadline_hours:
            if self.booking_deadline_hours > self.cancel_deadline_hours:
                raise ValueError('约课截止时间不能晚于取消截止时间')
        
        # 验证课节时长和间隔
        if self.default_slot_duration_minutes and self.default_slot_interval_minutes:
            if self.default_slot_interval_minutes >= self.default_slot_duration_minutes:
                raise ValueError('课节间隔不能大于或等于课节时长')
        
        return self


class CourseSystemConfigUpdate(SQLModel):
    """更新课程系统配置请求模型"""
    
    # 课节基础配置
    default_slot_duration_minutes: Optional[int] = Field(
        default=None, 
        ge=5, 
        le=120,
        description="默认课节时长(分钟)"
    )
    default_slot_interval_minutes: Optional[int] = Field(
        default=None, 
        ge=0, 
        le=30,
        description="默认课节间隔(分钟)"
    )
    
    # 直接约课配置
    direct_booking_enabled: Optional[bool] = Field(
        default=None, 
        description="是否启用直接约课"
    )
    max_advance_days: Optional[int] = Field(
        default=None, 
        ge=1, 
        le=365,
        description="最大提前预约天数"
    )
    booking_deadline_hours: Optional[int] = Field(
        default=None, 
        ge=0, 
        le=72,
        description="约课截止时间(小时)"
    )
    cancel_deadline_hours: Optional[int] = Field(
        default=None, 
        ge=0, 
        le=72,
        description="取消约课截止时间(小时)"
    )
    booking_time_from: Optional[time] = Field(
        default=None,
        description="约课时间范围-开始时间"
    )
    booking_time_to: Optional[time] = Field(
        default=None,
        description="约课时间范围-结束时间"
    )
    
    # 课程材料配置
    require_material: Optional[bool] = Field(
        default=None, 
        description="是否必须选择教材"
    )
    
    # 教师权限配置
    teacher_can_add_slots: Optional[bool] = Field(
        default=None, 
        description="教师是否可以添加时间段"
    )
    teacher_can_delete_empty_slots: Optional[bool] = Field(
        default=None, 
        description="教师是否可以删除空时间段"
    )
    teacher_can_cancel_booking: Optional[bool] = Field(
        default=None, 
        description="教师是否可以取消预约"
    )
    teacher_need_confirm: Optional[bool] = Field(
        default=None, 
        description="教师是否需要确认预约"
    )
    
    # 固定课表配置
    fixed_booking_enabled: Optional[bool] = Field(
        default=None, 
        description="是否启用固定课表"
    )
    
    # 排课配置
    auto_schedule_enabled: Optional[bool] = Field(
        default=None, 
        description="是否启用自动排课"
    )
    auto_schedule_day: Optional[int] = Field(
        default=None, 
        ge=1, 
        le=7,
        description="自动排课提前天数"
    )
    auto_schedule_time: Optional[time] = Field(
        default=None,
        description="自动排课时间"
    )
    default_schedule_weeks: Optional[int] = Field(
        default=None, 
        ge=1, 
        le=52,
        description="默认排课周数"
    )
    
    @field_validator('booking_time_from', 'booking_time_to', 'auto_schedule_time')
    @classmethod
    def validate_time_fields(cls, v):
        """验证时间字段格式"""
        if v is not None:
            if isinstance(v, str):
                try:
                    # 尝试解析时间字符串
                    from datetime import datetime
                    parsed_time = datetime.strptime(v, '%H:%M').time()
                    return parsed_time
                except ValueError:
                    raise ValueError('时间格式错误，应为 HH:MM 格式')
        return v


class CourseSystemConfigQuery(SQLModel):
    """课程系统配置查询参数模型"""
    include_defaults: bool = Field(
        default=True, 
        description="是否包含默认值"
    )
    format_output: bool = Field(
        default=True, 
        description="是否格式化输出"
    )


# API响应模型
class CourseSystemConfigResponse(CourseSystemConfigBase):
    """课程系统配置响应模型"""
    id: int = Field(description="配置ID")
    tenant_id: int = Field(description="租户ID")
    created_by: Optional[int] = Field(description="创建者ID")
    created_at: datetime = Field(description="创建时间")
    updated_at: Optional[datetime] = Field(description="更新时间")
    
    # 添加格式化字段
    booking_time_range: Optional[str] = Field(
        default=None,
        description="约课时间范围（格式化显示）"
    )
    auto_schedule_time_display: Optional[str] = Field(
        default=None,
        description="自动排课时间（格式化显示）"
    )
    
    @model_validator(mode='after')
    def format_display_fields(self):
        """格式化显示字段"""
        # 格式化约课时间范围
        if self.booking_time_from and self.booking_time_to:
            self.booking_time_range = f"{self.booking_time_from.strftime('%H:%M')}-{self.booking_time_to.strftime('%H:%M')}"
        
        # 格式化自动排课时间
        if self.auto_schedule_time:
            self.auto_schedule_time_display = self.auto_schedule_time.strftime('%H:%M')
        
        return self


class CourseSystemConfigDetail(CourseSystemConfigResponse):
    """课程系统配置详情响应模型"""
    validation_summary: Dict[str, Any] = Field(
        default_factory=dict,
        description="配置验证摘要"
    )
    usage_statistics: Dict[str, Any] = Field(
        default_factory=dict,
        description="配置使用统计"
    )


# 配置验证模型
class CourseSystemConfigValidation(SQLModel):
    """课程系统配置验证模型"""
    config_data: Dict[str, Any] = Field(description="配置数据")
    strict_mode: bool = Field(default=False, description="是否启用严格模式验证")
    
    @field_validator('config_data')
    @classmethod
    def validate_config_data(cls, v):
        """验证配置数据"""
        if not v:
            raise ValueError('配置数据不能为空')
        
        # 使用配置验证器进行验证
        validator = CourseConfigValidator()
        validation_result = validator.validate_config(v)
        
        if not validation_result.get('is_valid', False):
            errors = validation_result.get('errors', [])
            raise ValueError(f'配置验证失败: {"; ".join(errors)}')
        
        return v


class CourseSystemConfigReset(SQLModel):
    """课程系统配置重置模型"""
    reset_to_defaults: bool = Field(default=True, description="是否重置为默认值")
    preserve_fields: List[str] = Field(
        default_factory=list,
        description="保留的字段列表（不重置）"
    )
    confirm_reset: bool = Field(description="确认重置操作")
    
    @field_validator('confirm_reset')
    @classmethod
    def validate_confirm_reset(cls, v):
        """验证确认重置"""
        if not v:
            raise ValueError('必须确认重置操作')
        return v


# 配置导入导出模型
class CourseSystemConfigExport(SQLModel):
    """课程系统配置导出模型"""
    include_metadata: bool = Field(default=True, description="是否包含元数据")
    export_format: str = Field(default="json", description="导出格式")
    
    @field_validator('export_format')
    @classmethod
    def validate_export_format(cls, v):
        """验证导出格式"""
        if v not in ['json', 'yaml']:
            raise ValueError('导出格式必须是 json 或 yaml')
        return v


class CourseSystemConfigImport(SQLModel):
    """课程系统配置导入模型"""
    config_data: Dict[str, Any] = Field(description="配置数据")
    overwrite_existing: bool = Field(default=False, description="是否覆盖现有配置")
    validate_before_import: bool = Field(default=True, description="导入前是否验证")
    
    @field_validator('config_data')
    @classmethod
    def validate_import_data(cls, v):
        """验证导入数据"""
        if not v:
            raise ValueError('导入数据不能为空')
        
        # 检查必要的字段
        required_fields = ['default_slot_duration_minutes', 'default_slot_interval_minutes']
        for field in required_fields:
            if field not in v:
                raise ValueError(f'缺少必要字段: {field}')
        
        return v


# 配置比较模型
class CourseSystemConfigCompare(SQLModel):
    """课程系统配置比较模型"""
    source_config: Dict[str, Any] = Field(description="源配置")
    target_config: Dict[str, Any] = Field(description="目标配置")
    
    @field_validator('source_config', 'target_config')
    @classmethod
    def validate_config_data(cls, v):
        """验证配置数据"""
        if not v:
            raise ValueError('配置数据不能为空')
        return v


class CourseSystemConfigDiff(SQLModel):
    """课程系统配置差异模型"""
    added_fields: Dict[str, Any] = Field(default_factory=dict, description="新增字段")
    removed_fields: Dict[str, Any] = Field(default_factory=dict, description="删除字段")
    changed_fields: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="变更字段")
    unchanged_fields: Dict[str, Any] = Field(default_factory=dict, description="未变更字段")
    summary: Dict[str, int] = Field(default_factory=dict, description="差异摘要") 