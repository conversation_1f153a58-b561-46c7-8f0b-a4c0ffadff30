"""
课程系统配置工具函数
"""
from datetime import time
from typing import Optional, Dict, Any
from .config_exceptions import CourseConfigBusinessException


class CourseConfigValidator:
    """课程配置验证器"""
    
    # 默认配置值
    DEFAULT_CONFIG = {
        "default_slot_duration_minutes": 25,
        "default_slot_interval_minutes": 5,
        "direct_booking_enabled": True,
        "max_advance_days": 30,
        "booking_deadline_hours": 2,
        "cancel_deadline_hours": 2,
        "booking_time_from": None,
        "booking_time_to": None,
        "require_material": True,
        "teacher_can_add_slots": True,
        "teacher_can_delete_empty_slots": True,
        "teacher_can_cancel_booking": False,
        "teacher_need_confirm": False,
        "fixed_booking_enabled": True,
        "auto_schedule_enabled": True,
        "auto_schedule_day": 22,
        "auto_schedule_time": time(14, 0),
        "default_schedule_weeks": 4,
        "interrupt_on_conflict": True,
        "skip_insufficient_balance": True,
        "remove_insufficient_locks": True,
    }
    
    @classmethod
    def validate_slot_duration(cls, duration: int) -> None:
        """验证课节时长"""
        if not (5 <= duration <= 120):
            raise CourseConfigBusinessException.invalid_duration(duration)
    
    @classmethod
    def validate_slot_interval(cls, interval: int) -> None:
        """验证课节间隔"""
        if not (0 <= interval <= 30):
            raise CourseConfigBusinessException.invalid_interval(interval)
    
    @classmethod
    def validate_advance_days(cls, days: Optional[int]) -> None:
        """验证提前预约天数"""
        if days is not None and not (1 <= days <= 365):
            raise CourseConfigBusinessException.invalid_advance_days(days)
    
    @classmethod
    def validate_deadline_hours(cls, hours: Optional[int], deadline_type: str) -> None:
        """验证截止时间"""
        if hours is not None and not (0 <= hours <= 72):
            raise CourseConfigBusinessException.invalid_deadline_hours(hours, deadline_type)
    
    @classmethod
    def validate_booking_time_range(cls, from_time: Optional[time], to_time: Optional[time]) -> None:
        """验证预约时间范围"""
        if from_time and to_time and from_time >= to_time:
            raise CourseConfigBusinessException.booking_time_conflict()
    
    @classmethod
    def validate_schedule_day(cls, day: int) -> None:
        """验证排课日期"""
        if not (1 <= day <= 28):
            raise CourseConfigBusinessException.invalid_schedule_day(day)
    
    @classmethod
    def validate_schedule_weeks(cls, weeks: int) -> None:
        """验证排课周数"""
        if not (1 <= weeks <= 12):
            raise CourseConfigBusinessException.invalid_schedule_weeks(weeks)
    
    @classmethod
    def validate_config_data(cls, config_data: Dict[str, Any]) -> None:
        """验证完整的配置数据"""
        # 验证课节基础配置
        if "default_slot_duration_minutes" in config_data:
            cls.validate_slot_duration(config_data["default_slot_duration_minutes"])
        
        if "default_slot_interval_minutes" in config_data:
            cls.validate_slot_interval(config_data["default_slot_interval_minutes"])
        
        # 验证直接约课配置
        if "max_advance_days" in config_data:
            cls.validate_advance_days(config_data["max_advance_days"])
        
        if "booking_deadline_hours" in config_data:
            cls.validate_deadline_hours(config_data["booking_deadline_hours"], "预约")
        
        if "cancel_deadline_hours" in config_data:
            cls.validate_deadline_hours(config_data["cancel_deadline_hours"], "取消")
        
        # 验证预约时间范围
        booking_from = config_data.get("booking_time_from")
        booking_to = config_data.get("booking_time_to")
        if booking_from or booking_to:
            cls.validate_booking_time_range(booking_from, booking_to)
        
        # 验证排课配置
        if "auto_schedule_day" in config_data:
            cls.validate_schedule_day(config_data["auto_schedule_day"])
        
        if "default_schedule_weeks" in config_data:
            cls.validate_schedule_weeks(config_data["default_schedule_weeks"])
    
    @classmethod
    def get_default_config(cls) -> Dict[str, Any]:
        """获取默认配置"""
        return cls.DEFAULT_CONFIG.copy()
    
    @classmethod
    def merge_with_defaults(cls, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """将用户配置与默认配置合并"""
        merged_config = cls.get_default_config()
        merged_config.update(config_data)
        return merged_config


class CourseConfigHelper:
    """课程配置辅助工具"""
    
    @staticmethod
    def format_time_range(from_time: Optional[time], to_time: Optional[time]) -> str:
        """格式化时间范围显示"""
        if not from_time and not to_time:
            return "全天"
        elif from_time and not to_time:
            return f"从 {from_time.strftime('%H:%M')} 开始"
        elif not from_time and to_time:
            return f"到 {to_time.strftime('%H:%M')} 结束"
        else:
            return f"{from_time.strftime('%H:%M')} - {to_time.strftime('%H:%M')}"
    
    @staticmethod
    def get_config_summary(config) -> Dict[str, str]:
        """获取配置摘要信息"""
        return {
            "课节时长": f"{config.default_slot_duration_minutes} 分钟",
            "课节间隔": f"{config.default_slot_interval_minutes} 分钟",
            "直接约课": "启用" if config.direct_booking_enabled else "禁用",
            "固定约课": "启用" if config.fixed_booking_enabled else "禁用",
            "自动排课": "启用" if config.auto_schedule_enabled else "禁用",
            "提前预约": f"最多 {config.max_advance_days} 天" if config.max_advance_days else "无限制",
            "预约截止": f"课前 {config.booking_deadline_hours} 小时" if config.booking_deadline_hours else "无限制",
            "取消截止": f"课前 {config.cancel_deadline_hours} 小时" if config.cancel_deadline_hours else "无限制",
            "预约时间": CourseConfigHelper.format_time_range(config.booking_time_from, config.booking_time_to),
            "排课日期": f"每月 {config.auto_schedule_day} 号",
            "排课时间": config.auto_schedule_time.strftime('%H:%M'),
            "排课周数": f"{config.default_schedule_weeks} 周",
        }
    
    @staticmethod
    def is_booking_time_allowed(config, current_time: time) -> bool:
        """检查当前时间是否允许预约"""
        if not config.booking_time_from and not config.booking_time_to:
            return True
        
        if config.booking_time_from and config.booking_time_to:
            return config.booking_time_from <= current_time <= config.booking_time_to
        elif config.booking_time_from:
            return current_time >= config.booking_time_from
        elif config.booking_time_to:
            return current_time <= config.booking_time_to
        
        return True
    
    @staticmethod
    def get_teacher_permissions_summary(config) -> Dict[str, str]:
        """获取教师权限配置摘要"""
        return {
            "自主增加课时": "允许" if config.teacher_can_add_slots else "不允许",
            "删除空课时": "允许" if config.teacher_can_delete_empty_slots else "不允许",
            "取消学生预约": "允许" if config.teacher_can_cancel_booking else "不允许",
            "学生预约需确认": "需要" if config.teacher_need_confirm else "不需要",
        }
    
    @staticmethod
    def get_scheduling_settings_summary(config) -> Dict[str, str]:
        """获取排课设置摘要"""
        return {
            "遇到冲突": "终止排课" if config.interrupt_on_conflict else "跳过冲突",
            "余额不足": "跳过学生" if config.skip_insufficient_balance else "终止排课",
            "移除余额不足的锁定": "是" if config.remove_insufficient_locks else "否",
        }