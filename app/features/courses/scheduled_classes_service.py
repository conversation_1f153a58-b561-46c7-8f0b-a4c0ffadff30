from typing import List, Optional, Tuple
from sqlmodel import Session, select, text, func, and_, or_
from datetime import datetime, timezone

from .scheduled_classes_models import ScheduledClass, ClassStatus, ClassType
from .scheduled_classes_schemas import (
    ScheduledClassCreate, ScheduledClassUpdate, ScheduledClassQuery,
    TeacherClassCreate, MemberClassBooking, AdminClassCreate,
    FixedScheduleClassCreate, BatchFixedScheduleCreate,
    ClassBooking, ClassCancellation, ClassStatusUpdate,
    BatchBookResult, BatchCancelResult,
    ScheduledClassRead
)
from .scheduled_classes_validators import ScheduledClassValidator

# 导入业务异常和便捷函数
from .scheduled_classes_exceptions import (
    ScheduledClassBusinessException, ScheduledClassNotFoundError
)

# 导入会员卡相关服务和模型
from ..member_cards.consumption_service import ConsumptionService
from ..member_cards.schemas import CourseBookingBalanceCheck
from ..member_cards.exceptions import (
    MemberCardNotFoundError, MemberCardBusinessException
)

# 导入统一异常处理
from app.api.common.exceptions import BusinessException


class ScheduledClassService:
    """已排课表服务"""
    
    def __init__(self, session: Session, tenant_id: int):
        self.session = session
        self.tenant_id = tenant_id
        # 设置RLS上下文
        self.session.exec(text(f"SET app.current_tenant_id = '{tenant_id}'"))
    
    # ==================== 基础CRUD操作 ====================
    
    def create_scheduled_class(self, class_data: ScheduledClassCreate, created_by: Optional[int] = None) -> ScheduledClass:
        """创建已排课表记录"""
        # 验证课程时间
        time_errors = ScheduledClassValidator.validate_class_time(
            class_data.class_datetime, 
            class_data.duration_minutes
        )
        if time_errors:
            raise ScheduledClassBusinessException.validation_error("; ".join(time_errors))
        
        # 验证会员预约信息
        member_errors = ScheduledClassValidator.validate_member_booking(
            class_data.member_id, 
            ClassStatus.BOOKED if class_data.member_id else ClassStatus.AVAILABLE
        )
        if member_errors:
            raise ScheduledClassBusinessException.validation_error("; ".join(member_errors))
        
        # 检查教师时间冲突
        if self._check_teacher_time_conflict(
            class_data.teacher_id, 
            class_data.class_datetime, 
            class_data.duration_minutes
        ):
            raise ScheduledClassBusinessException.teacher_time_conflict(
                class_data.teacher_id, 
                class_data.class_datetime
            )
        
        # 如果指定了会员，检查会员时间冲突
        if class_data.member_id and self._check_member_time_conflict(
            class_data.member_id, 
            class_data.class_datetime, 
            class_data.duration_minutes
        ):
            raise ScheduledClassBusinessException.member_time_conflict(
                class_data.member_id, 
                class_data.class_datetime
            )
        
        # 创建课程记录
        class_dict = class_data.model_dump()
        class_dict['tenant_id'] = self.tenant_id
        class_dict['created_by'] = created_by
        
        # 根据是否有会员ID设置状态
        if class_data.member_id:
            class_dict['status'] = ClassStatus.BOOKED
        else:
            class_dict['status'] = ClassStatus.AVAILABLE
        
        scheduled_class = ScheduledClass(**class_dict)
        
        # 设置创建时间和更新时间
        now = datetime.now()
        scheduled_class.created_at = now
        scheduled_class.updated_at = now
        
        self.session.add(scheduled_class)
        self.session.commit()
        self.session.refresh(scheduled_class)
        
        # 验证对象是否真的存在于数据库
        db_class = self.session.get(ScheduledClass, scheduled_class.id)
        if not db_class:
            raise ScheduledClassBusinessException.general_error("课程创建失败：无法从数据库检索新创建的课程")
        
        return scheduled_class
    
    def get_scheduled_class(self, class_id: int) -> Optional[ScheduledClass]:
        """根据ID获取已排课表记录（RLS自动过滤租户）"""
        return self.session.get(ScheduledClass, class_id)
    
    def update_scheduled_class(self, class_id: int, class_data: ScheduledClassUpdate, updated_by: Optional[int] = None) -> ScheduledClass:
        """更新已排课表记录"""
        scheduled_class = self.session.get(ScheduledClass, class_id)
        if not scheduled_class:
            raise ScheduledClassNotFoundError(class_id)
        
        # 如果更新时间，验证新时间
        if class_data.class_datetime:
            time_errors = ScheduledClassValidator.validate_class_time(
                class_data.class_datetime, 
                class_data.duration_minutes or scheduled_class.duration_minutes
            )
            if time_errors:
                raise ScheduledClassBusinessException.validation_error("; ".join(time_errors))
            
            # 检查时间冲突（排除当前课程）
            if self._check_teacher_time_conflict(
                scheduled_class.teacher_id, 
                class_data.class_datetime, 
                class_data.duration_minutes or scheduled_class.duration_minutes,
                exclude_class_id=class_id
            ):
                raise ScheduledClassBusinessException.teacher_time_conflict(
                    scheduled_class.teacher_id, 
                    class_data.class_datetime
                )
        
        # 如果更新状态，验证状态转换
        if class_data.status and not ScheduledClassValidator.validate_status_transition(
            scheduled_class.status, 
            class_data.status
        ):
            raise ScheduledClassBusinessException.invalid_status_transition(
                scheduled_class.status.value, 
                class_data.status.value
            )
        
        # 验证会员预约信息
        new_member_id = class_data.member_id if class_data.member_id is not None else scheduled_class.member_id
        new_status = class_data.status if class_data.status is not None else scheduled_class.status
        
        member_errors = ScheduledClassValidator.validate_member_booking(new_member_id, new_status)
        if member_errors:
            raise ScheduledClassBusinessException.validation_error("; ".join(member_errors))
        
        # 更新字段
        update_data = class_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(scheduled_class, field, value)
        
        scheduled_class.updated_at = datetime.now()
        
        self.session.add(scheduled_class)
        self.session.commit()
        self.session.refresh(scheduled_class)
        
        return scheduled_class
    
    def delete_scheduled_class(self, class_id: int) -> bool:
        """删除已排课表记录（软删除）"""
        scheduled_class = self.session.get(ScheduledClass, class_id)
        if not scheduled_class:
            return False
        
        # 软删除
        scheduled_class.is_deleted = True
        scheduled_class.updated_at = datetime.now()
        
        self.session.add(scheduled_class)
        self.session.commit()
        return True
    
    # ==================== 查询方法 ====================
    
    def get_scheduled_classes(self, query_params: ScheduledClassQuery) -> Tuple[List[ScheduledClass], int]:
        """获取已排课表列表"""
        statement = select(ScheduledClass).where(
            and_(
                ScheduledClass.is_deleted == False
            )
        )
        
        # 应用筛选条件
        if query_params.teacher_id:
            statement = statement.where(ScheduledClass.teacher_id == query_params.teacher_id)
        if query_params.member_id:
            statement = statement.where(ScheduledClass.member_id == query_params.member_id)
        if query_params.class_type:
            statement = statement.where(ScheduledClass.class_type == query_params.class_type)
        if query_params.status:
            statement = statement.where(ScheduledClass.status == query_params.status)
        
        # 时间范围筛选
        if query_params.date_from:
            statement = statement.where(
                func.date(ScheduledClass.class_datetime) >= query_params.date_from
            )
        if query_params.date_to:
            statement = statement.where(
                func.date(ScheduledClass.class_datetime) <= query_params.date_to
            )
        if query_params.datetime_from:
            statement = statement.where(ScheduledClass.class_datetime >= query_params.datetime_from)
        if query_params.datetime_to:
            statement = statement.where(ScheduledClass.class_datetime <= query_params.datetime_to)
        
        # 时间段筛选
        if query_params.time_from:
            statement = statement.where(
                func.extract('hour', ScheduledClass.class_datetime) * 60 + 
                func.extract('minute', ScheduledClass.class_datetime) >= 
                query_params.time_from.hour * 60 + query_params.time_from.minute
            )
        if query_params.time_to:
            statement = statement.where(
                func.extract('hour', ScheduledClass.class_datetime) * 60 + 
                func.extract('minute', ScheduledClass.class_datetime) <= 
                query_params.time_to.hour * 60 + query_params.time_to.minute
            )
        
        # 价格范围筛选
        if query_params.min_price:
            statement = statement.where(ScheduledClass.price >= query_params.min_price)
        if query_params.max_price:
            statement = statement.where(ScheduledClass.price <= query_params.max_price)
        
        # 可见性筛选
        if query_params.is_visible_to_member is not None:
            statement = statement.where(ScheduledClass.is_visible_to_member == query_params.is_visible_to_member)
        
        # 搜索关键词（需要JOIN相关表，这里先简化处理）
        if query_params.search:
            statement = statement.where(
                or_(
                    ScheduledClass.material_name.contains(query_params.search),
                    ScheduledClass.booking_remark.contains(query_params.search),
                    ScheduledClass.operator_name.contains(query_params.search)
                )
            )
        
        # 获取总数
        count_statement = select(func.count()).select_from(statement.subquery())
        total = self.session.exec(count_statement).one()
        
        # 排序和分页
        if query_params.sort_by == "class_datetime":
            if query_params.sort_order == "desc":
                statement = statement.order_by(ScheduledClass.class_datetime.desc())
            else:
                statement = statement.order_by(ScheduledClass.class_datetime.asc())
        elif query_params.sort_by == "created_at":
            if query_params.sort_order == "desc":
                statement = statement.order_by(ScheduledClass.created_at.desc())
            else:
                statement = statement.order_by(ScheduledClass.created_at.asc())
        else:
            # 默认按上课时间排序
            statement = statement.order_by(ScheduledClass.class_datetime.asc())
        
        # 分页
        skip = (query_params.page - 1) * query_params.size
        statement = statement.offset(skip).limit(query_params.size)
        
        classes = self.session.exec(statement).all()
        return classes, total

    def get_available_classes(self, teacher_id: Optional[int] = None,
                            date_from: Optional[datetime] = None,
                            date_to: Optional[datetime] = None) -> List[ScheduledClass]:
        """获取可预约的课程列表"""
        statement = select(ScheduledClass).where(
            and_(
                ScheduledClass.status == ClassStatus.AVAILABLE,
                ScheduledClass.is_deleted == False,
                ScheduledClass.is_visible_to_member == True
            )
        )

        if teacher_id:
            statement = statement.where(ScheduledClass.teacher_id == teacher_id)

        if date_from:
            statement = statement.where(ScheduledClass.class_datetime >= date_from)

        if date_to:
            statement = statement.where(ScheduledClass.class_datetime <= date_to)

        statement = statement.order_by(ScheduledClass.class_datetime.asc())

        return self.session.exec(statement).all()

    def get_teacher_classes(self, teacher_id: int,
                          date_from: Optional[datetime] = None,
                          date_to: Optional[datetime] = None,
                          status: Optional[ClassStatus] = None) -> List[ScheduledClass]:
        """获取教师的课程列表"""
        statement = select(ScheduledClass).where(
            and_(
                ScheduledClass.teacher_id == teacher_id,
                ScheduledClass.is_deleted == False
            )
        )

        if date_from:
            statement = statement.where(ScheduledClass.class_datetime >= date_from)

        if date_to:
            statement = statement.where(ScheduledClass.class_datetime <= date_to)

        if status:
            statement = statement.where(ScheduledClass.status == status)

        statement = statement.order_by(ScheduledClass.class_datetime.asc())

        return self.session.exec(statement).all()

    def get_member_classes(self, member_id: int,
                         date_from: Optional[datetime] = None,
                         date_to: Optional[datetime] = None,
                         status: Optional[ClassStatus] = None) -> List[ScheduledClass]:
        """获取会员的课程列表"""
        statement = select(ScheduledClass).where(
            and_(
                ScheduledClass.member_id == member_id,
                ScheduledClass.is_deleted == False
            )
        )

        if date_from:
            statement = statement.where(ScheduledClass.class_datetime >= date_from)

        if date_to:
            statement = statement.where(ScheduledClass.class_datetime <= date_to)

        if status:
            statement = statement.where(ScheduledClass.status == status)

        statement = statement.order_by(ScheduledClass.class_datetime.asc())

        return self.session.exec(statement).all()

    def get_classes_by_date_range(self, date_from: datetime, date_to: datetime,
                                teacher_id: Optional[int] = None,
                                member_id: Optional[int] = None) -> List[ScheduledClass]:
        """根据日期范围获取课程列表"""
        statement = select(ScheduledClass).where(
            and_(
                ScheduledClass.class_datetime >= date_from,
                ScheduledClass.class_datetime <= date_to,
                ScheduledClass.is_deleted == False
            )
        )

        if teacher_id:
            statement = statement.where(ScheduledClass.teacher_id == teacher_id)

        if member_id:
            statement = statement.where(ScheduledClass.member_id == member_id)

        statement = statement.order_by(ScheduledClass.class_datetime.asc())

        return self.session.exec(statement).all()

    def count_classes_by_status(self, teacher_id: Optional[int] = None,
                              member_id: Optional[int] = None,
                              date_from: Optional[datetime] = None,
                              date_to: Optional[datetime] = None) -> dict:
        """统计各状态的课程数量"""
        base_statement = select(ScheduledClass).where(
            ScheduledClass.is_deleted == False
        )

        if teacher_id:
            base_statement = base_statement.where(ScheduledClass.teacher_id == teacher_id)

        if member_id:
            base_statement = base_statement.where(ScheduledClass.member_id == member_id)

        if date_from:
            base_statement = base_statement.where(ScheduledClass.class_datetime >= date_from)

        if date_to:
            base_statement = base_statement.where(ScheduledClass.class_datetime <= date_to)

        result = {}
        for status in ClassStatus:
            count_statement = select(func.count()).select_from(
                base_statement.where(ScheduledClass.status == status).subquery()
            )
            count = self.session.exec(count_statement).one()
            result[status.value] = count

        return result

    def batch_update_status(self, class_ids: List[int], new_status: ClassStatus,
                          updated_by: Optional[int] = None) -> List[ScheduledClass]:
        """批量更新课程状态"""
        updated_classes = []

        for class_id in class_ids:
            try:
                scheduled_class = self.session.get(ScheduledClass, class_id)
                if not scheduled_class:
                    continue

                # 验证状态转换
                if not ScheduledClassValidator.validate_status_transition(
                    scheduled_class.status, new_status
                ):
                    continue

                scheduled_class.status = new_status
                scheduled_class.updated_at = datetime.now()

                self.session.add(scheduled_class)
                updated_classes.append(scheduled_class)

            except Exception:
                # 跳过有问题的记录，继续处理其他记录
                continue

        if updated_classes:
            self.session.commit()
            for scheduled_class in updated_classes:
                self.session.refresh(scheduled_class)

        return updated_classes

    def batch_delete_classes(self, class_ids: List[int]) -> int:
        """批量删除课程（软删除）"""
        deleted_count = 0

        for class_id in class_ids:
            scheduled_class = self.session.get(ScheduledClass, class_id)
            if scheduled_class and not scheduled_class.is_deleted:
                scheduled_class.is_deleted = True
                scheduled_class.updated_at = datetime.now()
                self.session.add(scheduled_class)
                deleted_count += 1

        if deleted_count > 0:
            self.session.commit()

        return deleted_count

    # ==================== 分角色的课程创建功能 ====================

    def create_teacher_class(self, teacher_id: int, teacher_class_data: TeacherClassCreate,
                           created_by: Optional[int] = None) -> ScheduledClass:
        """教师版课程创建 - 教师开放可预约课节"""
        # 转换为统一的ScheduledClassCreate
        scheduled_class_data = ScheduledClassCreate(
            teacher_id=teacher_id,
            member_id=None,  # 教师开放的课节初始无会员
            class_datetime=teacher_class_data.class_datetime,
            duration_minutes=teacher_class_data.duration_minutes,
            class_type=ClassType.DIRECT,  # 教师开放的都是直接约课
            price=teacher_class_data.price,
            material_name=teacher_class_data.material_name,
            is_visible_to_member=teacher_class_data.is_visible_to_member,
            operator_name=f"教师开课"  # 标识操作来源
        )

        # 调用统一的创建方法
        return self.create_scheduled_class(scheduled_class_data, created_by)

    def book_member_class(self, member_id: int, member_booking_data: MemberClassBooking,
                         created_by: Optional[int] = None) -> ScheduledClass:
        """会员版课程预约 - 会员直接约课"""
        # 先获取要预约的课程
        existing_class = self.session.get(ScheduledClass, member_booking_data.class_id)
        if not existing_class:
            raise ScheduledClassNotFoundError(member_booking_data.class_id)

        # 检查课程是否可预约
        if existing_class.status != ClassStatus.AVAILABLE:
            raise ScheduledClassBusinessException.class_not_available(
                member_booking_data.class_id,
                existing_class.status.value
            )

        # 检查课程是否对会员可见
        if not existing_class.is_visible_to_member:
            raise ScheduledClassBusinessException.class_not_visible(member_booking_data.class_id)

        # 检查会员时间冲突
        if self._check_member_time_conflict(
            member_id,
            existing_class.class_datetime,
            existing_class.duration_minutes,
            exclude_class_id=member_booking_data.class_id
        ):
            raise ScheduledClassBusinessException.member_time_conflict(
                member_id,
                existing_class.class_datetime
            )

        # 验证预约时间是否在允许范围内
        booking_errors = ScheduledClassValidator.validate_booking_time(existing_class.class_datetime)
        if booking_errors:
            raise ScheduledClassBusinessException.booking_deadline_passed(
                existing_class.class_datetime
            )

        # 开始事务处理
        try:
            # 会员卡余额验证和扣费（不自动提交事务）
            used_card_id = None
            if existing_class.price and existing_class.price > 0:
                used_card_id = self._validate_and_deduct_member_card_balance(
                    member_id=member_id,
                    course_price=existing_class.price,
                    scheduled_class_id=existing_class.id,
                    preferred_card_id=member_booking_data.member_card_id
                )

            # 更新课程为已预约状态
            existing_class.member_id = member_id
            existing_class.status = ClassStatus.BOOKED

            # 设置会员卡信息（优先使用实际扣费的卡片）
            if used_card_id:
                existing_class.member_card_id = used_card_id
                # TODO: 可以从消费服务获取卡片名称
                existing_class.member_card_name = member_booking_data.member_card_name
            else:
                existing_class.member_card_id = member_booking_data.member_card_id
                existing_class.member_card_name = member_booking_data.member_card_name

            existing_class.material_name = member_booking_data.material_name
            existing_class.booking_remark = member_booking_data.booking_remark
            existing_class.operator_name = "会员自主预约"  # 标识是会员自主预约
            existing_class.updated_at = datetime.now()

            self.session.add(existing_class)
            
            # 统一提交事务（包含扣费和课程预约）
            self.session.commit()
            self.session.refresh(existing_class)

            return existing_class
            
        except Exception as e:
            # 发生任何错误时回滚事务
            self.session.rollback()
            raise

    def book_class(self, class_id: int, member_id: int, booking_data: Optional[MemberClassBooking] = None,
                operator_name: Optional[str] = None) -> ScheduledClass:
        """管理员代为预约课程 - 管理员指定会员预约课程"""
        scheduled_class = self.session.get(ScheduledClass, class_id)
        if not scheduled_class:
            raise ScheduledClassNotFoundError(class_id)

        # 检查课程状态
        if scheduled_class.status != ClassStatus.AVAILABLE:
            raise ScheduledClassBusinessException.class_not_available(
                class_id,
                scheduled_class.status.value
            )

        # 检查课程是否对会员可见（管理员预约时可以跳过此检查）
        if not scheduled_class.is_visible_to_member:
            raise ScheduledClassBusinessException.class_not_visible(class_id)

        # 验证预约时间（管理员预约时可以放宽限制）
        booking_errors = ScheduledClassValidator.validate_booking_time(
            scheduled_class.class_datetime
        )
        if booking_errors:
            raise ScheduledClassBusinessException.booking_deadline_passed(
                scheduled_class.class_datetime
            )

        # 检查会员时间冲突
        if self._check_member_time_conflict(
            member_id,
            scheduled_class.class_datetime,
            scheduled_class.duration_minutes,
            exclude_class_id=class_id
        ):
            raise ScheduledClassBusinessException.member_time_conflict(
                member_id,
                scheduled_class.class_datetime
            )

        # 开始事务处理
        try:
            # 会员卡余额验证和扣费（不自动提交事务）
            used_card_id = None
            if scheduled_class.price and scheduled_class.price > 0:
                used_card_id = self._validate_and_deduct_member_card_balance(
                    member_id=member_id,
                    course_price=scheduled_class.price,
                    scheduled_class_id=scheduled_class.id,
                    preferred_card_id=booking_data.member_card_id if booking_data else None
                )

            # 更新课程信息
            scheduled_class.member_id = member_id
            scheduled_class.status = ClassStatus.BOOKED

            # 设置会员卡信息（优先使用实际扣费的卡片）
            if used_card_id:
                scheduled_class.member_card_id = used_card_id
                # TODO: 可以从消费服务获取卡片名称
                scheduled_class.member_card_name = booking_data.member_card_name if booking_data else None
            elif booking_data:
                scheduled_class.member_card_id = booking_data.member_card_id
                scheduled_class.member_card_name = booking_data.member_card_name

            # 设置预约备注和操作员信息
            if booking_data and booking_data.booking_remark:
                scheduled_class.booking_remark = booking_data.booking_remark
            else:
                scheduled_class.booking_remark = "管理员代为预约"
            
            # 设置操作员信息（标识是管理员代为预约）
            scheduled_class.operator_name = operator_name
            scheduled_class.updated_at = datetime.now()

            self.session.add(scheduled_class)
            
            # 统一提交事务（包含扣费和课程预约）
            self.session.commit()
            self.session.refresh(scheduled_class)

            return scheduled_class
            
        except Exception as e:
            # 发生任何错误时回滚事务
            self.session.rollback()
            raise

    def create_admin_class(self, admin_class_data: AdminClassCreate,
                         created_by: Optional[int] = None) -> ScheduledClass:
        """管理员版课程创建 - 管理员全权限操作"""
        # 转换为统一的ScheduledClassCreate
        scheduled_class_data = ScheduledClassCreate(
            teacher_id=admin_class_data.teacher_id,
            member_id=admin_class_data.member_id,
            class_datetime=admin_class_data.class_datetime,
            duration_minutes=admin_class_data.duration_minutes,
            class_type=admin_class_data.class_type,
            price=admin_class_data.price,
            member_card_id=admin_class_data.member_card_id,
            member_card_name=admin_class_data.member_card_name,
            booking_remark=admin_class_data.booking_remark,
            member_no_cancel=admin_class_data.member_no_cancel,
            material_id=admin_class_data.material_id,
            material_name=admin_class_data.material_name,
            is_visible_to_member=admin_class_data.is_visible_to_member,
            operator_name=admin_class_data.operator_name or "管理员操作"
        )

        # 调用统一的创建方法
        return self.create_scheduled_class(scheduled_class_data, created_by)

    def batch_create_teacher_classes(self, teacher_id: int, class_datetimes: List[datetime],
                                   duration_minutes: int = 25, price: Optional[int] = None,
                                   material_name: Optional[str] = None,
                                   is_visible_to_member: bool = True,
                                   created_by: Optional[int] = None) -> List[ScheduledClass]:
        """批量创建教师课程"""
        created_classes = []
        failed_times = []

        for class_datetime in class_datetimes:
            try:
                teacher_class_data = TeacherClassCreate(
                    class_datetime=class_datetime,
                    duration_minutes=duration_minutes,
                    price=price,
                    material_name=material_name,
                    is_visible_to_member=is_visible_to_member
                )

                scheduled_class = self.create_teacher_class(teacher_id, teacher_class_data, created_by)
                created_classes.append(scheduled_class)

            except Exception as e:
                error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}"
                failed_times.append((class_datetime, error_msg))
                continue

        return created_classes

    # ==================== 固定约课排课功能 ====================

    def create_fixed_schedule_class(self, fixed_class_data: FixedScheduleClassCreate,
                                  created_by: Optional[int] = None) -> ScheduledClass:
        """创建固定约课排课 - 用于固定约课扣费时排课"""
        # 转换为统一的ScheduledClassCreate
        scheduled_class_data = ScheduledClassCreate(
            teacher_id=fixed_class_data.teacher_id,
            member_id=fixed_class_data.member_id,  # 固定约课必须有会员
            class_datetime=fixed_class_data.class_datetime,
            duration_minutes=fixed_class_data.duration_minutes,
            class_type=ClassType.FIXED,  # 设置为固定约课类型
            price=fixed_class_data.price,
            member_card_id=fixed_class_data.member_card_id,
            member_card_name=fixed_class_data.member_card_name,
            booking_remark=fixed_class_data.booking_remark,
            member_no_cancel=fixed_class_data.member_no_cancel,
            # 教材信息为空，由固定约课系统管理
            material_id=None,
            material_name=None,
            is_visible_to_member=fixed_class_data.is_visible_to_member,
            operator_name="固定约课排课"  # 标识操作来源
        )

        # 调用统一的创建方法
        return self.create_scheduled_class(scheduled_class_data, created_by)

    def batch_create_fixed_schedule_classes(self, batch_data: BatchFixedScheduleCreate,
                                          created_by: Optional[int] = None) -> List[ScheduledClass]:
        """批量创建固定约课排课"""
        created_classes = []
        failed_times = []

        for class_datetime in batch_data.class_datetimes:
            try:
                fixed_class_data = FixedScheduleClassCreate(
                    teacher_id=batch_data.teacher_id,
                    member_id=batch_data.member_id,
                    class_datetime=class_datetime,
                    duration_minutes=batch_data.duration_minutes,
                    price=batch_data.price,
                    member_card_id=batch_data.member_card_id,
                    member_card_name=batch_data.member_card_name,
                    booking_remark=batch_data.booking_remark,
                    member_no_cancel=batch_data.member_no_cancel,
                    is_visible_to_member=batch_data.is_visible_to_member
                )

                scheduled_class = self.create_fixed_schedule_class(fixed_class_data, created_by)
                created_classes.append(scheduled_class)

            except Exception as e:
                failed_times.append((class_datetime, str(e)))
                continue

        return created_classes

    # ==================== 课程预约和取消功能 ====================

    def _validate_and_deduct_member_card_balance(self, member_id: int, course_price: int,
                                               scheduled_class_id: int,
                                               preferred_card_id: Optional[int] = None) -> Optional[int]:
        """验证会员卡余额并执行扣费，返回使用的会员卡ID
        
        注意：此方法不会自动提交事务，需要外部控制事务提交
        """
        # 如果课程价格为0或None，跳过余额验证和扣费
        if not course_price or course_price <= 0:
            return None

        try:
            # 创建消费服务实例
            consumption_service = ConsumptionService(self.session, self.tenant_id)

            # 检查课程预约余额
            balance_check = CourseBookingBalanceCheck(
                member_id=member_id,
                course_price=course_price,
                preferred_card_id=preferred_card_id
            )

            balance_response = consumption_service.check_course_booking_balance(balance_check)

            # 如果余额不足，抛出业务异常
            if not balance_response.can_book:
                if not balance_response.available_cards:
                    # 如果没有可用会员卡，抛出异常
                    raise BusinessException(f"会员 {member_id} 没有可用的会员卡")
                else:
                    raise BusinessException(
                        f"会员卡余额不足，需要 {course_price} 元，缺少 {balance_response.shortage_amount} 元"
                    )

            # 余额充足，执行扣费（不自动提交事务）
            from ..member_cards.schemas import ConsumptionRequest
            consumption_request = ConsumptionRequest(
                member_card_id=balance_response.selected_card_id,
                amount=course_price,
                operation_description=f"课程预约扣费 - 课程ID: {scheduled_class_id}",
                reason=f"课程预约自动扣费",
                scheduled_class_id=scheduled_class_id
            )

            # 使用不自动提交事务的方法
            consumption_response, operation = consumption_service.consume_without_commit(
                consumption_request,
                operator_id=None  # 系统自动扣费，无操作员
            )

            return balance_response.selected_card_id

        except (MemberCardNotFoundError, MemberCardBusinessException) as e:
            # 将会员卡异常转换为统一业务异常
            raise BusinessException(f"会员卡操作失败：{str(e)}")

    def _process_course_cancellation_refund(self, scheduled_class: ScheduledClass,
                                          cancellation_reason: str,
                                          operator_name: Optional[str] = None) -> None:
        """处理课程取消时的退费逻辑（不提交事务）"""
        try:
            # 创建消费服务实例
            consumption_service = ConsumptionService(self.session, self.tenant_id)

            # 查找原始扣费操作记录
            from ..member_cards.models import MemberCardOperation, MemberCardOperationType
            original_operation = self.session.exec(
                select(MemberCardOperation).where(
                    and_(
                        MemberCardOperation.tenant_id == self.tenant_id,
                        MemberCardOperation.member_card_id == scheduled_class.member_card_id,
                        MemberCardOperation.scheduled_class_id == scheduled_class.id,
                        MemberCardOperation.operation_type.in_([
                            MemberCardOperationType.DIRECT_BOOKING,
                            MemberCardOperationType.ADMIN_BOOKING
                        ]),
                        MemberCardOperation.amount_change < 0  # 扣费记录是负数
                    )
                ).order_by(MemberCardOperation.created_at.desc())
            ).first()

            # 确定退费金额和原始操作ID
            if original_operation:
                # 找到原始扣费记录，使用记录中的金额
                refund_amount = abs(original_operation.amount_change)
                original_operation_id = original_operation.id
            else:
                # 没有找到原始扣费记录，使用课程价格作为退费金额
                refund_amount = scheduled_class.price
                original_operation_id = None
                # TODO: 添加警告日志 - 没有找到原始扣费记录，使用课程价格退费
                # logger.warning(f"课程取消退费：没有找到课程ID {scheduled_class.id} 的原始扣费记录，使用课程价格 {refund_amount} 元进行退费")

            # 执行退费（不提交事务）
            refund_reason = f"课程取消退费：{cancellation_reason}，课程时间: {scheduled_class.class_datetime}"
            refund_response, refund_operation = consumption_service.refund_without_commit(
                original_operation_id=original_operation_id,
                member_card_id=scheduled_class.member_card_id,
                refund_amount=refund_amount,
                reason=refund_reason,
                scheduled_class_id=scheduled_class.id,
                operator_id=None  # 系统自动退费，无操作员
            )

            # TODO: 添加退费成功日志
            # logger.info(f"课程取消退费成功：课程ID {scheduled_class.id}，退费金额 {refund_amount} 元，操作记录ID {refund_operation.id}")

        except (MemberCardNotFoundError, MemberCardBusinessException) as e:
            # 退费失败，记录错误但不影响课程取消
            # TODO: 添加错误日志记录或通知机制
            # logger.error(f"课程取消退费失败：课程ID {scheduled_class.id}，错误信息: {str(e)}")
            pass
        except Exception as e:
            # 其他异常也不影响课程取消
            # TODO: 添加错误日志记录
            # logger.error(f"课程取消退费异常：课程ID {scheduled_class.id}，异常信息: {str(e)}")
            pass

    def cancel_booking(self, class_id: int, cancellation_data: Optional[ClassCancellation] = None,
                      operator_name: Optional[str] = None) -> ScheduledClass:
        """取消课程预约"""
        scheduled_class = self.session.get(ScheduledClass, class_id)
        if not scheduled_class:
            raise ScheduledClassNotFoundError(class_id)

        # 检查课程状态
        if scheduled_class.status != ClassStatus.BOOKED:
            raise ScheduledClassBusinessException.class_not_available(
                class_id,
                scheduled_class.status.value
            )

        # 验证取消时间
        cancellation_errors = ScheduledClassValidator.validate_cancellation_time(
            scheduled_class.class_datetime
        )
        print("cancellation_errors:", cancellation_errors)
        if cancellation_errors:
            raise ScheduledClassBusinessException.cancellation_deadline_passed(
                scheduled_class.class_datetime
            )

        # 检查是否允许会员取消
        if scheduled_class.member_no_cancel:
            raise ScheduledClassBusinessException.member_cannot_cancel(class_id)

        # 开始事务处理
        try:
            # 处理退费逻辑（在更新课程信息之前，不自动提交事务）
            if (scheduled_class.price and scheduled_class.price > 0 and
                scheduled_class.member_card_id and scheduled_class.member_id):
                self._process_course_cancellation_refund(
                    scheduled_class,
                    cancellation_data.cancellation_reason if cancellation_data else "课程取消",
                    operator_name
                )

            # 更新课程信息
            scheduled_class.member_id = None
            scheduled_class.status = ClassStatus.AVAILABLE
            scheduled_class.member_card_id = None
            scheduled_class.member_card_name = None

            if cancellation_data:
                scheduled_class.booking_remark = cancellation_data.cancellation_reason
            else:
                scheduled_class.booking_remark = None

            scheduled_class.operator_name = operator_name
            scheduled_class.updated_at = datetime.now()

            self.session.add(scheduled_class)

            # 统一提交事务（包含退费和课程取消）
            self.session.commit()
            self.session.refresh(scheduled_class)

            return scheduled_class

        except Exception as e:
            # 发生任何错误时回滚事务
            self.session.rollback()
            raise

    def update_class_status(self, class_id: int, status_data: ClassStatusUpdate,
                          operator_name: Optional[str] = None) -> ScheduledClass:
        """更新课程状态"""
        scheduled_class = self.session.get(ScheduledClass, class_id)
        if not scheduled_class:
            raise ScheduledClassNotFoundError(class_id)

        # 验证状态转换
        if not ScheduledClassValidator.validate_status_transition(
            scheduled_class.status,
            status_data.status
        ):
            raise ScheduledClassBusinessException.invalid_status_transition(
                scheduled_class.status.value,
                status_data.status.value
            )

        # 记录原状态，用于判断是否需要扣费
        old_status = scheduled_class.status

        # 更新状态
        scheduled_class.status = status_data.status
        scheduled_class.operator_name = status_data.operator_name or operator_name

        # 如果有备注，更新备注
        if status_data.remark:
            scheduled_class.booking_remark = status_data.remark

        scheduled_class.updated_at = datetime.now()

        self.session.add(scheduled_class)
        self.session.commit()
        self.session.refresh(scheduled_class)

        return scheduled_class

    def batch_book_classes(self, member_id: int, class_ids: List[int],
                          operator_name: Optional[str] = None) -> BatchBookResult:
        """批量预约课程 - 给同一个会员批量预约多个课程
        
        Args:
            member_id: 会员ID
            class_ids: 要预约的课程ID列表
            operator_name: 操作员名称（管理员代为预约时使用）
        
        Returns:
            BatchBookResult: 包含成功和失败信息的结果模型
        """
        
        booked_classes = []
        failed_bookings = []

        for class_id in class_ids:
            try:
                # 使用管理员代为预约方法
                booked_class = self.book_class(class_id, member_id, operator_name=operator_name)
                booked_classes.append(booked_class)
            except Exception as e:
                error_msg = str(e) if str(e) else f"{type(e).__name__}"
                failed_bookings.append({
                    "class_id": class_id,
                    "member_id": member_id,
                    "error": error_msg
                })
                continue

        return BatchBookResult(
            success_count=len(booked_classes),
            failed_count=len(failed_bookings),
            booked_classes=[
                ScheduledClassRead.model_validate(cls)
                for cls in booked_classes
            ],
            failed_bookings=failed_bookings
        )

    def batch_cancel_bookings(self, class_ids: List[int],
                            operator_name: Optional[str] = None) -> BatchCancelResult:
        """批量取消预约
        
        Returns:
            BatchCancelResult: 包含成功和失败信息的结果模型
        """
        
        cancelled_classes = []
        failed_cancellations = []

        for class_id in class_ids:
            try:
                cancelled_class = self.cancel_booking(class_id, operator_name=operator_name)
                cancelled_classes.append(cancelled_class)
            except Exception as e:
                error_msg = str(e) if str(e) else f"{type(e).__name__}"
                failed_cancellations.append({
                    "class_id": class_id,
                    "error": error_msg
                })
                continue

        return BatchCancelResult(
            success_count=len(cancelled_classes),
            failed_count=len(failed_cancellations),
            cancelled_classes=[
                ScheduledClassRead.model_validate(cls)
                for cls in cancelled_classes
            ],
            failed_cancellations=failed_cancellations
        )

    def batch_book_member_classes(self, member_id: int, 
                                 member_booking_list: List[MemberClassBooking],
                                 created_by: Optional[int] = None) -> BatchBookResult:
        """会员批量自主预约课程 - 会员一次性预约多个课程
        
        Args:
            member_id: 会员ID
            member_booking_list: 会员预约数据列表
            created_by: 创建者ID
        
        Returns:
            BatchBookResult: 包含成功和失败信息的结果模型
        """
        
        booked_classes = []
        failed_bookings = []

        for booking_data in member_booking_list:
            try:
                # 使用会员自主预约方法
                booked_class = self.book_member_class(member_id, booking_data, created_by)
                booked_classes.append(booked_class)
            except Exception as e:
                error_msg = str(e) if str(e) else f"{type(e).__name__}"
                failed_bookings.append({
                    "class_id": booking_data.class_id,
                    "member_id": member_id,
                    "error": error_msg
                })
                continue

        return BatchBookResult(
            success_count=len(booked_classes),
            failed_count=len(failed_bookings),
            booked_classes=[
                ScheduledClassRead.model_validate(cls)
                for cls in booked_classes
            ],
            failed_bookings=failed_bookings
        )

    # ==================== 时间冲突检测功能 ====================

    def check_time_conflict(self, teacher_id: int, member_id: Optional[int],
                          class_datetime: datetime, duration_minutes: int,
                          exclude_class_id: Optional[int] = None) -> dict:
        """检查时间冲突（公开API）"""
        from datetime import timedelta

        result = {
            "has_conflict": False,
            "teacher_conflict": False,
            "member_conflict": False,
            "conflict_details": []
        }

        # 检查教师时间冲突
        teacher_conflicts = self._get_teacher_conflicts(
            teacher_id, class_datetime, duration_minutes, exclude_class_id
        )
        if teacher_conflicts:
            result["teacher_conflict"] = True
            result["has_conflict"] = True
            for conflict in teacher_conflicts:
                result["conflict_details"].append(
                    f"教师在{conflict.class_datetime.strftime('%Y-%m-%d %H:%M')}已有课程安排"
                )

        # 检查会员时间冲突
        if member_id:
            member_conflicts = self._get_member_conflicts(
                member_id, class_datetime, duration_minutes, exclude_class_id
            )
            if member_conflicts:
                result["member_conflict"] = True
                result["has_conflict"] = True
                for conflict in member_conflicts:
                    result["conflict_details"].append(
                        f"会员在{conflict.class_datetime.strftime('%Y-%m-%d %H:%M')}已有课程安排"
                    )

        return result

    def batch_check_time_conflicts(self, conflicts_to_check: List[dict]) -> List[dict]:
        """批量检查时间冲突"""
        results = []

        for check_data in conflicts_to_check:
            try:
                result = self.check_time_conflict(
                    teacher_id=check_data.get("teacher_id"),
                    member_id=check_data.get("member_id"),
                    class_datetime=check_data.get("class_datetime"),
                    duration_minutes=check_data.get("duration_minutes", 25),
                    exclude_class_id=check_data.get("exclude_class_id")
                )
                result["check_id"] = check_data.get("check_id")  # 用于标识检查项
                results.append(result)
            except Exception as e:
                results.append({
                    "check_id": check_data.get("check_id"),
                    "has_conflict": True,
                    "error": str(e)
                })

        return results

    def get_teacher_schedule(self, teacher_id: int, date_from: datetime, date_to: datetime) -> List[dict]:
        """获取教师的时间安排（用于冲突检测可视化）"""
        statement = select(ScheduledClass).where(
            and_(
                ScheduledClass.teacher_id == teacher_id,
                ScheduledClass.is_deleted == False,
                ScheduledClass.status.in_([ClassStatus.BOOKED]),
                ScheduledClass.class_datetime >= date_from,
                ScheduledClass.class_datetime <= date_to
            )
        ).order_by(ScheduledClass.class_datetime.asc())

        classes = self.session.exec(statement).all()

        schedule = []
        for cls in classes:
            from datetime import timedelta
            end_time = cls.class_datetime + timedelta(minutes=cls.duration_minutes)
            schedule.append({
                "class_id": cls.id,
                "start_time": cls.class_datetime,
                "end_time": end_time,
                "duration_minutes": cls.duration_minutes,
                "status": cls.status.value,
                "member_id": cls.member_id
            })

        return schedule

    def get_member_schedule(self, member_id: int, date_from: datetime, date_to: datetime) -> List[dict]:
        """获取会员的时间安排（用于冲突检测可视化）"""
        statement = select(ScheduledClass).where(
            and_(
                ScheduledClass.member_id == member_id,
                ScheduledClass.is_deleted == False,
                ScheduledClass.status.in_([ClassStatus.BOOKED]),
                ScheduledClass.class_datetime >= date_from,
                ScheduledClass.class_datetime <= date_to
            )
        ).order_by(ScheduledClass.class_datetime.asc())

        classes = self.session.exec(statement).all()

        schedule = []
        for cls in classes:
            from datetime import timedelta
            end_time = cls.class_datetime + timedelta(minutes=cls.duration_minutes)
            schedule.append({
                "class_id": cls.id,
                "start_time": cls.class_datetime,
                "end_time": end_time,
                "duration_minutes": cls.duration_minutes,
                "status": cls.status.value,
                "teacher_id": cls.teacher_id
            })

        return schedule

    def find_available_time_slots(self, teacher_id: int, date_from: datetime, date_to: datetime,
                                duration_minutes: int = 25, interval_minutes: int = 5) -> List[datetime]:
        """查找教师的可用时间段"""
        from datetime import timedelta

        # 获取教师的现有安排
        teacher_schedule = self.get_teacher_schedule(teacher_id, date_from, date_to)

        available_slots = []
        current_time = date_from

        while current_time + timedelta(minutes=duration_minutes) <= date_to:
            # 检查当前时间段是否与现有安排冲突
            is_available = True
            proposed_end_time = current_time + timedelta(minutes=duration_minutes)

            for scheduled in teacher_schedule:
                # 检查时间重叠
                if (current_time < scheduled["end_time"] and
                    proposed_end_time > scheduled["start_time"]):
                    is_available = False
                    break

            if is_available:
                # 检查时间是否在合理的营业时间内
                time_errors = ScheduledClassValidator.validate_class_time(current_time, duration_minutes)
                if not time_errors:
                    available_slots.append(current_time)

            current_time += timedelta(minutes=interval_minutes)

        return available_slots

    def suggest_alternative_times(self, teacher_id: int, original_datetime: datetime,
                                duration_minutes: int = 25, search_days: int = 7) -> List[datetime]:
        """建议替代时间（当原时间冲突时）"""
        from datetime import timedelta, date

        # 搜索范围：原时间前后几天
        search_start = original_datetime - timedelta(days=search_days)
        search_end = original_datetime + timedelta(days=search_days)

        # 获取可用时间段
        available_slots = self.find_available_time_slots(
            teacher_id, search_start, search_end, duration_minutes
        )

        # 按与原时间的接近程度排序
        available_slots.sort(key=lambda x: abs((x - original_datetime).total_seconds()))

        # 返回前10个最接近的时间
        return available_slots[:10]

    # ==================== 私有辅助方法 ====================

    def _get_teacher_conflicts(self, teacher_id: int, class_datetime: datetime,
                             duration_minutes: int, exclude_class_id: Optional[int] = None) -> List[ScheduledClass]:
        """获取教师时间冲突的课程列表"""
        from datetime import timedelta

        end_time = class_datetime + timedelta(minutes=duration_minutes)

        statement = select(ScheduledClass).where(
            and_(
                ScheduledClass.teacher_id == teacher_id,
                ScheduledClass.is_deleted == False,
                ScheduledClass.status.in_([ClassStatus.BOOKED]),
                or_(
                    # 新课程开始时间在现有课程时间范围内
                    and_(
                        ScheduledClass.class_datetime <= class_datetime,
                        ScheduledClass.class_datetime + text("INTERVAL '1 minute' * scheduled_classes.duration_minutes") > class_datetime
                    ),
                    # 新课程结束时间在现有课程时间范围内
                    and_(
                        ScheduledClass.class_datetime < end_time,
                        ScheduledClass.class_datetime + text("INTERVAL '1 minute' * scheduled_classes.duration_minutes") >= end_time
                    ),
                    # 新课程完全包含现有课程
                    and_(
                        ScheduledClass.class_datetime >= class_datetime,
                        ScheduledClass.class_datetime + text("INTERVAL '1 minute' * scheduled_classes.duration_minutes") <= end_time
                    )
                )
            )
        )

        if exclude_class_id:
            statement = statement.where(ScheduledClass.id != exclude_class_id)

        return self.session.exec(statement).all()

    def _get_member_conflicts(self, member_id: int, class_datetime: datetime,
                            duration_minutes: int, exclude_class_id: Optional[int] = None) -> List[ScheduledClass]:
        """获取会员时间冲突的课程列表"""
        from datetime import timedelta

        end_time = class_datetime + timedelta(minutes=duration_minutes)

        statement = select(ScheduledClass).where(
            and_(
                ScheduledClass.member_id == member_id,
                ScheduledClass.is_deleted == False,
                ScheduledClass.status.in_([ClassStatus.BOOKED]),
                or_(
                    # 新课程开始时间在现有课程时间范围内
                    and_(
                        ScheduledClass.class_datetime <= class_datetime,
                        ScheduledClass.class_datetime + text("INTERVAL '1 minute' * scheduled_classes.duration_minutes") > class_datetime
                    ),
                    # 新课程结束时间在现有课程时间范围内
                    and_(
                        ScheduledClass.class_datetime < end_time,
                        ScheduledClass.class_datetime + text("INTERVAL '1 minute' * scheduled_classes.duration_minutes") >= end_time
                    ),
                    # 新课程完全包含现有课程
                    and_(
                        ScheduledClass.class_datetime >= class_datetime,
                        ScheduledClass.class_datetime + text("INTERVAL '1 minute' * scheduled_classes.duration_minutes") <= end_time
                    )
                )
            )
        )

        if exclude_class_id:
            statement = statement.where(ScheduledClass.id != exclude_class_id)

        return self.session.exec(statement).all()

    def _check_teacher_time_conflict(self, teacher_id: int, class_datetime: datetime,
                                   duration_minutes: int, exclude_class_id: Optional[int] = None) -> bool:
        """检查教师时间冲突"""
        conflicts = self._get_teacher_conflicts(teacher_id, class_datetime, duration_minutes, exclude_class_id)
        return len(conflicts) > 0
    
    def _check_member_time_conflict(self, member_id: int, class_datetime: datetime,
                                  duration_minutes: int, exclude_class_id: Optional[int] = None) -> bool:
        """检查会员时间冲突"""
        conflicts = self._get_member_conflicts(member_id, class_datetime, duration_minutes, exclude_class_id)
        return len(conflicts) > 0


def get_scheduled_class_service(session: Session, tenant_id: int) -> ScheduledClassService:
    """获取已排课表服务实例"""
    return ScheduledClassService(session, tenant_id)
