from sqlmodel import SQLModel, Field, Index, UniqueConstraint
from typing import Optional
from datetime import datetime, timezone
from enum import Enum


class ClassType(str, Enum):
    """课程类型枚举"""
    FIXED = "fixed"      # 固定约课
    DIRECT = "direct"    # 直接约课


class ClassStatus(str, Enum):
    """课程状态枚举"""
    AVAILABLE = "available"           # 可预约（空课）
    BOOKED = "booked"                # 已预约
    TEACHER_NO_SHOW = "teacher_no_show"  # 教师缺席
    MEMBER_NO_SHOW = "member_no_show"    # 会员缺席
    # 注意：没有cancelled状态，取消后变回available


class ScheduledClassBase(SQLModel):
    """已排课表基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    
    # 基础信息
    teacher_id: int = Field(foreign_key="teachers.id", description="教师ID")
    member_id: Optional[int] = Field(default=None, foreign_key="members.id", description="会员ID（可为空，未预约状态）")
    
    # 时间信息
    class_datetime: datetime = Field(description="精确到分钟的上课时间")
    duration_minutes: int = Field(default=25, ge=5, le=120, description="课程时长（分钟）")
    
    # 课程信息
    class_type: ClassType = Field(default=ClassType.DIRECT, description="课程类型")
    price: Optional[int] = Field(default=None, ge=1, description="教师单价（整数，单位：元）")
    
    # 会员卡信息
    member_card_id: Optional[int] = Field(default=None, description="会员卡ID")
    member_card_name: Optional[str] = Field(default=None, max_length=100, description="会员卡名称（冗余字段，便于显示）")
    
    # 预约信息
    booking_remark: Optional[str] = Field(default=None, description="预约备注")
    member_no_cancel: bool = Field(default=False, description="会员是否可取消")
    
    # 教材信息（第一期使用字符串字段）
    material_id: Optional[int] = Field(default=None, description="预留教材ID（第二期使用）")
    material_name: Optional[str] = Field(default=None, max_length=100, description="教材名称（第一期主要使用此字段）")
    
    # 状态信息
    status: ClassStatus = Field(default=ClassStatus.AVAILABLE, description="课程状态")
    is_deleted: bool = Field(default=False, description="是否删除")
    
    # 可见性控制
    is_visible_to_member: bool = Field(default=True, description="是否对会员可见")
    
    # 操作信息
    operator_name: Optional[str] = Field(default=None, max_length=50, description="操作人显示名")


class ScheduledClass(ScheduledClassBase, table=True):
    """已排课表数据库模型"""
    __tablename__ = "scheduled_classes"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    created_by: Optional[int] = Field(default=None, foreign_key="users.id", description="创建者")
    
    __table_args__ = (
        # 约束设计
        # 确保同一教师在同一时间不能有多个课程（排除已删除的记录）
        UniqueConstraint('teacher_id', 'class_datetime', name='uq_teacher_class_datetime'),

        # 索引设计
        # 教师课程时间查询索引
        Index('idx_scheduled_classes_teacher_datetime', 'teacher_id', 'class_datetime'),
        # 会员课程时间查询索引
        Index('idx_scheduled_classes_member_datetime', 'member_id', 'class_datetime'),
        # 租户状态查询索引
        Index('idx_scheduled_classes_status', 'tenant_id', 'status'),
        # 可见性查询索引
        Index('idx_scheduled_classes_visible', 'is_visible_to_member', 'status'),
        # 课程时间查询索引
        Index('idx_scheduled_classes_datetime', 'class_datetime'),
        # 课程类型查询索引
        Index('idx_scheduled_classes_type', 'class_type', 'status'),
        # 租户课程查询索引
        Index('idx_scheduled_classes_tenant', 'tenant_id', 'id'),
        # 教师状态查询索引
        Index('idx_scheduled_classes_teacher_status', 'tenant_id', 'teacher_id', 'status'),
        # 会员状态查询索引
        Index('idx_scheduled_classes_member_status', 'tenant_id', 'member_id', 'status'),
        # 创建者查询索引
        Index('idx_scheduled_classes_created_by', 'tenant_id', 'created_by'),
        
        # 复合索引 - 优化多维度筛选查询
        Index('idx_scheduled_classes_teacher_time_status', 'teacher_id', 'class_datetime', 'status'),
        Index('idx_scheduled_classes_member_time_status', 'member_id', 'class_datetime', 'status'),
        Index('idx_scheduled_classes_tenant_datetime', 'tenant_id', 'class_datetime'),
        Index('idx_scheduled_classes_visible_datetime', 'is_visible_to_member', 'class_datetime'),
        Index('idx_scheduled_classes_type_datetime', 'class_type', 'class_datetime'),
        
        # 排序优化索引
        Index('idx_scheduled_classes_datetime_desc', 'tenant_id', 'class_datetime', 'id'),
        Index('idx_scheduled_classes_created_desc', 'tenant_id', 'created_at', 'id'),
    )
