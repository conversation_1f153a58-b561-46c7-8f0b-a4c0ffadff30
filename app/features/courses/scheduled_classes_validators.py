"""已排课表验证器模块"""

from typing import Optional, List
from datetime import datetime, timedelta
from .scheduled_classes_models import ClassStatus, ClassType


class ScheduledClassValidator:
    """已排课表验证器"""
    
    @staticmethod
    def validate_status_transition(current_status: ClassStatus, new_status: ClassStatus) -> bool:
        """
        验证课程状态转换是否合法
        
        Args:
            current_status: 当前状态
            new_status: 新状态
            
        Returns:
            bool: 是否允许转换
        """
        # 定义允许的状态转换规则
        allowed_transitions = {
            ClassStatus.AVAILABLE: [
                ClassStatus.BOOKED,
                ClassStatus.TEACHER_NO_SHOW,
                ClassStatus.MEMBER_NO_SHOW
            ],
            ClassStatus.BOOKED: [
                ClassStatus.AVAILABLE,  # 取消预约
                ClassStatus.TEACHER_NO_SHOW,
                ClassStatus.MEMBER_NO_SHOW
            ],
            ClassStatus.TEACHER_NO_SHOW: [
                ClassStatus.AVAILABLE,  # 重新开放
            ],
            ClassStatus.MEMBER_NO_SHOW: [
                ClassStatus.AVAILABLE,  # 重新开放
            ]
        }
        
        # 相同状态总是允许的
        if current_status == new_status:
            return True
            
        # 检查是否在允许的转换列表中
        return new_status in allowed_transitions.get(current_status, [])
    
    @staticmethod
    def validate_class_time(class_datetime: datetime, duration_minutes: int = 25) -> List[str]:
        """
        验证课程时间是否合理
        
        Args:
            class_datetime: 上课时间
            duration_minutes: 课程时长
            
        Returns:
            List[str]: 验证错误信息列表，空列表表示验证通过
        """
        errors = []
        
        # 检查是否是未来时间
        from datetime import timezone
        now = datetime.now()
        if class_datetime <= now:
            errors.append("上课时间必须是未来时间")
        
        # 检查是否太远的未来（比如超过1年）
        max_future_date = now + timedelta(days=365)
        if class_datetime > max_future_date:
            errors.append("上课时间不能超过一年后")
        
        # 检查课程时长是否合理
        if duration_minutes < 5:
            errors.append("课程时长不能少于5分钟")
        elif duration_minutes > 120:
            errors.append("课程时长不能超过120分钟")
        
        # 检查课程结束时间是否合理
        end_time = class_datetime + timedelta(minutes=duration_minutes)
        if end_time.hour >= 24:
            errors.append("课程结束时间不能超过24:00")
        
        return errors
    
    @staticmethod
    def validate_booking_time(class_datetime: datetime, booking_deadline_hours: int = 2) -> List[str]:
        """
        验证预约时间是否在允许范围内
        
        Args:
            class_datetime: 上课时间
            booking_deadline_hours: 预约截止时间（小时）
            
        Returns:
            List[str]: 验证错误信息列表
        """
        errors = []
        
        # 计算预约截止时间
        deadline = class_datetime - timedelta(hours=booking_deadline_hours)

        from datetime import timezone
        now = datetime.now()

        if now > deadline:
            errors.append(f"预约时间已过，需要在上课前{booking_deadline_hours}小时预约")
        
        return errors
    
    @staticmethod
    def validate_cancellation_time(class_datetime: datetime, cancel_deadline_hours: int = 2) -> List[str]:
        """
        验证取消时间是否在允许范围内
        
        Args:
            class_datetime: 上课时间
            cancel_deadline_hours: 取消截止时间（小时）
            
        Returns:
            List[str]: 验证错误信息列表
        """
        errors = []
        
        # 计算取消截止时间
        deadline = class_datetime - timedelta(hours=cancel_deadline_hours)

        from datetime import timezone
        now = datetime.now()

        if now > deadline:
            errors.append(f"取消时间已过，需要在上课前{cancel_deadline_hours}小时取消")
        
        return errors
    
    @staticmethod
    def validate_class_type_and_status(class_type: ClassType, status: ClassStatus) -> List[str]:
        """
        验证课程类型和状态的组合是否合理

        Args:
            class_type: 课程类型
            status: 课程状态

        Returns:
            List[str]: 验证错误信息列表
        """
        errors = []

        # 目前所有类型的课程都支持所有状态，暂无特殊限制
        # 这里可以根据业务需求添加特定的验证规则
        # 使用参数避免警告
        _ = class_type
        _ = status

        return errors
    
    @staticmethod
    def validate_member_booking(member_id: Optional[int], status: ClassStatus) -> List[str]:
        """
        验证会员预约信息
        
        Args:
            member_id: 会员ID
            status: 课程状态
            
        Returns:
            List[str]: 验证错误信息列表
        """
        errors = []
        
        # 如果状态是已预约，必须有会员ID
        if status == ClassStatus.BOOKED and not member_id:
            errors.append("已预约状态必须指定会员")
        
        # 如果状态是可预约，不应该有会员ID
        if status == ClassStatus.AVAILABLE and member_id:
            errors.append("可预约状态不应该指定会员")
        
        return errors


def get_status_display_name(status: ClassStatus) -> str:
    """获取状态显示名称"""
    status_names = {
        ClassStatus.AVAILABLE: "可预约",
        ClassStatus.BOOKED: "已预约",
        ClassStatus.TEACHER_NO_SHOW: "教师缺席",
        ClassStatus.MEMBER_NO_SHOW: "会员缺席"
    }
    return status_names.get(status, str(status))


def get_class_type_display_name(class_type: ClassType) -> str:
    """获取课程类型显示名称"""
    type_names = {
        ClassType.FIXED: "固定约课",
        ClassType.DIRECT: "直接约课"
    }
    return type_names.get(class_type, str(class_type))


def format_class_datetime(class_datetime: datetime) -> str:
    """格式化课程时间显示"""
    return class_datetime.strftime("%Y-%m-%d %H:%M")
