"""
课程系统配置业务逻辑服务
"""
from typing import Optional
from sqlmodel import Session, select, text
from datetime import datetime, timezone

from .config_models import CourseSystemConfig
from .config_schemas import CourseSystemConfigCreate, CourseSystemConfigUpdate, CourseSystemConfigQuery
from .config_exceptions import CourseConfigNotFoundError, CourseConfigBusinessException
from .config_utils import CourseConfigValidator


class CourseSystemConfigService:
    """课程系统配置服务"""

    def __init__(self, session: Session, tenant_id: int):
        self.session = session
        self.tenant_id = tenant_id
        # 设置RLS上下文
        self.session.execute(text(f"SET app.current_tenant_id = '{tenant_id}'"))

    def create_config(self, config_data: CourseSystemConfigCreate, created_by: Optional[int] = None) -> CourseSystemConfig:
        """创建课程系统配置（内部使用，通常由系统在创建租户时调用）"""
        # 检查是否已存在配置（每个租户只能有一个配置）
        existing_config = self.get_config()
        if existing_config:
            # 如果已存在，返回现有配置而不是抛出异常
            return existing_config

        # 验证配置数据
        CourseConfigValidator.validate_config_data(config_data.model_dump())

        # 创建配置对象
        config_dict = config_data.model_dump(exclude_unset=True)
        config_dict['tenant_id'] = self.tenant_id
        config_dict['created_by'] = created_by

        config = CourseSystemConfig(**config_dict)
        
        # 设置创建时间和更新时间
        now = datetime.now()
        config.created_at = now
        config.updated_at = now

        self.session.add(config)
        self.session.commit()
        self.session.refresh(config)

        return config

    def get_config(self) -> Optional[CourseSystemConfig]:
        """获取课程系统配置（每个租户只有一个配置）"""
        statement = select(CourseSystemConfig).where(CourseSystemConfig.tenant_id == self.tenant_id)
        return self.session.exec(statement).first()

    def get_config_or_create_default(self, created_by: Optional[int] = None) -> CourseSystemConfig:
        """获取配置，如果不存在则创建默认配置"""
        config = self.get_config()
        if not config:
            # 创建默认配置
            default_config = CourseSystemConfigCreate()
            config = self.create_config(default_config, created_by)
        return config

    def ensure_config_exists(self, created_by: Optional[int] = None) -> CourseSystemConfig:
        """确保租户配置存在（系统级别调用，用于租户初始化）"""
        return self.get_config_or_create_default(created_by)

    @classmethod
    def initialize_tenant_config(cls, session: Session, tenant_id: int, created_by: Optional[int] = None) -> CourseSystemConfig:
        """为新租户初始化课程系统配置（系统级别调用）"""
        service = cls(session, tenant_id)
        return service.ensure_config_exists(created_by)

    def update_config(self, config_data: CourseSystemConfigUpdate, updated_by: Optional[int] = None) -> CourseSystemConfig:
        """更新课程系统配置"""
        config = self.get_config()
        if not config:
            raise CourseConfigNotFoundError()

        # 验证更新数据
        update_dict = config_data.model_dump(exclude_unset=True)
        if update_dict:
            # 合并当前配置和更新数据进行验证
            current_dict = config.model_dump()
            current_dict.update(update_dict)
            CourseConfigValidator.validate_config_data(current_dict)

            # 应用更新
            for field, value in update_dict.items():
                setattr(config, field, value)

            config.updated_at = datetime.now()
            if updated_by:
                config.updated_by = updated_by

            self.session.add(config)
            self.session.commit()
            self.session.refresh(config)

        return config

    def reset_config_to_default(self, reset_by: Optional[int] = None) -> CourseSystemConfig:
        """重置配置为默认值"""
        config = self.get_config()
        if not config:
            raise CourseConfigNotFoundError()

        # 获取默认配置值
        default_values = CourseConfigValidator.get_default_config()
        
        # 重置所有配置字段（保留ID、tenant_id、created_at、created_by）
        for field, value in default_values.items():
            if hasattr(config, field) and field not in ['id', 'tenant_id', 'created_at', 'created_by']:
                setattr(config, field, value)

        config.updated_at = datetime.now()
        if reset_by:
            config.updated_by = reset_by

        self.session.add(config)
        self.session.commit()
        self.session.refresh(config)

        return config

    def get_config_by_field(self, field_name: str):
        """获取指定配置字段的值"""
        # 验证字段名是否存在
        if not hasattr(CourseSystemConfig, field_name):
            raise CourseConfigBusinessException.invalid_field_name(field_name)
            
        config = self.get_config()
        if not config:
            # 返回默认值
            default_config = CourseConfigValidator.get_default_config()
            return default_config.get(field_name)
        
        return getattr(config, field_name, None)

    def update_config_field(self, field_name: str, field_value, updated_by: Optional[int] = None) -> CourseSystemConfig:
        """更新单个配置字段"""
        config = self.get_config()
        if not config:
            raise CourseConfigNotFoundError()

        # 验证字段名是否存在
        if not hasattr(config, field_name):
            raise CourseConfigBusinessException.invalid_field_name(field_name)

        # 验证字段值
        validation_data = {field_name: field_value}
        CourseConfigValidator.validate_config_data(validation_data)

        # 更新字段
        setattr(config, field_name, field_value)
        config.updated_at = datetime.now()
        if updated_by:
            config.updated_by = updated_by

        self.session.add(config)
        self.session.commit()
        self.session.refresh(config)

        return config

    def validate_config_consistency(self) -> bool:
        """验证配置一致性"""
        config = self.get_config()
        if not config:
            return False

        try:
            config_dict = config.model_dump()
            CourseConfigValidator.validate_config_data(config_dict)
            return True
        except Exception:
            return False

    def get_booking_time_range(self) -> dict:
        """获取约课时间范围"""
        config = self.get_config()
        if not config:
            default_config = CourseConfigValidator.get_default_config()
            return {
                "max_advance_days": default_config.get("max_advance_days", 30),
                "booking_deadline_hours": default_config.get("booking_deadline_hours", 2),
                "cancel_deadline_hours": default_config.get("cancel_deadline_hours", 2),
                "booking_time_from": default_config.get("booking_time_from"),
                "booking_time_to": default_config.get("booking_time_to")
            }
        
        return {
            "max_advance_days": config.max_advance_days,
            "booking_deadline_hours": config.booking_deadline_hours,
            "cancel_deadline_hours": config.cancel_deadline_hours,
            "booking_time_from": config.booking_time_from,
            "booking_time_to": config.booking_time_to
        }

    def is_direct_booking_enabled(self) -> bool:
        """检查是否启用直接约课"""
        config = self.get_config()
        if not config:
            return CourseConfigValidator.get_default_config().get("direct_booking_enabled", True)
        return config.direct_booking_enabled

    def is_fixed_booking_enabled(self) -> bool:
        """检查是否启用固定课表"""
        config = self.get_config()
        if not config:
            return CourseConfigValidator.get_default_config().get("fixed_booking_enabled", True)
        return config.fixed_booking_enabled

    def is_auto_schedule_enabled(self) -> bool:
        """检查是否启用自动排课"""
        config = self.get_config()
        if not config:
            return CourseConfigValidator.get_default_config().get("auto_schedule_enabled", True)
        return config.auto_schedule_enabled

    def get_teacher_permissions(self) -> dict:
        """获取教师权限配置"""
        config = self.get_config()
        if not config:
            default_config = CourseConfigValidator.get_default_config()
            return {
                "teacher_can_add_slots": default_config.get("teacher_can_add_slots", True),
                "teacher_can_delete_empty_slots": default_config.get("teacher_can_delete_empty_slots", True),
                "teacher_can_cancel_booking": default_config.get("teacher_can_cancel_booking", False),
                "teacher_need_confirm": default_config.get("teacher_need_confirm", False)
            }
        
        return {
            "teacher_can_add_slots": config.teacher_can_add_slots,
            "teacher_can_delete_empty_slots": config.teacher_can_delete_empty_slots,
            "teacher_can_cancel_booking": config.teacher_can_cancel_booking,
            "teacher_need_confirm": config.teacher_need_confirm
        }

    def get_schedule_config(self) -> dict:
        """获取排课配置"""
        config = self.get_config()
        if not config:
            default_config = CourseConfigValidator.get_default_config()
            return {
                "auto_schedule_enabled": default_config.get("auto_schedule_enabled", True),
                "auto_schedule_day": default_config.get("auto_schedule_day"),
                "auto_schedule_time": default_config.get("auto_schedule_time"),
                "default_schedule_weeks": default_config.get("default_schedule_weeks", 4),
                "interrupt_on_conflict": default_config.get("interrupt_on_conflict", True),
                "skip_insufficient_balance": default_config.get("skip_insufficient_balance", True),
                "remove_insufficient_locks": default_config.get("remove_insufficient_locks", True)
            }
        
        return {
            "auto_schedule_enabled": config.auto_schedule_enabled,
            "auto_schedule_day": config.auto_schedule_day,
            "auto_schedule_time": config.auto_schedule_time,
            "default_schedule_weeks": config.default_schedule_weeks,
            "interrupt_on_conflict": config.interrupt_on_conflict,
            "skip_insufficient_balance": config.skip_insufficient_balance,
            "remove_insufficient_locks": config.remove_insufficient_locks
        }


def get_course_config_service(session: Session, tenant_id: int) -> CourseSystemConfigService:
    """获取课程系统配置服务实例"""
    return CourseSystemConfigService(session, tenant_id) 