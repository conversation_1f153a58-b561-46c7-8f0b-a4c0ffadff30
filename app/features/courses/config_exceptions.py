"""课程系统配置模块异常处理"""

from app.api.common.exceptions import BusinessException, NotFoundError, ErrorLevel, BaseErrorCode
from typing import Optional


class CourseConfigErrorCode(BaseErrorCode):
    """课程配置模块错误码"""
    CONFIG_NOT_FOUND = "COURSE_CONFIG_NOT_FOUND"
    INVALID_TIME_RANGE = "COURSE_CONFIG_INVALID_TIME_RANGE"
    INVALID_DURATION = "COURSE_CONFIG_INVALID_DURATION"
    INVALID_INTERVAL = "COURSE_CONFIG_INVALID_INTERVAL"
    INVALID_ADVANCE_DAYS = "COURSE_CONFIG_INVALID_ADVANCE_DAYS"
    INVALID_DEADLINE_HOURS = "COURSE_CONFIG_INVALID_DEADLINE_HOURS"
    INVALID_SCHEDULE_DAY = "COURSE_CONFIG_INVALID_SCHEDULE_DAY"
    INVALID_SCHEDULE_WEEKS = "COURSE_CONFIG_INVALID_SCHEDULE_WEEKS"
    BOOKING_TIME_CONFLICT = "COURSE_CONFIG_BOOKING_TIME_CONFLICT"
    INVALID_CONFIG_VALUE = "COURSE_CONFIG_INVALID_VALUE"
    INVALID_FIELD_NAME = "COURSE_CONFIG_INVALID_FIELD_NAME"


class CourseConfigNotFoundError(NotFoundError):
    """课程配置不存在异常"""
    def __init__(self, tenant_id: Optional[int] = None):
        if tenant_id:
            super().__init__(f"租户 {tenant_id} 的课程系统配置")
        else:
            super().__init__("课程系统配置")


class CourseConfigBusinessException(BusinessException):
    """课程配置业务异常"""

    @classmethod
    def invalid_field_name(cls, field_name: str):
        """无效的字段名"""
        return cls(
            f"配置字段名 '{field_name}' 无效",
            CourseConfigErrorCode.INVALID_FIELD_NAME,
            ErrorLevel.WARNING
        )

    @classmethod
    def invalid_time_range(cls, start_time: str, end_time: str):
        """无效的时间范围"""
        return cls(
            f"预约时间范围无效：开始时间 {start_time} 不能晚于结束时间 {end_time}",
            CourseConfigErrorCode.INVALID_TIME_RANGE,
            ErrorLevel.WARNING
        )

    @classmethod
    def invalid_duration(cls, duration: int):
        """无效的课节时长"""
        return cls(
            f"课节时长 {duration} 分钟无效，必须在 5-120 分钟之间",
            CourseConfigErrorCode.INVALID_DURATION,
            ErrorLevel.WARNING
        )

    @classmethod
    def invalid_interval(cls, interval: int):
        """无效的课节间隔"""
        return cls(
            f"课节间隔 {interval} 分钟无效，必须在 0-30 分钟之间",
            CourseConfigErrorCode.INVALID_INTERVAL,
            ErrorLevel.WARNING
        )

    @classmethod
    def invalid_advance_days(cls, days: int):
        """无效的提前预约天数"""
        return cls(
            f"提前预约天数 {days} 无效，必须在 1-365 天之间",
            CourseConfigErrorCode.INVALID_ADVANCE_DAYS,
            ErrorLevel.WARNING
        )

    @classmethod
    def invalid_deadline_hours(cls, hours: int, deadline_type: str):
        """无效的截止时间"""
        return cls(
            f"{deadline_type}截止时间 {hours} 小时无效，必须在 0-72 小时之间",
            CourseConfigErrorCode.INVALID_DEADLINE_HOURS,
            ErrorLevel.WARNING
        )

    @classmethod
    def invalid_schedule_day(cls, day: int):
        """无效的排课日期"""
        return cls(
            f"自动排课日期 {day} 号无效，必须在 1-28 号之间",
            CourseConfigErrorCode.INVALID_SCHEDULE_DAY,
            ErrorLevel.WARNING
        )

    @classmethod
    def invalid_schedule_weeks(cls, weeks: int):
        """无效的排课周数"""
        return cls(
            f"排课周数 {weeks} 无效，必须在 1-12 周之间",
            CourseConfigErrorCode.INVALID_SCHEDULE_WEEKS,
            ErrorLevel.WARNING
        )

    @classmethod
    def booking_time_conflict(cls):
        """预约时间冲突"""
        return cls(
            "预约时间限制设置冲突：开始时间不能晚于结束时间",
            CourseConfigErrorCode.BOOKING_TIME_CONFLICT,
            ErrorLevel.WARNING
        )

    @classmethod
    def invalid_config_value(cls, field_name: str, value: str):
        """无效的配置值"""
        return cls(
            f"配置项 '{field_name}' 的值 '{value}' 无效",
            CourseConfigErrorCode.INVALID_CONFIG_VALUE,
            ErrorLevel.WARNING
        )

    @classmethod
    def general_error(cls, message: str):
        """通用课程配置业务错误"""
        return cls(message) 