from sqlmodel import SQLModel, Field
from typing import Optional, List
from datetime import datetime, date, time
from pydantic import field_validator
from .scheduled_classes_models import ScheduledClassBase, ClassType, ClassStatus
from pydantic import BaseModel, ConfigDict


# API请求模型
class ScheduledClassCreate(SQLModel):
    """创建已排课表请求模型"""
    teacher_id: int = Field(gt=0, description="教师ID")
    member_id: Optional[int] = Field(default=None, gt=0, description="会员ID（可为空，未预约状态）")
    
    # 时间信息
    class_datetime: datetime = Field(description="精确到分钟的上课时间")
    duration_minutes: int = Field(default=25, ge=5, le=120, description="课程时长（分钟）")
    
    # 课程信息
    class_type: ClassType = Field(default=ClassType.DIRECT, description="课程类型")
    price: Optional[int] = Field(default=None, ge=1, description="教师单价（整数，单位：元）")
    
    # 会员卡信息
    member_card_id: Optional[int] = Field(default=None, gt=0, description="会员卡ID")
    member_card_name: Optional[str] = Field(default=None, max_length=100, description="会员卡名称")
    
    # 预约信息
    booking_remark: Optional[str] = Field(default=None, max_length=500, description="预约备注")
    member_no_cancel: bool = Field(default=False, description="会员是否可取消")
    
    # 教材信息
    material_id: Optional[int] = Field(default=None, gt=0, description="教材ID")
    material_name: Optional[str] = Field(default=None, max_length=100, description="教材名称")
    
    # 可见性控制
    is_visible_to_member: bool = Field(default=True, description="是否对会员可见")
    
    # 操作信息
    operator_name: Optional[str] = Field(default=None, max_length=50, description="操作人显示名")

    @field_validator('class_datetime')
    @classmethod
    def validate_class_datetime(cls, v):
        """验证上课时间"""
        from datetime import timezone
        now = datetime.now()
        if v <= now:
            raise ValueError('上课时间必须是未来时间')
        return v


class ScheduledClassUpdate(SQLModel):
    """更新已排课表请求模型"""
    member_id: Optional[int] = Field(default=None, gt=0, description="会员ID")
    
    # 时间信息
    class_datetime: Optional[datetime] = Field(default=None, description="精确到分钟的上课时间")
    duration_minutes: Optional[int] = Field(default=None, ge=5, le=120, description="课程时长（分钟）")
    
    # 课程信息
    price: Optional[int] = Field(default=None, ge=1, description="教师单价（整数，单位：元）")
    
    # 会员卡信息
    member_card_id: Optional[int] = Field(default=None, gt=0, description="会员卡ID")
    member_card_name: Optional[str] = Field(default=None, max_length=100, description="会员卡名称")
    
    # 预约信息
    booking_remark: Optional[str] = Field(default=None, max_length=500, description="预约备注")
    member_no_cancel: Optional[bool] = Field(default=None, description="会员是否可取消")
    
    # 教材信息
    material_id: Optional[int] = Field(default=None, gt=0, description="教材ID")
    material_name: Optional[str] = Field(default=None, max_length=100, description="教材名称")
    
    # 状态信息
    status: Optional[ClassStatus] = Field(default=None, description="课程状态")
    
    # 可见性控制
    is_visible_to_member: Optional[bool] = Field(default=None, description="是否对会员可见")
    
    # 操作信息
    operator_name: Optional[str] = Field(default=None, max_length=50, description="操作人显示名")

    @field_validator('class_datetime')
    @classmethod
    def validate_class_datetime(cls, v):
        """验证上课时间"""
        from datetime import timezone
        if v is not None:
            now = datetime.now()
            if v <= now:
                raise ValueError('上课时间必须是未来时间')
        return v


# API响应模型
class ScheduledClassRead(ScheduledClassBase):
    """已排课表响应模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None
    
    # 扩展显示字段
    teacher_name: Optional[str] = Field(default=None, description="教师姓名")
    teacher_number: Optional[str] = Field(default=None, description="教师编号")
    member_name: Optional[str] = Field(default=None, description="会员姓名")
    member_phone: Optional[str] = Field(default=None, description="会员手机号")
    
    # 格式化显示字段
    class_datetime_display: Optional[str] = Field(default=None, description="上课时间显示")
    status_display: Optional[str] = Field(default=None, description="状态显示名称")
    class_type_display: Optional[str] = Field(default=None, description="课程类型显示名称")


class ScheduledClassList(SQLModel):
    """已排课表列表响应模型"""
    id: int
    teacher_id: int
    teacher_name: Optional[str] = None
    teacher_number: Optional[str] = None
    member_id: Optional[int] = None
    member_name: Optional[str] = None
    class_datetime: datetime
    class_datetime_display: Optional[str] = None
    duration_minutes: int
    class_type: ClassType
    class_type_display: Optional[str] = None
    status: ClassStatus
    status_display: Optional[str] = None
    price: Optional[int] = None
    is_visible_to_member: bool
    created_at: datetime


# ==================== 分角色的课程创建模型 ====================

class TeacherClassCreate(SQLModel):
    """教师版课程创建请求模型 - 教师开放可预约课节"""
    class_datetime: datetime = Field(description="精确到分钟的上课时间")
    duration_minutes: int = Field(default=25, ge=5, le=120, description="课程时长（分钟）")
    price: Optional[int] = Field(default=None, ge=1, description="教师单价（整数，单位：元）")

    # 教材信息（可选）
    material_name: Optional[str] = Field(default=None, max_length=100, description="教材名称")

    # 可见性控制
    is_visible_to_member: bool = Field(default=True, description="是否对会员可见")

    @field_validator('class_datetime')
    @classmethod
    def validate_class_datetime(cls, v):
        """验证上课时间"""
        from datetime import timezone
        now = datetime.now()
        if v <= now:
            raise ValueError('上课时间必须是未来时间')
        return v


class MemberClassBooking(SQLModel):
    """会员版课程预约请求模型 - 会员直接约课"""
    class_id: int = Field(gt=0, description="课程ID")

    # 会员卡信息
    member_card_id: Optional[int] = Field(default=None, gt=0, description="会员卡ID")
    member_card_name: Optional[str] = Field(default=None, max_length=100, description="会员卡名称")

    # 教材信息（可选，如果课程已有教材则可不传）
    material_name: Optional[str] = Field(default=None, max_length=100, description="教材名称")

    # 预约信息
    booking_remark: Optional[str] = Field(default=None, max_length=500, description="预约备注")


class ClassBookingData(SQLModel):
    """课程预约数据模型 - 用于预约接口的请求体"""
    # 会员卡信息
    member_card_id: Optional[int] = Field(default=None, gt=0, description="会员卡ID")
    member_card_name: Optional[str] = Field(default=None, max_length=100, description="会员卡名称")

    # 教材信息（可选，因为课程可能已经有教材）
    material_name: Optional[str] = Field(default=None, max_length=100, description="教材名称")

    # 预约信息
    booking_remark: Optional[str] = Field(default=None, max_length=500, description="预约备注")


class AdminClassCreate(SQLModel):
    """管理员版课程创建请求模型 - 管理员全权限操作"""
    teacher_id: int = Field(gt=0, description="教师ID")
    member_id: Optional[int] = Field(default=None, gt=0, description="会员ID（可选）")

    # 时间信息
    class_datetime: datetime = Field(description="精确到分钟的上课时间")
    duration_minutes: int = Field(default=25, ge=5, le=120, description="课程时长（分钟）")

    # 课程信息
    class_type: ClassType = Field(default=ClassType.DIRECT, description="课程类型")
    price: Optional[int] = Field(default=None, ge=1, description="教师单价（整数，单位：元）")

    # 会员卡信息（当指定会员时）
    member_card_id: Optional[int] = Field(default=None, gt=0, description="会员卡ID")
    member_card_name: Optional[str] = Field(default=None, max_length=100, description="会员卡名称")

    # 预约信息
    booking_remark: Optional[str] = Field(default=None, max_length=500, description="预约备注")
    member_no_cancel: bool = Field(default=False, description="会员是否可取消")

    # 教材信息（可选）
    material_id: Optional[int] = Field(default=None, gt=0, description="教材ID")
    material_name: Optional[str] = Field(default=None, max_length=100, description="教材名称")

    # 可见性控制
    is_visible_to_member: bool = Field(default=True, description="是否对会员可见")

    # 操作信息
    operator_name: Optional[str] = Field(default=None, max_length=50, description="操作人显示名")

    @field_validator('class_datetime')
    @classmethod
    def validate_class_datetime(cls, v):
        """验证上课时间"""
        from datetime import timezone
        now = datetime.now()
        if v <= now:
            raise ValueError('上课时间必须是未来时间')
        return v


class FixedScheduleClassCreate(SQLModel):
    """固定约课排课版本 - 用于固定约课扣费时批量排课"""
    teacher_id: int = Field(gt=0, description="教师ID")
    member_id: int = Field(gt=0, description="会员ID")

    # 时间信息
    class_datetime: datetime = Field(description="精确到分钟的上课时间")
    duration_minutes: int = Field(default=25, ge=5, le=120, description="课程时长（分钟）")

    # 价格信息（必填）
    price: int = Field(gt=0, description="教师单价（整数，单位：元）")

    # 会员卡信息
    member_card_id: Optional[int] = Field(default=None, gt=0, description="会员卡ID")
    member_card_name: Optional[str] = Field(default=None, max_length=100, description="会员卡名称")

    # 预约信息
    booking_remark: Optional[str] = Field(default=None, max_length=500, description="预约备注")
    member_no_cancel: bool = Field(default=False, description="会员是否可取消")

    # 注意：教材信息必须为空，由固定约课系统管理
    # material_id 和 material_name 不在此模型中

    # 可见性控制
    is_visible_to_member: bool = Field(default=True, description="是否对会员可见")

    @field_validator('class_datetime')
    @classmethod
    def validate_class_datetime(cls, v):
        """验证上课时间"""
        from datetime import timezone
        now = datetime.now()
        if v <= now:
            raise ValueError('上课时间必须是未来时间')
        return v


# 批量固定约课排课模型
class BatchFixedScheduleCreate(SQLModel):
    """批量固定约课排课请求模型"""
    teacher_id: int = Field(gt=0, description="教师ID")
    member_id: int = Field(gt=0, description="会员ID")
    price: int = Field(gt=0, description="教师单价（整数，单位：元）")

    # 批量时间信息
    class_datetimes: List[datetime] = Field(min_items=1, description="课程时间列表")
    duration_minutes: int = Field(default=25, ge=5, le=120, description="课程时长（分钟）")

    # 会员卡信息
    member_card_id: Optional[int] = Field(default=None, gt=0, description="会员卡ID")
    member_card_name: Optional[str] = Field(default=None, max_length=100, description="会员卡名称")

    # 预约信息
    booking_remark: Optional[str] = Field(default=None, max_length=500, description="预约备注")
    member_no_cancel: bool = Field(default=False, description="会员是否可取消")

    # 可见性控制
    is_visible_to_member: bool = Field(default=True, description="是否对会员可见")

    @field_validator('class_datetimes')
    @classmethod
    def validate_class_datetimes(cls, v):
        """验证课程时间列表"""
        from datetime import timezone
        now = datetime.now()
        for i, dt in enumerate(v):
            if v[i] <= now:
                raise ValueError(f'课程时间 {v[i]} 必须是未来时间')
        return v


# 课程预约模型
class ClassBooking(SQLModel):
    """课程预约请求模型"""
    class_id: int = Field(gt=0, description="课程ID")
    member_id: int = Field(gt=0, description="会员ID")
    member_card_id: Optional[int] = Field(default=None, gt=0, description="会员卡ID")
    member_card_name: Optional[str] = Field(default=None, max_length=100, description="会员卡名称")
    booking_remark: Optional[str] = Field(default=None, max_length=500, description="预约备注")
    operator_name: Optional[str] = Field(default=None, max_length=50, description="操作人显示名")


# 课程取消模型
class ClassCancellation(SQLModel):
    """课程取消请求模型"""
    cancellation_reason: Optional[str] = Field(default=None, max_length=500, description="取消原因")
    operator_name: Optional[str] = Field(default=None, max_length=50, description="操作人显示名")


# 课程状态更新模型
class ClassStatusUpdate(SQLModel):
    """课程状态更新请求模型"""
    status: ClassStatus = Field(description="课程状态")
    operator_name: Optional[str] = Field(default=None, max_length=50, description="操作人显示名")
    remark: Optional[str] = Field(default=None, max_length=500, description="状态更新备注")


class BatchClassStatusUpdate(SQLModel):
    """批量课程状态更新请求模型"""
    class_ids: List[int] = Field(min_items=1, description="课程ID列表")
    new_status: ClassStatus = Field(description="新状态")
    operator_name: Optional[str] = Field(default=None, max_length=50, description="操作人显示名")
    remark: Optional[str] = Field(default=None, max_length=500, description="状态更新备注")
    
    @field_validator('class_ids')
    @classmethod
    def validate_class_ids(cls, v):
        """验证课程ID列表"""
        if len(v) > 50:
            raise ValueError('单次批量操作不能超过50个课程')
        if len(set(v)) != len(v):
            raise ValueError('课程ID列表中存在重复项')
        return v


# 查询参数模型
class ScheduledClassQuery(SQLModel):
    """已排课表查询参数模型"""
    # 分页参数
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")

    # 基础筛选
    teacher_id: Optional[int] = Field(default=None, gt=0, description="教师ID")
    member_id: Optional[int] = Field(default=None, gt=0, description="会员ID")
    class_type: Optional[ClassType] = Field(default=None, description="课程类型")
    status: Optional[ClassStatus] = Field(default=None, description="课程状态")

    # 时间范围筛选
    date_from: Optional[date] = Field(default=None, description="开始日期")
    date_to: Optional[date] = Field(default=None, description="结束日期")
    datetime_from: Optional[datetime] = Field(default=None, description="开始时间")
    datetime_to: Optional[datetime] = Field(default=None, description="结束时间")

    # 时间段筛选
    time_from: Optional[time] = Field(default=None, description="时间段开始")
    time_to: Optional[time] = Field(default=None, description="时间段结束")

    # 价格范围筛选
    min_price: Optional[int] = Field(default=None, ge=0, description="最低价格")
    max_price: Optional[int] = Field(default=None, ge=0, description="最高价格")

    # 可见性筛选
    is_visible_to_member: Optional[bool] = Field(default=None, description="是否对会员可见")

    # 搜索关键词
    search: Optional[str] = Field(default=None, max_length=100, description="搜索关键词（教师姓名、会员姓名、教材名称）")

    # 排序参数
    sort_by: str = Field(default="class_datetime", description="排序字段")
    sort_order: str = Field(default="asc", description="排序方向")

    @field_validator('sort_order')
    @classmethod
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('排序方向必须是 asc 或 desc')
        return v

    @field_validator('date_from', 'date_to')
    @classmethod
    def validate_date_range(cls, v, info):
        """验证日期范围"""
        if info.field_name == 'date_to' and v is not None:
            date_from = info.data.get('date_from')
            if date_from and v < date_from:
                raise ValueError('结束日期不能早于开始日期')
        return v

    @field_validator('datetime_from', 'datetime_to')
    @classmethod
    def validate_datetime_range(cls, v, info):
        """验证时间范围"""
        if info.field_name == 'datetime_to' and v is not None:
            datetime_from = info.data.get('datetime_from')
            if datetime_from and v < datetime_from:
                raise ValueError('结束时间不能早于开始时间')
        return v

    @field_validator('time_from', 'time_to')
    @classmethod
    def validate_time_range(cls, v, info):
        """验证时间段范围"""
        if info.field_name == 'time_to' and v is not None:
            time_from = info.data.get('time_from')
            if time_from and v < time_from:
                raise ValueError('结束时间段不能早于开始时间段')
        return v

    @field_validator('min_price', 'max_price')
    @classmethod
    def validate_price_range(cls, v, info):
        """验证价格范围"""
        if info.field_name == 'max_price' and v is not None:
            min_price = info.data.get('min_price')
            if min_price and v < min_price:
                raise ValueError('最高价格不能低于最低价格')
        return v


# 可用课程查询参数（会员端使用）
class AvailableClassQuery(SQLModel):
    """可用课程查询参数模型（会员端）"""
    # 分页参数
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")

    # 教师筛选
    teacher_id: Optional[int] = Field(default=None, gt=0, description="教师ID")
    teacher_category: Optional[str] = Field(default=None, description="教师分类")

    # 时间筛选
    date_from: Optional[date] = Field(default=None, description="开始日期")
    date_to: Optional[date] = Field(default=None, description="结束日期")
    time_from: Optional[time] = Field(default=None, description="时间段开始")
    time_to: Optional[time] = Field(default=None, description="时间段结束")

    # 价格筛选
    max_price: Optional[int] = Field(default=None, ge=0, description="最高价格")

    # 排序参数
    sort_by: str = Field(default="class_datetime", description="排序字段")
    sort_order: str = Field(default="asc", description="排序方向")

    @field_validator('sort_order')
    @classmethod
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('排序方向必须是 asc 或 desc')
        return v


# 冲突检测模型
class ConflictCheckRequest(SQLModel):
    """时间冲突检测请求模型"""
    teacher_id: int = Field(gt=0, description="教师ID")
    member_id: Optional[int] = Field(default=None, gt=0, description="会员ID")
    class_datetime: datetime = Field(description="上课时间")
    duration_minutes: int = Field(default=25, ge=5, le=120, description="课程时长（分钟）")
    exclude_class_id: Optional[int] = Field(default=None, gt=0, description="排除的课程ID（用于更新时检测）")


class ConflictCheckResponse(SQLModel):
    """时间冲突检测响应模型"""
    has_conflict: bool = Field(description="是否存在冲突")
    teacher_conflict: bool = Field(default=False, description="教师时间冲突")
    member_conflict: bool = Field(default=False, description="会员时间冲突")
    conflict_details: Optional[List[str]] = Field(default=None, description="冲突详情")


class BatchTeacherClassCreate(SQLModel):
    """批量创建教师课程请求模型"""
    teacher_id: int = Field(gt=0, description="教师ID")
    class_datetimes: List[datetime] = Field(min_items=1, max_items=50, description="课程时间列表")
    duration_minutes: int = Field(default=25, ge=5, le=120, description="课程时长（分钟）")
    price: Optional[int] = Field(default=None, ge=1, description="教师单价（整数，单位：元）")
    
    # 教材信息（可选）
    material_name: Optional[str] = Field(default=None, max_length=100, description="教材名称")
    
    # 可见性控制
    is_visible_to_member: bool = Field(default=True, description="是否对会员可见")
    
    @field_validator('class_datetimes')
    @classmethod
    def validate_class_datetimes(cls, v):
        """验证课程时间列表"""
        from datetime import timezone
        now = datetime.now()
        for i, dt in enumerate(v):
            if v[i] <= now:
                raise ValueError(f'课程时间 {v[i]} 必须是未来时间')
        return v


class BatchCancelResult(BaseModel):
    """批量取消预约结果"""
    success_count: int = Field(description="成功取消数量")
    failed_count: int = Field(description="失败数量")
    cancelled_classes: List[ScheduledClassRead] = Field(default=[], description="成功取消的课程")
    failed_cancellations: List[dict] = Field(default=[], description="失败的取消记录")
    
    model_config = ConfigDict(from_attributes=True)

class BatchBookResult(BaseModel):
    """批量预约结果"""
    success_count: int = Field(description="成功预约数量")
    failed_count: int = Field(description="失败数量")
    booked_classes: List[ScheduledClassRead] = Field(default=[], description="成功预约的课程")
    failed_bookings: List[dict] = Field(default=[], description="失败的预约记录")
    
    model_config = ConfigDict(from_attributes=True)
