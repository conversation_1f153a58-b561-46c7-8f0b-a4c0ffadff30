"""租户模块异常处理"""

from app.api.common.exceptions import BusinessException, NotFoundError, ErrorLevel, BaseErrorCode
from typing import Optional


class TenantErrorCode(BaseErrorCode):
    """租户模块错误码"""
    CODE_EXISTS = "TENANT_CODE_EXISTS"
    CONTACT_EMAIL_EXISTS = "TENANT_CONTACT_EMAIL_EXISTS"
    TENANT_SUSPENDED = "TENANT_SUSPENDED"
    TENANT_TERMINATED = "TENANT_TERMINATED"
    PLAN_NOT_FOUND = "TENANT_PLAN_NOT_FOUND"
    TRIAL_EXPIRED = "TENANT_TRIAL_EXPIRED"


class TenantNotFoundError(NotFoundError):
    """租户不存在异常"""
    def __init__(self, code: Optional[str] = None):
        if code:
            super().__init__(f"租户代码 {code} 对应的租户")
        else:
            super().__init__("租户")


class PlanTemplateNotFoundError(NotFoundError):
    """套餐模板不存在异常"""
    def __init__(self, plan_code: str):
        super().__init__(f"套餐模板 {plan_code}")


class TenantBusinessException(BusinessException):
    """租户业务异常"""
    
    @classmethod
    def code_already_exists(cls, code: str):
        """租户代码已存在"""
        return cls(
            f"租户代码 {code} 已存在",
            TenantErrorCode.CODE_EXISTS,
            ErrorLevel.WARNING
        )
    
    @classmethod
    def contact_email_already_exists(cls, email: str):
        """联系邮箱已存在"""
        return cls(
            f"联系邮箱 {email} 已存在",
            TenantErrorCode.CONTACT_EMAIL_EXISTS,
            ErrorLevel.WARNING
        )
    
    @classmethod
    def tenant_suspended(cls, reason: str = None):
        """租户已暂停"""
        message = "租户已被暂停"
        if reason:
            message += f"，原因：{reason}"
        return cls(
            message,
            TenantErrorCode.TENANT_SUSPENDED,
            ErrorLevel.ERROR
        )
    
    @classmethod
    def tenant_terminated(cls):
        """租户已终止"""
        return cls(
            "租户已被终止",
            TenantErrorCode.TENANT_TERMINATED,
            ErrorLevel.ERROR
        )
    
    @classmethod
    def plan_not_found(cls, plan_code: str):
        """套餐模板不存在 - 抛出 NotFoundError"""
        return PlanTemplateNotFoundError(plan_code)
    
    @classmethod
    def trial_expired(cls):
        """试用期已过期"""
        return cls(
            "试用期已过期，请联系管理员升级套餐",
            TenantErrorCode.TRIAL_EXPIRED,
            ErrorLevel.WARNING
        )
    
    @classmethod
    def general_error(cls, message: str):
        """通用租户业务错误"""
        return cls(message)  # 使用默认错误码和级别