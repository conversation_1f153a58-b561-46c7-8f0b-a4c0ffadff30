from sqlmodel import SQLModel, Field, Relationship, Column
from sqlalchemy import JSON
from typing import Optional, Dict, Any
from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum


class PlanType(str, Enum):
    """套餐类型枚举"""
    TRIAL = "trial"
    BASIC = "basic"
    STANDARD = "standard"
    PREMIUM = "premium"
    ENTERPRISE = "enterprise"


class TenantStatus(str, Enum):
    """租户状态枚举"""
    TRIAL = "trial"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    TERMINATED = "terminated"
    EXPIRED = "expired"


class BillingCycle(str, Enum):
    """计费周期枚举"""
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


# 租户基础模型
class TenantBase(SQLModel):
    """租户基础信息"""
    name: str = Field(max_length=100, description="机构名称")
    code: str = Field(max_length=50, unique=True, description="机构代码")
    display_name: Optional[str] = Field(default=None, max_length=100, description="显示名称")
    description: Optional[str] = Field(default=None, description="机构描述")

    # 域名和访问
    domain: Optional[str] = Field(default=None, max_length=100, unique=True, description="自定义域名")
    subdomain: Optional[str] = Field(default=None, max_length=50, unique=True, description="子域名")
    logo_url: Optional[str] = Field(default=None, max_length=500, description="机构logo")
    favicon_url: Optional[str] = Field(default=None, max_length=500, description="网站图标")

    # 联系信息
    contact_name: Optional[str] = Field(default=None, max_length=50, description="联系人姓名")
    contact_phone: Optional[str] = Field(default=None, max_length=20, description="联系电话")
    contact_email: Optional[str] = Field(default=None, max_length=100, description="联系邮箱")
    address: Optional[str] = Field(default=None, description="机构地址")

    # 服务配置
    plan_type: PlanType = Field(default=PlanType.TRIAL, description="套餐类型")
    max_teachers: int = Field(default=5, description="最大教师数量")
    max_members: int = Field(default=50, description="最大会员数量")
    max_storage_gb: int = Field(default=1, description="最大存储空间(GB)")

    # 计费相关
    billing_cycle: BillingCycle = Field(default=BillingCycle.MONTHLY, description="计费周期")
    price_per_class: Decimal = Field(default=Decimal("0.00"), max_digits=10, decimal_places=2, description="按课时计费单价")
    monthly_fee: Decimal = Field(default=Decimal("0.00"), max_digits=10, decimal_places=2, description="月费")
    setup_fee: Decimal = Field(default=Decimal("0.00"), max_digits=10, decimal_places=2, description="安装费")

    # 状态管理
    status: TenantStatus = Field(default=TenantStatus.TRIAL, description="租户状态")
    subscription_expires_at: Optional[datetime] = Field(default=None, description="订阅到期时间")


class Tenant(TenantBase, table=True):
    """租户数据库模型"""
    __tablename__ = "tenants"

    id: Optional[int] = Field(default=None, primary_key=True)

    trial_expires_at: Optional[datetime] = Field(default=None, description="试用期结束时间")
    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="创建时间")
    updated_at: Optional[datetime] = Field(default_factory=datetime.utcnow, description="更新时间")
    created_by: Optional[str] = Field(default=None, max_length=50, description="创建者")
    last_login_at: Optional[datetime] = Field(default=None, description="最后登录时间")

    # 配置信息 (JSON字段)
    settings: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON), description="机构个性化配置")
    features: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON), description="功能开关配置")
    branding: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON), description="品牌定制配置")

    # 技术信息
    database_schema: Optional[str] = Field(default=None, max_length=50, description="对应的数据库schema")
    api_key: Optional[str] = Field(default=None, max_length=100, unique=True, description="API访问密钥")
    webhook_url: Optional[str] = Field(default=None, max_length=500, description="回调地址")

    # 统计信息
    total_users: int = Field(default=0, description="总用户数")
    total_members: int = Field(default=0, description="总会员数")
    total_classes: int = Field(default=0, description="总课程数")
    total_revenue: Decimal = Field(default=Decimal("0.00"), max_digits=12, decimal_places=2, description="总收入")


# 租户配置模板模型
class TenantPlanTemplateBase(SQLModel):
    """租户配置模板基础模型"""
    plan_code: str = Field(max_length=20, unique=True, description="套餐代码")
    plan_name: str = Field(max_length=50, description="套餐名称")
    description: Optional[str] = Field(default=None, description="套餐描述")

    # 资源限制
    max_teachers: int = Field(description="最大教师数")
    max_members: int = Field(description="最大会员数")
    max_storage_gb: int = Field(description="最大存储空间(GB)")

    # 定价信息
    monthly_price: Decimal = Field(max_digits=10, decimal_places=2, description="月费")
    yearly_price: Optional[Decimal] = Field(default=None, max_digits=10, decimal_places=2, description="年费")
    setup_fee: Decimal = Field(default=Decimal("0.00"), max_digits=10, decimal_places=2, description="安装费")
    price_per_class: Decimal = Field(default=Decimal("0.00"), max_digits=10, decimal_places=2, description="按课时计费")

    # 状态
    is_active: bool = Field(default=True, description="是否激活")
    sort_order: int = Field(default=0, description="排序")


class TenantPlanTemplate(TenantPlanTemplateBase, table=True):
    """租户配置模板数据库模型"""
    __tablename__ = "tenant_plan_templates"

    id: Optional[int] = Field(default=None, primary_key=True)
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    updated_at: Optional[datetime] = Field(default=None)
    
    # 功能权限 (JSON字段)
    features: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON), description="功能列表")
