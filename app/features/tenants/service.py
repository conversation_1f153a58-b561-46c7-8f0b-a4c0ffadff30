from typing import List, Optional
from sqlmodel import Session, select
from datetime import datetime, timedelta, timezone
import secrets
import string

from .models import Tenant, TenantPlanTemplate, PlanType, TenantStatus
from .schemas import TenantCreate, TenantUpdate
from .exceptions import TenantNotFoundError, TenantBusinessException
from app.models.shared.audit import SystemAuditLog, AuditStatus
from app.utils.json_encoder import prepare_audit_data
from app.db.session import get_global_session


class TenantService:
    """租户管理服务"""
    
    def __init__(self, session: Session):
        self.session = session
    
    def create_tenant(self, tenant_data: TenantCreate, created_by: str = None) -> Tenant:
        """创建新租户"""
        # 检查租户代码是否已存在
        existing_tenant = self.get_tenant_by_code(tenant_data.code)
        if existing_tenant:
            raise TenantBusinessException.code_already_exists(tenant_data.code)
        
        # 检查联系邮箱是否已存在
        if tenant_data.contact_email:
            existing_email_tenant = self._get_tenant_by_contact_email(tenant_data.contact_email)
            if existing_email_tenant:
                raise TenantBusinessException.contact_email_already_exists(tenant_data.contact_email)
        
        # 生成数据库schema名称
        schema_name = f"tenant_{tenant_data.code.lower()}"
        
        # 生成API密钥
        api_key = self._generate_api_key()
        
        # 设置试用期到期时间
        trial_expires_at = datetime.now() + timedelta(days=14)
        
        # 创建租户实例
        tenant = Tenant(
            **tenant_data.model_dump(),
            database_schema=schema_name,
            api_key=api_key,
            trial_expires_at=trial_expires_at,
            created_by=created_by
        )
        
        self.session.add(tenant)
        self.session.commit()
        self.session.refresh(tenant)
        
        # 记录审计日志
        self._log_audit(
            action="create_tenant",
            resource_type="tenant",
            resource_id=tenant.id,
            new_values=tenant_data.model_dump(),
            description=f"创建租户: {tenant.name}"
        )
        
        return tenant
    
    def get_tenant(self, tenant_id: int) -> Optional[Tenant]:
        """根据ID获取租户"""
        return self.session.get(Tenant, tenant_id)
    
    def get_tenant_by_code(self, code: str) -> Optional[Tenant]:
        """根据代码获取租户"""
        statement = select(Tenant).where(Tenant.code == code)
        return self.session.exec(statement).first()
    
    def _get_tenant_by_contact_email(self, email: str) -> Optional[Tenant]:
        """根据联系邮箱获取租户"""
        statement = select(Tenant).where(Tenant.contact_email == email)
        return self.session.exec(statement).first()
    
    def get_tenants(self, skip: int = 0, limit: int = 100) -> List[Tenant]:
        """获取租户列表"""
        statement = select(Tenant).offset(skip).limit(limit)
        return self.session.exec(statement).all()
    
    def update_tenant(self, tenant_id: int, tenant_data: TenantUpdate) -> Tenant:
        """更新租户信息"""
        tenant = self.session.get(Tenant, tenant_id)
        if not tenant:
            raise TenantNotFoundError()
        
        # 检查联系邮箱是否已存在（如果更新邮箱）
        update_data = tenant_data.model_dump(exclude_unset=True)
        if 'contact_email' in update_data and update_data['contact_email']:
            existing_email_tenant = self._get_tenant_by_contact_email(update_data['contact_email'])
            if existing_email_tenant and existing_email_tenant.id != tenant_id:
                raise TenantBusinessException.contact_email_already_exists(update_data['contact_email'])
        
        # 记录旧值用于审计
        old_values = tenant.model_dump()
        
        # 更新字段
        for field, value in update_data.items():
            setattr(tenant, field, value)
        
        tenant.updated_at = datetime.now()
        
        self.session.add(tenant)
        self.session.commit()
        self.session.refresh(tenant)
        
        # 记录审计日志
        self._log_audit(
            action="update_tenant",
            resource_type="tenant",
            resource_id=tenant.id,
            old_values=old_values,
            new_values=update_data,
            description=f"更新租户: {tenant.name}"
        )
        
        return tenant
    
    def delete_tenant(self, tenant_id: int) -> None:
        """删除租户（软删除）"""
        tenant = self.session.get(Tenant, tenant_id)
        if not tenant:
            raise TenantNotFoundError()
        
        # 软删除：设置状态为terminated
        tenant.status = TenantStatus.TERMINATED
        tenant.updated_at = datetime.now()
        
        self.session.add(tenant)
        self.session.commit()
        
        # 记录审计日志
        self._log_audit(
            action="delete_tenant",
            resource_type="tenant",
            resource_id=tenant.id,
            description=f"删除租户: {tenant.name}"
        )
    
    def get_plan_templates(self) -> List[TenantPlanTemplate]:
        """获取所有套餐模板"""
        statement = select(TenantPlanTemplate).where(TenantPlanTemplate.is_active == True)
        return self.session.exec(statement).all()
    
    def get_plan_template(self, plan_code: str) -> Optional[TenantPlanTemplate]:
        """根据套餐代码获取模板"""
        statement = select(TenantPlanTemplate).where(TenantPlanTemplate.plan_code == plan_code)
        return self.session.exec(statement).first()
    
    def apply_plan_template(self, tenant_id: int, plan_code: str) -> Tenant:
        """为租户应用套餐模板"""
        tenant = self.session.get(Tenant, tenant_id)
        if not tenant:
            raise TenantNotFoundError()
        
        plan_template = self.get_plan_template(plan_code)
        if not plan_template:
            raise TenantBusinessException.plan_not_found(plan_code)
        
        # 应用套餐配置
        tenant.plan_type = PlanType(plan_code)
        tenant.max_teachers = plan_template.max_teachers
        tenant.max_members = plan_template.max_members
        tenant.max_storage_gb = plan_template.max_storage_gb
        tenant.monthly_fee = plan_template.monthly_price
        tenant.price_per_class = plan_template.price_per_class
        tenant.setup_fee = plan_template.setup_fee
        tenant.features = plan_template.features
        tenant.updated_at = datetime.now()
        
        self.session.add(tenant)
        self.session.commit()
        self.session.refresh(tenant)
        
        # 记录审计日志
        self._log_audit(
            action="apply_plan_template",
            resource_type="tenant",
            resource_id=tenant.id,
            new_values={"plan_code": plan_code},
            description=f"应用套餐模板 {plan_code} 到租户: {tenant.name}"
        )
        
        return tenant
    
    def activate_tenant(self, tenant_id: int) -> Tenant:
        """激活租户"""
        tenant = self.session.get(Tenant, tenant_id)
        if not tenant:
            raise TenantNotFoundError()
        
        tenant.status = TenantStatus.ACTIVE
        tenant.updated_at = datetime.now()
        
        self.session.add(tenant)
        self.session.commit()
        self.session.refresh(tenant)
        
        # 记录审计日志
        self._log_audit(
            action="activate_tenant",
            resource_type="tenant",
            resource_id=tenant.id,
            description=f"激活租户: {tenant.name}"
        )
        
        return tenant
    
    def suspend_tenant(self, tenant_id: int, reason: str = None) -> Tenant:
        """暂停租户"""
        tenant = self.session.get(Tenant, tenant_id)
        if not tenant:
            raise TenantNotFoundError()
        
        tenant.status = TenantStatus.SUSPENDED
        tenant.updated_at = datetime.now()
        
        self.session.add(tenant)
        self.session.commit()
        self.session.refresh(tenant)
        
        # 记录审计日志
        self._log_audit(
            action="suspend_tenant",
            resource_type="tenant",
            resource_id=tenant.id,
            description=f"暂停租户: {tenant.name}，原因: {reason or '未指定'}",
            extra_data={"reason": reason} if reason else None
        )
        
        return tenant
    
    def _generate_api_key(self) -> str:
        """生成API密钥"""
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(32))
    
    def _log_audit(self, action: str, resource_type: str = None, resource_id: int = None,
                   old_values: dict = None, new_values: dict = None, description: str = None,
                   extra_data: dict = None):
        """记录审计日志"""
        audit_log = SystemAuditLog(
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            old_values=prepare_audit_data(old_values),
            new_values=prepare_audit_data(new_values),
            description=description,
            extra_data=prepare_audit_data(extra_data),
            status=AuditStatus.SUCCESS
        )
        
        self.session.add(audit_log)
        # 注意：这里不提交，由调用方统一提交


def get_tenant_service(session: Session = None) -> TenantService:
    """获取租户服务实例"""
    if session is None:
        session = next(get_global_session())
    return TenantService(session)
