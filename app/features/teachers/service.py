from typing import List, Optional, Tuple
from sqlmodel import Session, select, text, func, and_, or_
from datetime import datetime, timezone
from decimal import Decimal

from .models import Teacher, TeacherTag, TeacherCategory, TeacherRegion, TeacherStatus
from .schemas import TeacherCreate, TeacherUpdate, TeacherQ<PERSON>y, Teacher<PERSON>agAssign, TeacherTagBatch, TeacherStatusUpdate
from app.features.tags.models import Tag

# 导入业务异常和便捷函数
from app.api.common.exceptions import BusinessException
from .exceptions import TeacherBusinessException, TeacherNotFoundError


class TeacherService:
    """教师管理服务"""

    def __init__(self, session: Session, tenant_id: int):
        self.session = session
        self.tenant_id = tenant_id
        # 设置RLS上下文
        self.session.execute(text(f"SET app.current_tenant_id = '{tenant_id}'"))

    def create_teacher(self, teacher_data: TeacherCreate, created_by: Optional[int] = None) -> Teacher:
        """创建教师"""
        # 检查邮箱是否已存在
        if teacher_data.email:
            existing_teacher = self.get_teacher_by_email(teacher_data.email)
            if existing_teacher:
                raise TeacherBusinessException.email_already_exists(teacher_data.email)

        # 检查手机号是否已存在
        if teacher_data.phone:
            existing_teacher = self.get_teacher_by_phone(teacher_data.phone)
            if existing_teacher:
                raise TeacherBusinessException.phone_already_exists(teacher_data.phone)

        # 检查微信OpenID是否已存在
        if teacher_data.wechat_openid:
            existing_teacher = self.get_teacher_by_wechat(teacher_data.wechat_openid)
            if existing_teacher:
                raise TeacherBusinessException.wechat_already_bound(teacher_data.wechat_openid)

        # 提取标签ID，不包含在教师数据中
        tag_ids = teacher_data.tag_ids
        teacher_dict = teacher_data.model_dump(exclude={'tag_ids'})
        teacher_dict['tenant_id'] = self.tenant_id
        teacher_dict['created_by'] = created_by

        teacher = Teacher(**teacher_dict)
        # 设置创建时间和更新时间
        now = datetime.now()
        teacher.created_at = now
        teacher.updated_at = now

        self.session.add(teacher)
        self.session.commit()
        self.session.refresh(teacher)

        # 验证对象是否真的存在于数据库
        db_teacher = self.session.get(Teacher, teacher.id)
        if not db_teacher:
            raise TeacherBusinessException.general_error("教师创建失败：无法从数据库检索新创建的教师")

        # 分配标签
        if tag_ids:
            self._assign_tags_to_teacher(teacher.id, tag_ids, created_by)

        return teacher

    def get_teacher(self, teacher_id: int) -> Optional[Teacher]:
        """根据ID获取教师（RLS自动过滤租户）"""
        return self.session.get(Teacher, teacher_id)

    def get_teacher_by_email(self, email: str) -> Optional[Teacher]:
        """根据邮箱获取教师"""
        statement = select(Teacher).where(Teacher.email == email)
        return self.session.exec(statement).first()

    def get_teacher_by_phone(self, phone: str) -> Optional[Teacher]:
        """根据手机号获取教师"""
        statement = select(Teacher).where(Teacher.phone == phone)
        return self.session.exec(statement).first()

    def get_teacher_by_wechat(self, wechat_openid: str) -> Optional[Teacher]:
        """根据微信OpenID获取教师"""
        statement = select(Teacher).where(Teacher.wechat_openid == wechat_openid)
        return self.session.exec(statement).first()

    def get_teachers(self, query_params: TeacherQuery) -> Tuple[List[Teacher], int]:
        """获取教师列表（RLS自动过滤租户）"""
        statement = select(Teacher)

        # 筛选条件
        if query_params.name:
            statement = statement.where(Teacher.name.ilike(f"%{query_params.name}%"))
        if query_params.teacher_category:
            statement = statement.where(Teacher.teacher_category == query_params.teacher_category)
        if query_params.region:
            statement = statement.where(Teacher.region == query_params.region)
        if query_params.status:
            statement = statement.where(Teacher.status == query_params.status)
        if query_params.show_to_members is not None:
            statement = statement.where(Teacher.show_to_members == query_params.show_to_members)
        if query_params.min_price is not None:
            statement = statement.where(Teacher.price_per_class >= query_params.min_price)
        if query_params.max_price is not None:
            statement = statement.where(Teacher.price_per_class <= query_params.max_price)

        # 标签筛选
        if query_params.tag_ids:
            # 查找拥有指定标签的教师
            tag_subquery = select(TeacherTag.teacher_id).where(
                TeacherTag.tag_id.in_(query_params.tag_ids)
            )
            statement = statement.where(Teacher.id.in_(tag_subquery))

        # 计算总数
        count_statement = select(func.count()).select_from(statement.subquery())
        total = self.session.exec(count_statement).one()

        # 排序
        if query_params.sort_by == "name":
            order_column = Teacher.name
        elif query_params.sort_by == "price_per_class":
            order_column = Teacher.price_per_class
        elif query_params.sort_by == "priority_level":
            order_column = Teacher.priority_level
        elif query_params.sort_by == "created_at":
            order_column = Teacher.created_at
        else:
            order_column = Teacher.created_at

        if query_params.sort_order == "desc":
            statement = statement.order_by(order_column.desc())
        else:
            statement = statement.order_by(order_column.asc())

        # 分页
        offset = (query_params.page - 1) * query_params.size
        statement = statement.offset(offset).limit(query_params.size)

        teachers = self.session.exec(statement).all()
        return teachers, total

    def count_teachers(self,
                      teacher_category: Optional[TeacherCategory] = None,
                      region: Optional[TeacherRegion] = None,
                      status: Optional[TeacherStatus] = None) -> int:
        """统计教师数量（RLS自动过滤租户）"""
        statement = select(Teacher)

        if teacher_category:
            statement = statement.where(Teacher.teacher_category == teacher_category)
        if region:
            statement = statement.where(Teacher.region == region)
        if status:
            statement = statement.where(Teacher.status == status)

        result = self.session.exec(statement)
        return len(result.all())

    def update_teacher(self, teacher_id: int, teacher_data: TeacherUpdate) -> Teacher:
        """更新教师信息"""
        teacher = self.session.get(Teacher, teacher_id)
        if not teacher:
            raise TeacherNotFoundError(teacher_id)

        update_data = teacher_data.model_dump(exclude_unset=True)

        # 如果更新邮箱，检查是否已存在
        if 'email' in update_data and update_data['email']:
            existing_teacher = self.get_teacher_by_email(update_data['email'])
            if existing_teacher and existing_teacher.id != teacher_id:
                raise TeacherBusinessException.email_already_exists(update_data['email'])

        # 如果更新手机号，检查是否已存在
        if 'phone' in update_data and update_data['phone']:
            existing_teacher = self.get_teacher_by_phone(update_data['phone'])
            if existing_teacher and existing_teacher.id != teacher_id:
                raise TeacherBusinessException.phone_already_exists(update_data['phone'])

        # 如果更新微信OpenID，检查是否已存在
        if 'wechat_openid' in update_data and update_data['wechat_openid']:
            existing_teacher = self.get_teacher_by_wechat(update_data['wechat_openid'])
            if existing_teacher and existing_teacher.id != teacher_id:
                raise TeacherBusinessException.wechat_already_bound(update_data['wechat_openid'])

        for field, value in update_data.items():
            setattr(teacher, field, value)

        teacher.updated_at = datetime.now()

        self.session.add(teacher)
        self.session.commit()
        self.session.refresh(teacher)

        return teacher

    def delete_teacher(self, teacher_id: int) -> None:
        """删除教师（硬删除）"""
        teacher = self.session.get(Teacher, teacher_id)
        if not teacher:
            raise TeacherNotFoundError(teacher_id)

        # 先删除教师标签关联
        tag_statement = select(TeacherTag).where(TeacherTag.teacher_id == teacher_id)
        teacher_tags = self.session.exec(tag_statement).all()
        for teacher_tag in teacher_tags:
            self.session.delete(teacher_tag)

        # 删除教师
        self.session.delete(teacher)
        self.session.commit()

    def _assign_tags_to_teacher(self, teacher_id: int, tag_ids: List[int], created_by: Optional[int] = None):
        """为教师分配标签（内部方法）"""
        now = datetime.now()
        for tag_id in tag_ids:
            # 检查标签是否存在
            tag = self.session.get(Tag, tag_id)
            if not tag:
                continue  # 跳过不存在的标签

            # 检查是否已经分配
            existing = self.session.exec(
                select(TeacherTag).where(
                    and_(TeacherTag.teacher_id == teacher_id, TeacherTag.tag_id == tag_id)
                )
            ).first()
            if existing:
                continue  # 跳过已分配的标签

            teacher_tag = TeacherTag(
                teacher_id=teacher_id,
                tag_id=tag_id,
                created_by=created_by,
                created_at=now
            )
            self.session.add(teacher_tag)

        self.session.commit()

    # 教师标签管理方法
    def assign_tags_to_teacher(self, teacher_id: int, tag_ids: List[int], created_by: Optional[int] = None) -> None:
        """为教师分配标签"""
        teacher = self.session.get(Teacher, teacher_id)
        if not teacher:
            raise TeacherNotFoundError(teacher_id)

        self._assign_tags_to_teacher(teacher_id, tag_ids, created_by)

    def remove_tags_from_teacher(self, teacher_id: int, tag_ids: List[int]) -> None:
        """移除教师标签"""
        teacher = self.session.get(Teacher, teacher_id)
        if not teacher:
            raise TeacherNotFoundError(teacher_id)

        for tag_id in tag_ids:
            teacher_tag = self.session.exec(
                select(TeacherTag).where(
                    and_(TeacherTag.teacher_id == teacher_id, TeacherTag.tag_id == tag_id)
                )
            ).first()
            if teacher_tag:
                self.session.delete(teacher_tag)

        self.session.commit()

    def get_teacher_tags(self, teacher_id: int) -> List[dict]:
        """获取教师的标签列表"""
        statement = select(Tag, TeacherTag).join(
            TeacherTag, Tag.id == TeacherTag.tag_id
        ).where(TeacherTag.teacher_id == teacher_id)

        results = self.session.exec(statement).all()
        tags = []
        for tag, teacher_tag in results:
            tag_dict = tag.model_dump()
            tag_dict['assigned_at'] = teacher_tag.created_at
            tags.append(tag_dict)

        return tags

    def batch_assign_tags(self, teacher_ids: List[int], tag_ids: List[int], created_by: Optional[int] = None) -> None:
        """批量为教师分配标签"""
        for teacher_id in teacher_ids:
            teacher = self.session.get(Teacher, teacher_id)
            if teacher:  # 只处理存在的教师
                self._assign_tags_to_teacher(teacher_id, tag_ids, created_by)

    def batch_remove_tags(self, teacher_ids: List[int], tag_ids: List[int]) -> None:
        """批量移除教师标签"""
        for teacher_id in teacher_ids:
            self.remove_tags_from_teacher(teacher_id, tag_ids)

    # 教师状态管理方法
    def update_teacher_status(self, teacher_id: int, status_data: TeacherStatusUpdate) -> Teacher:
        """更新教师状态"""
        teacher = self.session.get(Teacher, teacher_id)
        if not teacher:
            raise TeacherNotFoundError(teacher_id)

        # 验证状态转换是否合法
        if not self._is_valid_status_transition(teacher.status, status_data.status):
            raise TeacherBusinessException.invalid_status_transition(
                teacher.status.value, status_data.status.value
            )

        teacher.status = status_data.status
        teacher.updated_at = datetime.now()

        self.session.add(teacher)
        self.session.commit()
        self.session.refresh(teacher)

        return teacher

    def activate_teacher(self, teacher_id: int) -> Teacher:
        """激活教师"""
        return self.update_teacher_status(
            teacher_id,
            TeacherStatusUpdate(status=TeacherStatus.ACTIVE)
        )

    def deactivate_teacher(self, teacher_id: int) -> Teacher:
        """停用教师"""
        return self.update_teacher_status(
            teacher_id,
            TeacherStatusUpdate(status=TeacherStatus.INACTIVE)
        )

    def suspend_teacher(self, teacher_id: int, reason: Optional[str] = None) -> Teacher:
        """暂停教师"""
        return self.update_teacher_status(
            teacher_id,
            TeacherStatusUpdate(status=TeacherStatus.SUSPENDED, reason=reason)
        )

    def _is_valid_status_transition(self, from_status: TeacherStatus, to_status: TeacherStatus) -> bool:
        """验证状态转换是否合法"""
        # 定义允许的状态转换
        valid_transitions = {
            TeacherStatus.PENDING: [TeacherStatus.ACTIVE, TeacherStatus.INACTIVE],
            TeacherStatus.ACTIVE: [TeacherStatus.INACTIVE, TeacherStatus.SUSPENDED],
            TeacherStatus.INACTIVE: [TeacherStatus.ACTIVE],
            TeacherStatus.SUSPENDED: [TeacherStatus.ACTIVE, TeacherStatus.INACTIVE]
        }

        return to_status in valid_transitions.get(from_status, [])

    # 高级查询方法
    def get_teachers_by_priority(self, limit: int = 10) -> List[Teacher]:
        """按优先级获取教师列表"""
        statement = select(Teacher).where(
            Teacher.status == TeacherStatus.ACTIVE
        ).order_by(
            Teacher.priority_level.desc(),
            Teacher.created_at.asc()
        ).limit(limit)

        return self.session.exec(statement).all()

    def get_teachers_by_region_and_category(self,
                                          region: TeacherRegion,
                                          category: TeacherCategory) -> List[Teacher]:
        """按区域和分类获取教师"""
        statement = select(Teacher).where(
            and_(
                Teacher.region == region,
                Teacher.teacher_category == category,
                Teacher.status == TeacherStatus.ACTIVE,
                Teacher.show_to_members == True
            )
        ).order_by(Teacher.priority_level.desc())

        return self.session.exec(statement).all()

    def search_teachers(self, keyword: str, limit: int = 20) -> List[Teacher]:
        """搜索教师"""
        statement = select(Teacher).where(
            and_(
                or_(
                    Teacher.name.ilike(f"%{keyword}%"),
                    Teacher.introduction.ilike(f"%{keyword}%")
                ),
                Teacher.status == TeacherStatus.ACTIVE
            )
        ).limit(limit)

        return self.session.exec(statement).all()

    def get_teachers_with_tags(self, query_params: TeacherQuery) -> Tuple[List[dict], int]:
        """获取带标签信息的教师列表"""
        teachers, total = self.get_teachers(query_params)

        teachers_with_tags = []
        for teacher in teachers:
            teacher_dict = teacher.model_dump()
            teacher_dict['tags'] = self.get_teacher_tags(teacher.id)
            teacher_dict['tag_count'] = len(teacher_dict['tags'])
            teachers_with_tags.append(teacher_dict)

        return teachers_with_tags, total

    def get_available_teachers_for_members(self,
                                         region: Optional[TeacherRegion] = None,
                                         category: Optional[TeacherCategory] = None,
                                         max_price: Optional[Decimal] = None) -> List[Teacher]:
        """获取对会员端可见的教师列表"""
        statement = select(Teacher).where(
            and_(
                Teacher.status == TeacherStatus.ACTIVE,
                Teacher.show_to_members == True
            )
        )

        if region:
            statement = statement.where(Teacher.region == region)
        if category:
            statement = statement.where(Teacher.teacher_category == category)
        if max_price:
            statement = statement.where(Teacher.price_per_class <= max_price)

        statement = statement.order_by(
            Teacher.priority_level.desc(),
            Teacher.price_per_class.asc()
        )

        return self.session.exec(statement).all()

    def get_teachers_statistics(self) -> dict:
        """获取教师统计信息"""
        total_teachers = self.count_teachers()
        active_teachers = self.count_teachers(status=TeacherStatus.ACTIVE)

        # 按分类统计
        category_stats = {}
        for category in TeacherCategory:
            count = self.count_teachers(teacher_category=category)
            category_stats[category.value] = count

        # 按区域统计
        region_stats = {}
        for region in TeacherRegion:
            count = self.count_teachers(region=region)
            region_stats[region.value] = count

        return {
            "total_teachers": total_teachers,
            "active_teachers": active_teachers,
            "category_distribution": category_stats,
            "region_distribution": region_stats
        }

    def validate_tag_ids(self, tag_ids_str: str) -> List[int]:
        """验证并解析标签ID字符串"""
        try:
            return [int(id.strip()) for id in tag_ids_str.split(",") if id.strip()]
        except ValueError:
            raise TeacherBusinessException.invalid_tag_ids(tag_ids_str)

    def validate_batch_operation(self, operation: str) -> None:
        """验证批量操作类型"""
        valid_operations = ["add", "assign", "remove"]
        if operation not in valid_operations:
            raise TeacherBusinessException.invalid_operation(operation)

    def validate_file_upload(self, content_type: Optional[str], file_size: int, max_size_mb: int = 5) -> None:
        """验证文件上传"""
        # 验证文件类型
        if not content_type or not content_type.startswith('image/'):
            raise TeacherBusinessException.invalid_file_type(content_type)

        # 验证文件大小
        max_size_bytes = max_size_mb * 1024 * 1024
        if file_size > max_size_bytes:
            size_mb = file_size / (1024 * 1024)
            raise TeacherBusinessException.file_too_large(size_mb, max_size_mb)


def get_teacher_service(session: Session, tenant_id: int) -> TeacherService:
    """获取教师服务实例"""
    return TeacherService(session, tenant_id)
