from sqlmodel import SQLModel, Field, UniqueConstraint, Index
from typing import Optional
from datetime import datetime, timezone, time
from enum import Enum


class Weekday(int, Enum):
    """星期枚举"""
    MONDAY = 1      # 星期一
    TUESDAY = 2     # 星期二
    WEDNESDAY = 3   # 星期三
    THURSDAY = 4    # 星期四
    FRIDAY = 5      # 星期五
    SATURDAY = 6    # 星期六
    SUNDAY = 7      # 星期日


class TeacherFixedSlotBase(SQLModel):
    """教师固定时间占位表基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    teacher_id: int = Field(foreign_key="teachers.id", description="教师ID")
    weekday: Weekday = Field(description="星期几（1-7，1为星期一）")
    start_time: time = Field(description="开始时间（HH:MM格式）")
    duration_minutes: Optional[int] = Field(default=None, description="课节时长(分钟)，NULL时使用系统默认值")
    is_available: bool = Field(default=True, description="是否开放给会员锁定")
    is_visible_to_members: bool = Field(default=True, description="是否对会员可见")


class TeacherFixedSlot(TeacherFixedSlotBase, table=True):
    """教师固定时间占位表"""
    __tablename__ = "teacher_fixed_slots"
    
    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")
    created_by: Optional[int] = Field(default=None, foreign_key="users.id", description="创建者ID")
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        description="创建时间"
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        description="更新时间"
    )
    
    # 创建唯一约束：同一教师在同一时间段只能有一条记录
    __table_args__ = (
        UniqueConstraint(
            "tenant_id", "teacher_id", "weekday", "start_time",
            name="uk_teacher_weekday_starttime"
        ),
        # 创建索引提高查询性能
        Index("idx_teacher_fixed_slots_teacher_id", "teacher_id"),
        Index("idx_teacher_fixed_slots_tenant_id", "tenant_id"),
        Index("idx_teacher_fixed_slots_weekday", "weekday"),
        Index("idx_teacher_fixed_slots_available", "is_available"),
        Index("idx_teacher_fixed_slots_visible", "is_visible_to_members"),
        Index("idx_teacher_fixed_slots_teacher_weekday", "teacher_id", "weekday"),
        Index("idx_teacher_fixed_slots_time_range", "weekday", "start_time"),
    )


# 时间验证规则配置
class TimeValidationConfig:
    """时间验证规则配置（简化版）"""
    # 基础时间间隔（分钟）
    DEFAULT_INTERVAL_MINUTES = 5

    # 课程时长限制
    MIN_DURATION_MINUTES = 5   # 最短5分钟
    MAX_DURATION_MINUTES = 180 # 最长3小时
    DEFAULT_DURATION_MINUTES = 25  # 默认25分钟


# 时间处理工具函数
def validate_time_format(time_str: str) -> bool:
    """验证时间格式是否正确（HH:MM）"""
    try:
        time.fromisoformat(time_str)
        return True
    except ValueError:
        return False


def parse_time_string(time_str: str) -> time:
    """解析时间字符串为time对象"""
    try:
        return time.fromisoformat(time_str)
    except ValueError:
        raise ValueError(f"无效的时间格式: {time_str}，应为 HH:MM 格式")


def time_to_string(time_obj: time) -> str:
    """将time对象转换为字符串"""
    return time_obj.strftime("%H:%M")


def calculate_end_time(start_time: time, duration_minutes: int) -> time:
    """计算结束时间"""
    from datetime import datetime, timedelta

    # 验证时长
    if not validate_duration_minutes(duration_minutes):
        raise ValueError(f"无效的课程时长: {duration_minutes}分钟")

    # 创建一个临时的datetime对象来进行时间计算
    temp_datetime = datetime.combine(datetime.today(), start_time)
    end_datetime = temp_datetime + timedelta(minutes=duration_minutes)

    # 如果跨天了，需要特殊处理
    if end_datetime.date() > temp_datetime.date():
        raise ValueError("课程时长导致跨天，请检查开始时间和时长设置")

    return end_datetime.time()


def validate_time_interval(start_time: time, interval_minutes: int = TimeValidationConfig.DEFAULT_INTERVAL_MINUTES) -> bool:
    """验证时间是否符合指定间隔（默认5分钟间隔）"""
    total_minutes = start_time.hour * 60 + start_time.minute
    return total_minutes % interval_minutes == 0


def validate_time_slot_basic(start_time: time, duration_minutes: int = TimeValidationConfig.DEFAULT_DURATION_MINUTES) -> tuple[bool, str]:
    """
    基础时间段验证（简化版）

    Returns:
        tuple[bool, str]: (是否有效, 错误信息)
    """
    # 验证时间间隔（5分钟）
    if not validate_time_interval(start_time):
        return False, "开始时间必须是5分钟的整数倍"

    # 验证课程时长
    if not validate_duration_minutes(duration_minutes):
        return False, f"课程时长必须在{TimeValidationConfig.MIN_DURATION_MINUTES}-{TimeValidationConfig.MAX_DURATION_MINUTES}分钟之间"

    # 验证是否跨天
    try:
        calculate_end_time(start_time, duration_minutes)
    except ValueError as e:
        return False, str(e)

    return True, ""





def get_weekday_name(weekday: Weekday) -> str:
    """获取星期的中文名称"""
    weekday_names = {
        Weekday.MONDAY: "星期一",
        Weekday.TUESDAY: "星期二", 
        Weekday.WEDNESDAY: "星期三",
        Weekday.THURSDAY: "星期四",
        Weekday.FRIDAY: "星期五",
        Weekday.SATURDAY: "星期六",
        Weekday.SUNDAY: "星期日"
    }
    return weekday_names.get(weekday, "未知")


def validate_weekday(weekday: int) -> bool:
    """验证星期数字是否有效"""
    return 1 <= weekday <= 7


def validate_duration_minutes(duration: int) -> bool:
    """验证课程时长是否合理（5-180分钟）"""
    return 5 <= duration <= 180


def check_time_overlap(
    start_time1: time, 
    duration1: int, 
    start_time2: time, 
    duration2: int
) -> bool:
    """检查两个时间段是否重叠"""
    try:
        end_time1 = calculate_end_time(start_time1, duration1)
        end_time2 = calculate_end_time(start_time2, duration2)
        
        # 转换为分钟数进行比较
        start1_minutes = start_time1.hour * 60 + start_time1.minute
        end1_minutes = end_time1.hour * 60 + end_time1.minute
        start2_minutes = start_time2.hour * 60 + start_time2.minute
        end2_minutes = end_time2.hour * 60 + end_time2.minute
        
        # 检查是否重叠
        return not (end1_minutes <= start2_minutes or end2_minutes <= start1_minutes)
    except ValueError:
        # 如果时间计算出错，认为有重叠（保守处理）
        return True


def get_available_time_slots(
    existing_slots: list,
    start_hour: int = 6,
    end_hour: int = 23,
    interval_minutes: int = 5,
    duration_minutes: int = 25
) -> list:
    """获取可用的时间段列表"""
    available_slots = []
    
    # 生成所有可能的时间段
    current_minutes = start_hour * 60
    end_minutes = end_hour * 60
    
    while current_minutes + duration_minutes <= end_minutes:
        current_time = time(hour=current_minutes // 60, minute=current_minutes % 60)
        
        # 检查是否与现有时间段冲突
        has_conflict = False
        for existing_slot in existing_slots:
            if check_time_overlap(
                current_time, duration_minutes,
                existing_slot.start_time, existing_slot.duration_minutes or 25
            ):
                has_conflict = True
                break
        
        if not has_conflict:
            available_slots.append(current_time)
        
        current_minutes += interval_minutes
    
    return available_slots
