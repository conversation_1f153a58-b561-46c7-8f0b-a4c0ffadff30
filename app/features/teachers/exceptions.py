"""教师模块异常处理"""

from app.api.common.exceptions import BusinessException, NotFoundError, <PERSON>rrorLevel, BaseErrorCode
from typing import Optional


class TeacherErrorCode(BaseErrorCode):
    """教师模块错误码"""
    EMAIL_EXISTS = "TEACHER_EMAIL_EXISTS"
    PHONE_EXISTS = "TEACHER_PHONE_EXISTS"
    WECHAT_BOUND = "TEACHER_WECHAT_BOUND"
    STATUS_INVALID = "TEACHER_STATUS_INVALID"
    TAG_NOT_FOUND = "TEACHER_TAG_NOT_FOUND"
    TAG_ALREADY_ASSIGNED = "TEACHER_TAG_ALREADY_ASSIGNED"

    # 固定时间段相关错误码
    SLOT_CONFLICT = "TEACHER_SLOT_CONFLICT"
    SLOT_NOT_FOUND = "TEACHER_SLOT_NOT_FOUND"
    INVALID_TIME_RANGE = "TEACHER_INVALID_TIME_RANGE"
    BATCH_OPERATION_FAILED = "TEACHER_BATCH_OPERATION_FAILED"
    SLOT_NOT_BELONG_TO_TEACHER = "TEACHER_SLOT_NOT_BELONG_TO_TEACHER"

    # 通用错误码
    INVALID_OPERATION = "TEACHER_INVALID_OPERATION"
    INVALID_FILE_TYPE = "TEACHER_INVALID_FILE_TYPE"
    FILE_TOO_LARGE = "TEACHER_FILE_TOO_LARGE"
    INVALID_TAG_IDS = "TEACHER_INVALID_TAG_IDS"
    INVALID_TIME_FORMAT = "TEACHER_INVALID_TIME_FORMAT"


class TeacherNotFoundError(NotFoundError):
    """教师不存在异常"""
    def __init__(self, teacher_id: Optional[int] = None):
        if teacher_id:
            super().__init__(f"ID为 {teacher_id} 的教师")
        else:
            super().__init__("教师")


class TeacherFixedSlotNotFoundError(NotFoundError):
    """教师固定时间段不存在异常"""
    def __init__(self, slot_id: Optional[int] = None):
        if slot_id:
            super().__init__(f"ID为 {slot_id} 的教师固定时间段")
        else:
            super().__init__("教师固定时间段")


class TeacherBusinessException(BusinessException):
    """教师业务异常"""

    @classmethod
    def email_already_exists(cls, email: str):
        """邮箱已存在"""
        return cls(
            f"邮箱 '{email}' 已存在",
            TeacherErrorCode.EMAIL_EXISTS,
            ErrorLevel.WARNING
        )

    @classmethod
    def phone_already_exists(cls, phone: str):
        """手机号已存在"""
        return cls(
            f"手机号 '{phone}' 已存在",
            TeacherErrorCode.PHONE_EXISTS,
            ErrorLevel.WARNING
        )

    @classmethod
    def wechat_already_bound(cls, wechat_openid: str):
        """微信已绑定其他教师"""
        return cls(
            f"微信账号已绑定其他教师",
            TeacherErrorCode.WECHAT_BOUND,
            ErrorLevel.WARNING
        )

    @classmethod
    def invalid_status_transition(cls, from_status: str, to_status: str):
        """无效的状态转换"""
        return cls(
            f"无法从状态 '{from_status}' 转换到 '{to_status}'",
            TeacherErrorCode.STATUS_INVALID,
            ErrorLevel.ERROR
        )

    @classmethod
    def tag_not_found(cls, tag_id: int):
        """标签不存在"""
        return cls(
            f"标签 ID {tag_id} 不存在",
            TeacherErrorCode.TAG_NOT_FOUND,
            ErrorLevel.WARNING
        )

    @classmethod
    def tag_already_assigned(cls, tag_name: str):
        """标签已分配给教师"""
        return cls(
            f"标签 '{tag_name}' 已分配给该教师",
            TeacherErrorCode.TAG_ALREADY_ASSIGNED,
            ErrorLevel.WARNING
        )

    @classmethod
    def general_error(cls, message: str):
        """通用教师业务错误"""
        return cls(message)

    # 固定时间段相关异常
    @classmethod
    def slot_conflict(cls, teacher_name: str, weekday: str, time_slot: str):
        """时间段冲突"""
        return cls(
            f"教师 '{teacher_name}' 在 {weekday} {time_slot} 的时间段已存在",
            TeacherErrorCode.SLOT_CONFLICT,
            ErrorLevel.WARNING
        )

    @classmethod
    def slot_not_found(cls, slot_id: int = None):
        """时间段不存在"""
        if slot_id:
            message = f"ID为 {slot_id} 的时间段不存在"
        else:
            message = "指定的时间段不存在"
        return cls(
            message,
            TeacherErrorCode.SLOT_NOT_FOUND,
            ErrorLevel.WARNING
        )

    @classmethod
    def invalid_time_range(cls, start_time: str, end_time: str):
        """无效的时间范围"""
        return cls(
            f"无效的时间范围：{start_time} 到 {end_time}",
            TeacherErrorCode.INVALID_TIME_RANGE,
            ErrorLevel.WARNING
        )

    @classmethod
    def batch_operation_failed(cls, failed_count: int, total_count: int):
        """批量操作失败"""
        return cls(
            f"批量操作失败：{failed_count}/{total_count} 个操作失败",
            TeacherErrorCode.BATCH_OPERATION_FAILED,
            ErrorLevel.ERROR
        )

    @classmethod
    def slot_not_belong_to_teacher(cls, slot_id: int, teacher_id: int):
        """时间段不属于指定教师"""
        return cls(
            f"时间段 {slot_id} 不属于教师 {teacher_id}",
            TeacherErrorCode.SLOT_NOT_BELONG_TO_TEACHER,
            ErrorLevel.WARNING
        )

    @classmethod
    def invalid_operation(cls, operation: str):
        """无效的操作类型"""
        return cls(
            f"无效的操作类型: {operation}",
            TeacherErrorCode.INVALID_OPERATION,
            ErrorLevel.WARNING
        )

    @classmethod
    def invalid_file_type(cls, file_type: str = None):
        """无效的文件类型"""
        message = "只支持图片文件"
        if file_type:
            message += f"，当前文件类型: {file_type}"
        return cls(
            message,
            TeacherErrorCode.INVALID_FILE_TYPE,
            ErrorLevel.WARNING
        )

    @classmethod
    def file_too_large(cls, size_mb: float, max_size_mb: int = 5):
        """文件过大"""
        return cls(
            f"文件大小 {size_mb:.1f}MB 超过限制 {max_size_mb}MB",
            TeacherErrorCode.FILE_TOO_LARGE,
            ErrorLevel.WARNING
        )

    @classmethod
    def invalid_tag_ids(cls, tag_ids: str):
        """无效的标签ID格式"""
        return cls(
            f"标签ID格式错误: {tag_ids}",
            TeacherErrorCode.INVALID_TAG_IDS,
            ErrorLevel.WARNING
        )

    @classmethod
    def invalid_time_format(cls, time_str: str, field_name: str = "时间"):
        """无效的时间格式"""
        return cls(
            f"无效的{field_name}格式: {time_str}",
            TeacherErrorCode.INVALID_TIME_FORMAT,
            ErrorLevel.WARNING
        )
