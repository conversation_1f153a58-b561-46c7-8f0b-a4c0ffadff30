from sqlmodel import SQLModel, Field
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from pydantic import field_validator

from .models import TeacherBase, TeacherCategory, TeacherRegion, TeacherStatus
from app.features.users.models import Gender


# API请求模型
class TeacherCreate(SQLModel):
    """创建教师请求模型"""
    name: str = Field(min_length=1, max_length=50, description="教师姓名")
    gender: Optional[Gender] = Field(default=None, description="性别")
    avatar: Optional[str] = Field(default=None, max_length=500, description="头像URL")
    phone: Optional[str] = Field(default=None, max_length=20, description="手机号")
    email: Optional[str] = Field(default=None, max_length=100, description="邮箱")
    price_per_class: int = Field(default=0, description="单节课价格（整数，单位：元）")
    teacher_category: TeacherCategory = Field(description="教师分类")
    region: TeacherRegion = Field(description="教师区域")
    wechat_bound: bool = Field(default=False, description="是否绑定微信")
    wechat_openid: Optional[str] = Field(default=None, max_length=100, description="微信OpenID")
    wechat_unionid: Optional[str] = Field(default=None, max_length=100, description="微信UnionID")
    show_to_members: bool = Field(default=True, description="是否对会员端展示")
    introduction: Optional[str] = Field(default=None, description="教师介绍")
    teaching_experience: Optional[int] = Field(default=None, ge=0, description="教学经验(年)")
    specialties: List[str] = Field(default_factory=list, description="专业特长")
    certifications: List[str] = Field(default_factory=list, description="资质证书")
    priority_level: int = Field(default=0, description="排课优先级")
    notes: Optional[str] = Field(default=None, description="备注")
    tag_ids: List[int] = Field(default_factory=list, description="标签ID列表")

    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('邮箱格式不正确')
        return v

    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v):
        if v and len(v) < 11:
            raise ValueError('手机号长度不能少于11位')
        return v


class TeacherUpdate(SQLModel):
    """更新教师请求模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=50, description="教师姓名")
    gender: Optional[Gender] = Field(default=None, description="性别")
    avatar: Optional[str] = Field(default=None, max_length=500, description="头像URL")
    phone: Optional[str] = Field(default=None, max_length=20, description="手机号")
    email: Optional[str] = Field(default=None, max_length=100, description="邮箱")
    price_per_class: Optional[Decimal] = Field(default=None, ge=0, description="单节课价格")
    teacher_category: Optional[TeacherCategory] = Field(default=None, description="教师分类")
    region: Optional[TeacherRegion] = Field(default=None, description="教师区域")
    wechat_bound: Optional[bool] = Field(default=None, description="是否绑定微信")
    wechat_openid: Optional[str] = Field(default=None, max_length=100, description="微信OpenID")
    wechat_unionid: Optional[str] = Field(default=None, max_length=100, description="微信UnionID")
    show_to_members: Optional[bool] = Field(default=None, description="是否对会员端展示")
    introduction: Optional[str] = Field(default=None, description="教师介绍")
    teaching_experience: Optional[int] = Field(default=None, ge=0, description="教学经验(年)")
    specialties: Optional[List[str]] = Field(default=None, description="专业特长")
    certifications: Optional[List[str]] = Field(default=None, description="资质证书")
    priority_level: Optional[int] = Field(default=None, description="排课优先级")
    notes: Optional[str] = Field(default=None, description="备注")

    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('邮箱格式不正确')
        return v

    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v):
        if v and len(v) < 11:
            raise ValueError('手机号长度不能少于11位')
        return v


class TeacherQuery(SQLModel):
    """教师查询参数模型"""
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")
    name: Optional[str] = Field(default=None, description="教师姓名搜索")
    teacher_category: Optional[TeacherCategory] = Field(default=None, description="教师分类")
    region: Optional[TeacherRegion] = Field(default=None, description="教师区域")
    status: Optional[TeacherStatus] = Field(default=None, description="教师状态")
    show_to_members: Optional[bool] = Field(default=None, description="是否对会员端展示")
    min_price: Optional[Decimal] = Field(default=None, ge=0, description="最低价格")
    max_price: Optional[Decimal] = Field(default=None, ge=0, description="最高价格")
    tag_ids: Optional[List[int]] = Field(default=None, description="标签ID列表")
    sort_by: str = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="desc", description="排序方向")

    @field_validator('sort_order')
    @classmethod
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('排序方向必须是 asc 或 desc')
        return v


# API响应模型
class TeacherRead(TeacherBase):
    """教师响应模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None


class TeacherList(SQLModel):
    """教师列表响应模型"""
    id: int
    name: str
    gender: Optional[Gender] = None
    avatar: Optional[str] = None
    teacher_category: TeacherCategory
    region: TeacherRegion
    price_per_class: Decimal
    status: TeacherStatus
    show_to_members: bool
    priority_level: int
    teaching_experience: Optional[int] = None
    tag_count: int = Field(default=0, description="标签数量")


class TeacherDetail(TeacherRead):
    """教师详情响应模型"""
    tags: List[dict] = Field(default_factory=list, description="关联的标签列表")


# 教师标签管理模型
class TeacherTagAssign(SQLModel):
    """教师标签分配模型"""
    tag_ids: List[int] = Field(description="标签ID列表")


class TeacherTagBatch(SQLModel):
    """教师标签批量操作模型"""
    teacher_ids: List[int] = Field(description="教师ID列表")
    tag_ids: List[int] = Field(description="标签ID列表")
    operation: str = Field(description="操作类型：add-添加，remove-移除")

    @field_validator('operation')
    @classmethod
    def validate_operation(cls, v):
        if v not in ['add', 'remove']:
            raise ValueError('操作类型必须是 add 或 remove')
        return v


# 教师状态管理模型
class TeacherStatusUpdate(SQLModel):
    """教师状态更新模型"""
    status: TeacherStatus = Field(description="新状态")
    reason: Optional[str] = Field(default=None, description="状态变更原因")
