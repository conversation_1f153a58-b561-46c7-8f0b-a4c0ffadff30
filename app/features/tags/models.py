from sqlmodel import SQLModel, Field, UniqueConstraint, Index
from typing import Optional
from datetime import datetime, timezone
from enum import Enum


class TagStatus(str, Enum):
    """标签状态枚举"""
    ACTIVE = "active"      # 激活
    INACTIVE = "inactive"  # 停用


class TagCategoryBase(SQLModel):
    """标签分类基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    name: str = Field(max_length=50, description="分类名称")
    description: Optional[str] = Field(default=None, max_length=200, description="分类描述")
    sort_order: int = Field(default=0, description="排序顺序")


class TagCategory(TagCategoryBase, table=True):
    """标签分类数据库模型"""
    __tablename__ = "tag_categories"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    created_by: Optional[int] = Field(default=None, foreign_key="users.id", description="创建者")
    
    __table_args__ = (
        # 租户内分类名称唯一
        UniqueConstraint('tenant_id', 'name', name='uq_tenant_tag_category_name'),
        # 索引
        Index('idx_tenant_tag_categories', 'tenant_id', 'id'),
        Index('idx_tag_category_sort', 'tenant_id', 'sort_order'),
    )


class TagBase(SQLModel):
    """标签基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    category_id: int = Field(foreign_key="tag_categories.id", description="标签分类ID")
    name: str = Field(max_length=50, description="标签名称")
    description: Optional[str] = Field(default=None, max_length=200, description="标签描述")
    status: TagStatus = Field(default=TagStatus.ACTIVE, description="标签状态")


class Tag(TagBase, table=True):
    """标签数据库模型"""
    __tablename__ = "tags"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    created_by: Optional[int] = Field(default=None, foreign_key="users.id", description="创建者")
    
    __table_args__ = (
        # 租户内同分类下标签名称唯一
        UniqueConstraint('tenant_id', 'category_id', 'name', name='uq_tenant_category_tag_name'),
        # 索引
        Index('idx_tenant_tags', 'tenant_id', 'id'),
        Index('idx_tag_category', 'tenant_id', 'category_id'),
        Index('idx_tag_status', 'tenant_id', 'status'),
    )
