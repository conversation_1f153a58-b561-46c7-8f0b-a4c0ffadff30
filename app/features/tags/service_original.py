from typing import List, Optional, Tuple
from sqlmodel import Session, select, text, func
from datetime import datetime, timezone

from .models import TagCategory, Tag, TagStatus
from .schemas import (
    TagCategoryCreate, TagCategoryUpdate, TagCategoryQuery,
    TagCreate, TagUpdate, TagQuery, TagBatchCreate, TagBatchUpdate
)

# 导入业务异常和便捷函数
from app.api.common.exceptions import BusinessException
from .exceptions import (
    TagBusinessException, TagCategoryNotFoundError, TagNotFoundError
)


class TagCategoryService:
    """标签分类管理服务"""

    def __init__(self, session: Session, tenant_id: int):
        self.session = session
        self.tenant_id = tenant_id
        # 设置RLS上下文
        self.session.execute(text(f"SET app.current_tenant_id = '{tenant_id}'"))

    def create_category(self, category_data: TagCategoryCreate, created_by: Optional[int] = None) -> TagCategory:
        """创建标签分类"""
        # 检查分类名称是否已存在（租户内唯一）
        existing_category = self.get_category_by_name(category_data.name)
        if existing_category:
            raise TagBusinessException.category_name_already_exists(category_data.name)

        category_dict = category_data.model_dump()
        category_dict['tenant_id'] = self.tenant_id
        category_dict['created_by'] = created_by

        category = TagCategory(**category_dict)
        # 设置创建时间和更新时间
        now = datetime.now()
        category.created_at = now
        category.updated_at = now

        self.session.add(category)
        self.session.commit()
        self.session.refresh(category)

        # 验证对象是否真的存在于数据库
        db_category = self.session.get(TagCategory, category.id)
        if not db_category:
            raise TagBusinessException.general_error("标签分类创建失败：无法从数据库检索新创建的分类")

        return category

    def get_category(self, category_id: int) -> Optional[TagCategory]:
        """根据ID获取标签分类（RLS自动过滤租户）"""
        return self.session.get(TagCategory, category_id)

    def get_category_by_name(self, name: str) -> Optional[TagCategory]:
        """根据名称获取标签分类"""
        statement = select(TagCategory).where(TagCategory.name == name)
        return self.session.exec(statement).first()

    def get_categories(self, query_params: TagCategoryQuery) -> Tuple[List[TagCategory], int]:
        """获取标签分类列表（RLS自动过滤租户）"""
        statement = select(TagCategory)

        # 名称模糊查询
        if query_params.name:
            statement = statement.where(TagCategory.name.ilike(f"%{query_params.name}%"))

        # 排序
        statement = statement.order_by(TagCategory.sort_order.asc(), TagCategory.created_at.desc())

        # 计算总数
        count_statement = select(func.count(TagCategory.id))
        if query_params.name:
            count_statement = count_statement.where(TagCategory.name.ilike(f"%{query_params.name}%"))
        total = self.session.exec(count_statement).one()

        # 分页
        offset = (query_params.page - 1) * query_params.size
        statement = statement.offset(offset).limit(query_params.size)

        categories = self.session.exec(statement).all()
        return categories, total

    def update_category(self, category_id: int, category_data: TagCategoryUpdate) -> TagCategory:
        """更新标签分类"""
        category = self.session.get(TagCategory, category_id)
        if not category:
            raise TagCategoryNotFoundError()

        update_data = category_data.model_dump(exclude_unset=True)

        # 如果更新名称，检查是否已存在
        if 'name' in update_data and update_data['name']:
            existing_category = self.get_category_by_name(update_data['name'])
            if existing_category and existing_category.id != category_id:
                raise TagBusinessException.category_name_already_exists(update_data['name'])

        for field, value in update_data.items():
            setattr(category, field, value)

        category.updated_at = datetime.now()

        self.session.add(category)
        self.session.commit()
        self.session.refresh(category)

        return category

    def delete_category(self, category_id: int) -> None:
        """删除标签分类"""
        category = self.session.get(TagCategory, category_id)
        if not category:
            raise TagCategoryNotFoundError()

        # 检查分类下是否还有标签
        tag_count = self.session.exec(
            select(func.count(Tag.id)).where(Tag.category_id == category_id)
        ).one()

        if tag_count > 0:
            raise TagBusinessException.category_has_tags(category.name, tag_count)

        self.session.delete(category)
        self.session.commit()

    def get_category_with_tag_count(self, category_id: int) -> Optional[dict]:
        """获取标签分类及其标签数量"""
        category = self.session.get(TagCategory, category_id)
        if not category:
            return None

        tag_count = self.session.exec(
            select(func.count(Tag.id)).where(Tag.category_id == category_id)
        ).one()

        return {
            **category.model_dump(),
            "tag_count": tag_count
        }


class TagService:
    """标签管理服务"""

    def __init__(self, session: Session, tenant_id: int):
        self.session = session
        self.tenant_id = tenant_id
        # 设置RLS上下文
        self.session.execute(text(f"SET app.current_tenant_id = '{tenant_id}'"))

    def create_tag(self, tag_data: TagCreate, created_by: Optional[int] = None) -> Tag:
        """创建标签"""
        # 验证分类是否存在
        category = self.session.get(TagCategory, tag_data.category_id)
        if not category:
            raise TagCategoryNotFoundError()

        # 检查标签名称是否已存在（同分类下唯一）
        existing_tag = self.get_tag_by_name_and_category(tag_data.name, tag_data.category_id)
        if existing_tag:
            raise TagBusinessException.tag_name_already_exists(tag_data.name, category.name)

        tag_dict = tag_data.model_dump()
        tag_dict['tenant_id'] = self.tenant_id
        tag_dict['created_by'] = created_by

        tag = Tag(**tag_dict)
        # 设置创建时间和更新时间
        now = datetime.now()
        tag.created_at = now
        tag.updated_at = now

        self.session.add(tag)
        self.session.commit()
        self.session.refresh(tag)

        # 验证对象是否真的存在于数据库
        db_tag = self.session.get(Tag, tag.id)
        if not db_tag:
            raise TagBusinessException.general_error("标签创建失败：无法从数据库检索新创建的标签")

        return tag

    def get_tag(self, tag_id: int) -> Optional[Tag]:
        """根据ID获取标签（RLS自动过滤租户）"""
        return self.session.get(Tag, tag_id)

    def get_tag_by_name_and_category(self, name: str, category_id: int) -> Optional[Tag]:
        """根据名称和分类获取标签"""
        statement = select(Tag).where(
            Tag.name == name,
            Tag.category_id == category_id
        )
        return self.session.exec(statement).first()

    def get_tags(self, query_params: TagQuery) -> Tuple[List[Tag], int]:
        """获取标签列表（RLS自动过滤租户）"""
        statement = select(Tag).join(TagCategory)

        # 筛选条件
        if query_params.name:
            statement = statement.where(Tag.name.ilike(f"%{query_params.name}%"))
        if query_params.category_id:
            statement = statement.where(Tag.category_id == query_params.category_id)
        if query_params.status:
            statement = statement.where(Tag.status == query_params.status)

        # 排序
        statement = statement.order_by(TagCategory.sort_order.asc(), Tag.created_at.desc())

        # 计算总数
        count_statement = select(func.count(Tag.id)).join(TagCategory)
        if query_params.name:
            count_statement = count_statement.where(Tag.name.ilike(f"%{query_params.name}%"))
        if query_params.category_id:
            count_statement = count_statement.where(Tag.category_id == query_params.category_id)
        if query_params.status:
            count_statement = count_statement.where(Tag.status == query_params.status)
        total = self.session.exec(count_statement).one()

        # 分页
        offset = (query_params.page - 1) * query_params.size
        statement = statement.offset(offset).limit(query_params.size)

        tags = self.session.exec(statement).all()
        return tags, total

    def update_tag(self, tag_id: int, tag_data: TagUpdate) -> Tag:
        """更新标签"""
        tag = self.session.get(Tag, tag_id)
        if not tag:
            raise TagNotFoundError()

        update_data = tag_data.model_dump(exclude_unset=True)

        # 如果更新分类，验证分类是否存在
        if 'category_id' in update_data and update_data['category_id']:
            category = self.session.get(TagCategory, update_data['category_id'])
            if not category:
                raise TagCategoryNotFoundError()

        # 如果更新名称或分类，检查是否已存在
        if 'name' in update_data or 'category_id' in update_data:
            new_name = update_data.get('name', tag.name)
            new_category_id = update_data.get('category_id', tag.category_id)

            existing_tag = self.get_tag_by_name_and_category(new_name, new_category_id)
            if existing_tag and existing_tag.id != tag_id:
                category = self.session.get(TagCategory, new_category_id)
                raise TagBusinessException.tag_name_already_exists(new_name, category.name)

        for field, value in update_data.items():
            setattr(tag, field, value)

        tag.updated_at = datetime.now()

        self.session.add(tag)
        self.session.commit()
        self.session.refresh(tag)

        return tag

    def delete_tag(self, tag_id: int) -> None:
        """删除标签"""
        tag = self.session.get(Tag, tag_id)
        if not tag:
            raise TagNotFoundError()

        # TODO: 检查标签是否正在使用中（教师标签等）
        # 这里暂时跳过，等教师模块实现后再添加

        self.session.delete(tag)
        self.session.commit()

    def batch_create_tags(self, batch_data: TagBatchCreate, created_by: Optional[int] = None) -> List[Tag]:
        """批量创建标签"""
        # 验证分类是否存在
        category = self.session.get(TagCategory, batch_data.category_id)
        if not category:
            raise TagCategoryNotFoundError()

        created_tags = []
        now = datetime.now()

        for tag_name in batch_data.tags:
            # 检查标签名称是否已存在
            existing_tag = self.get_tag_by_name_and_category(tag_name, batch_data.category_id)
            if existing_tag:
                # 跳过已存在的标签，继续处理其他标签
                continue

            tag = Tag(
                tenant_id=self.tenant_id,
                category_id=batch_data.category_id,
                name=tag_name,
                status=TagStatus.ACTIVE,
                created_by=created_by,
                created_at=now,
                updated_at=now
            )
            self.session.add(tag)
            created_tags.append(tag)

        if created_tags:
            self.session.commit()
            # 刷新所有创建的标签
            for tag in created_tags:
                self.session.refresh(tag)

        return created_tags

    def batch_update_tags(self, batch_data: TagBatchUpdate) -> List[Tag]:
        """批量更新标签"""
        # 获取所有要更新的标签
        statement = select(Tag).where(Tag.id.in_(batch_data.tag_ids))
        tags = self.session.exec(statement).all()

        if len(tags) != len(batch_data.tag_ids):
            raise TagBusinessException.general_error("部分标签不存在")

        update_data = batch_data.model_dump(exclude_unset=True, exclude={'tag_ids'})

        # 如果更新分类，验证分类是否存在
        if 'category_id' in update_data and update_data['category_id']:
            category = self.session.get(TagCategory, update_data['category_id'])
            if not category:
                raise TagCategoryNotFoundError()

        now = datetime.now()
        updated_tags = []

        for tag in tags:
            for field, value in update_data.items():
                setattr(tag, field, value)
            tag.updated_at = now
            self.session.add(tag)
            updated_tags.append(tag)

        self.session.commit()
        # 刷新所有更新的标签
        for tag in updated_tags:
            self.session.refresh(tag)

        return updated_tags

    def get_tags_with_category(self, query_params: TagQuery) -> Tuple[List[dict], int]:
        """获取带分类信息的标签列表"""
        statement = select(Tag, TagCategory).join(TagCategory)

        # 筛选条件
        if query_params.name:
            statement = statement.where(Tag.name.ilike(f"%{query_params.name}%"))
        if query_params.category_id:
            statement = statement.where(Tag.category_id == query_params.category_id)
        if query_params.status:
            statement = statement.where(Tag.status == query_params.status)

        # 排序
        statement = statement.order_by(TagCategory.sort_order.asc(), Tag.created_at.desc())

        # 计算总数
        count_statement = select(func.count(Tag.id)).join(TagCategory)
        if query_params.name:
            count_statement = count_statement.where(Tag.name.ilike(f"%{query_params.name}%"))
        if query_params.category_id:
            count_statement = count_statement.where(Tag.category_id == query_params.category_id)
        if query_params.status:
            count_statement = count_statement.where(Tag.status == query_params.status)
        total = self.session.exec(count_statement).one()

        # 分页
        offset = (query_params.page - 1) * query_params.size
        statement = statement.offset(offset).limit(query_params.size)

        results = self.session.exec(statement).all()

        # 组合标签和分类信息
        tags_with_category = []
        for tag, category in results:
            tag_dict = tag.model_dump()
            tag_dict['category_name'] = category.name
            tags_with_category.append(tag_dict)

        return tags_with_category, total


# 服务工厂函数
def get_tag_category_service(session: Session, tenant_id: int) -> TagCategoryService:
    """获取标签分类服务实例"""
    return TagCategoryService(session, tenant_id)


def get_tag_service(session: Session, tenant_id: int) -> TagService:
    """获取标签服务实例"""
    return TagService(session, tenant_id)
