"""标签模块异常处理"""

from app.api.common.exceptions import BusinessException, NotFoundError, ErrorLevel, BaseErrorCode
from typing import Optional, Dict


class TagErrorCode(BaseErrorCode):
    """标签模块错误码"""
    CATEGORY_NAME_EXISTS = "TAG_CATEGORY_NAME_EXISTS"
    TAG_NAME_EXISTS = "TAG_TAG_NAME_EXISTS"
    CATEGORY_HAS_TAGS = "TAG_CATEGORY_HAS_TAGS"
    TAG_IN_USE = "TAG_TAG_IN_USE"


class TagCategoryNotFoundError(NotFoundError):
    """标签分类不存在异常"""
    def __init__(self, category_name: Optional[str] = None):
        if category_name:
            super().__init__(f"标签分类 {category_name}")
        else:
            super().__init__("标签分类")


class TagNotFoundError(NotFoundError):
    """标签不存在异常"""
    def __init__(self, tag_name: Optional[str] = None):
        if tag_name:
            super().__init__(f"标签 {tag_name}")
        else:
            super().__init__("标签")


class TagBusinessException(BusinessException):
    """标签业务异常"""
    
    @classmethod
    def category_name_already_exists(cls, name: str):
        """标签分类名称已存在"""
        return cls(
            f"标签分类名称 '{name}' 已存在",
            TagErrorCode.CATEGORY_NAME_EXISTS,
            ErrorLevel.WARNING
        )
    
    @classmethod
    def tag_name_already_exists(cls, name: str, category_name: str):
        """标签名称已存在"""
        return cls(
            f"标签名称 '{name}' 在分类 '{category_name}' 中已存在",
            TagErrorCode.TAG_NAME_EXISTS,
            ErrorLevel.WARNING
        )
    
    @classmethod
    def category_has_tags(cls, category_name: str, tag_count: int):
        """标签分类下还有标签，无法删除"""
        return cls(
            f"标签分类 '{category_name}' 下还有 {tag_count} 个标签，无法删除",
            TagErrorCode.CATEGORY_HAS_TAGS,
            ErrorLevel.WARNING,
            {"tag_count": tag_count}
        )
    
    @classmethod
    def tag_in_use(cls, tag_name: str, usage_count: int):
        """标签正在使用中，无法删除"""
        return cls(
            f"标签 '{tag_name}' 正在被 {usage_count} 个对象使用，无法删除",
            TagErrorCode.TAG_IN_USE,
            ErrorLevel.WARNING,
            {"usage_count": usage_count}
        )
    
    @classmethod
    def general_error(cls, message: str):
        """通用标签业务错误"""
        return cls(message)  # 使用默认错误码和级别
