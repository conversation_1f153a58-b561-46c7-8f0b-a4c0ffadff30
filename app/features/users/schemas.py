from sqlmodel import SQLModel, Field
from typing import Optional, List
from datetime import datetime, date
from pydantic import EmailStr

from .models import UserBase, UserRole, UserStatus, Gender


# API 请求模型
class UserCreate(SQLModel):
    """创建用户请求模型"""
    username: str = Field(min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    password: str = Field(min_length=6, description="密码")
    real_name: Optional[str] = None
    role: UserRole
    gender: Optional[Gender] = None
    birthday: Optional[date] = None


class UserUpdate(SQLModel):
    """更新用户请求模型"""
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    real_name: Optional[str] = None
    avatar_url: Optional[str] = None
    gender: Optional[Gender] = None
    birthday: Optional[date] = None
    status: Optional[UserStatus] = None


class PasswordChange(SQLModel):
    """修改密码请求模型"""
    old_password: str
    new_password: str = Field(min_length=6)


class UserSearchParams(SQLModel):
    """用户搜索参数"""
    skip: int = Field(default=0, ge=0, description="跳过记录数")
    limit: int = Field(default=100, ge=1, le=1000, description="返回记录数")
    role: Optional[UserRole] = Field(default=None, description="按角色筛选")
    status: Optional[UserStatus] = Field(default=None, description="按状态筛选")
    search: Optional[str] = Field(default=None, description="搜索关键词（用户名、邮箱、真实姓名）")


# API 响应模型
class UserRead(UserBase):
    """用户响应模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    # 不返回密码哈希
    password_hash: str = Field(exclude=True)


class UserListResponse(SQLModel):
    """用户列表响应模型"""
    items: List[UserRead]
    total: int
    skip: int
    limit: int
