"""用户模块异常处理"""

from app.api.common.exceptions import BusinessException, NotFoundError, ErrorLevel, BaseErrorCode
from typing import Optional, Dict


class UserErrorCode(BaseErrorCode):
    """用户模块错误码 - 只定义需要客户端特殊处理的错误"""
    USERNAME_EXISTS = "USER_USERNAME_EXISTS"
    EMAIL_EXISTS = "USER_EMAIL_EXISTS"
    ACCOUNT_LOCKED = "USER_ACCOUNT_LOCKED"
    INVALID_PASSWORD = "USER_INVALID_PASSWORD"


class UserNotFoundError(NotFoundError):
    """用户不存在异常"""
    def __init__(self, username: Optional[str] = None):
        if username:
            super().__init__(f"用户名 {username} 对应的用户")
        else:
            super().__init__("用户")


class UserBusinessException(BusinessException):
    """用户业务异常"""
    
    @classmethod
    def username_already_exists(cls, username: str):
        """用户名已存在 - 客户端需要高亮用户名字段"""
        return cls(
            f"用户名 '{username}' 已存在",
            UserErrorCode.USERNAME_EXISTS,
            ErrorLevel.WARNING
        )
    
    @classmethod
    def email_already_exists(cls, email: str):
        """邮箱已存在 - 客户端需要高亮邮箱字段"""
        return cls(
            f"邮箱 '{email}' 已存在",
            UserErrorCode.EMAIL_EXISTS,
            ErrorLevel.WARNING
        )
    
    @classmethod
    def account_locked(cls, locked_until: Optional[str] = None):
        """账户被锁定 - 客户端需要显示解锁时间或申诉入口"""
        message = "用户账户已被锁定"
        if locked_until:
            message += f"，锁定至 {locked_until}"
        return cls(
            message,
            UserErrorCode.ACCOUNT_LOCKED,
            ErrorLevel.ERROR,
            {"locked_until": locked_until} if locked_until else None
        )
    
    @classmethod
    def invalid_password(cls):
        """密码验证失败 - 客户端可能需要特殊处理"""
        return cls(
            "原密码不正确",
            UserErrorCode.INVALID_PASSWORD,
            ErrorLevel.WARNING
        )
    
    @classmethod
    def general_error(cls, message: str):
        """通用用户业务错误"""
        return cls(message)  # 使用默认错误码和级别