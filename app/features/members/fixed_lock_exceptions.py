from datetime import time
from enum import Enum

from app.api.common.exceptions import BusinessException, NotFoundError
from .fixed_lock_models import (
    MemberFixedSlotLockStatus, 
    get_lock_status_display,
    get_weekday_display
)


class MemberFixedSlotLockErrorCode(str, Enum):
    """会员固定课位锁定错误码"""
    SLOT_ALREADY_LOCKED = "SLOT_ALREADY_LOCKED"
    MEMBER_NOT_FOUND = "MEMBER_NOT_FOUND"
    TEACHER_SLOT_NOT_FOUND = "TEACHER_SLOT_NOT_FOUND"
    SLOT_NOT_AVAILABLE = "SLOT_NOT_AVAILABLE"
    SLOT_NOT_VISIBLE = "SLOT_NOT_VISIBLE"
    INVALID_STATUS_TRANSITION = "INVALID_STATUS_TRANSITION"
    BATCH_OPERATION_FAILED = "BATCH_OPERATION_FAILED"
    LOCK_CONFLICT = "LOCK_CONFLICT"
    INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS"


class MemberFixedSlotLockNotFoundError(NotFoundError):
    """会员固定课位锁定未找到异常"""
    def __init__(self, lock_id: int):
        super().__init__(f"会员固定课位锁定记录不存在: ID {lock_id}")


class MemberFixedSlotLockBusinessException(BusinessException):
    """会员固定课位锁定业务异常"""
    
    def __init__(self, message: str, code: str = "BUSINESS_ERROR"):
        super().__init__(message, code)
    
    @classmethod
    def slot_already_locked(cls, teacher_id: int, weekday: int, start_time: time, locked_by_member_id: int):
        """时间段已被锁定异常"""
        weekday_name = get_weekday_display(weekday)
        message = f"时间段 {weekday_name} {start_time} 已被会员 {locked_by_member_id} 锁定"
        return cls(message, MemberFixedSlotLockErrorCode.SLOT_ALREADY_LOCKED)
    
    @classmethod
    def member_not_found(cls, member_id: int):
        """会员不存在异常"""
        message = f"会员不存在: ID {member_id}"
        return cls(message, MemberFixedSlotLockErrorCode.MEMBER_NOT_FOUND)
    
    @classmethod
    def teacher_slot_not_found(cls, teacher_slot_id: int):
        """教师固定时间段不存在异常"""
        message = f"教师固定时间段不存在: ID {teacher_slot_id}"
        return cls(message, MemberFixedSlotLockErrorCode.TEACHER_SLOT_NOT_FOUND)
    
    @classmethod
    def slot_not_available(cls, teacher_slot_id: int):
        """时间段不可用异常"""
        message = f"时间段不可用: ID {teacher_slot_id}"
        return cls(message, MemberFixedSlotLockErrorCode.SLOT_NOT_AVAILABLE)
    
    @classmethod
    def slot_not_visible(cls, teacher_slot_id: int):
        """时间段对会员不可见异常"""
        message = f"时间段对会员不可见: ID {teacher_slot_id}"
        return cls(message, MemberFixedSlotLockErrorCode.SLOT_NOT_VISIBLE)
    
    @classmethod
    def invalid_status_transition(cls, current_status: MemberFixedSlotLockStatus, target_status: MemberFixedSlotLockStatus):
        """无效状态转换异常"""
        current_display = get_lock_status_display(current_status)
        target_display = get_lock_status_display(target_status)
        message = f"无效的状态转换: 从 {current_display} 到 {target_display}"
        return cls(message, MemberFixedSlotLockErrorCode.INVALID_STATUS_TRANSITION)
    
    @classmethod
    def batch_operation_failed(cls, operation: str, failed_count: int, total_count: int):
        """批量操作失败异常"""
        message = f"批量{operation}失败: {failed_count}/{total_count} 个操作失败"
        return cls(message, MemberFixedSlotLockErrorCode.BATCH_OPERATION_FAILED)
    
    @classmethod
    def lock_conflict(cls, member_id: int, conflict_details: str):
        """锁定冲突异常"""
        message = f"会员 {member_id} 锁定冲突: {conflict_details}"
        return cls(message, MemberFixedSlotLockErrorCode.LOCK_CONFLICT)
    
    @classmethod
    def insufficient_permissions(cls, operation: str, user_role: str = None):
        """权限不足异常"""
        if user_role:
            message = f"权限不足: {user_role} 无法执行 {operation} 操作"
        else:
            message = f"权限不足: 无法执行 {operation} 操作"
        return cls(message, MemberFixedSlotLockErrorCode.INSUFFICIENT_PERMISSIONS)
    
    @classmethod
    def general_error(cls, message: str):
        """通用业务错误"""
        return cls(message)


# 便捷的异常工厂函数
def lock_not_found(lock_id: int) -> MemberFixedSlotLockNotFoundError:
    """创建锁定记录未找到异常"""
    return MemberFixedSlotLockNotFoundError(lock_id)


def slot_already_locked(teacher_id: int, weekday: int, start_time: time, locked_by_member_id: int) -> MemberFixedSlotLockBusinessException:
    """创建时间段已被锁定异常"""
    return MemberFixedSlotLockBusinessException.slot_already_locked(teacher_id, weekday, start_time, locked_by_member_id)


def member_not_found(member_id: int) -> MemberFixedSlotLockBusinessException:
    """创建会员不存在异常"""
    return MemberFixedSlotLockBusinessException.member_not_found(member_id)


def teacher_slot_not_found(teacher_slot_id: int) -> MemberFixedSlotLockBusinessException:
    """创建教师时间段不存在异常"""
    return MemberFixedSlotLockBusinessException.teacher_slot_not_found(teacher_slot_id)


def slot_not_available(teacher_slot_id: int) -> MemberFixedSlotLockBusinessException:
    """创建时间段不可用异常"""
    return MemberFixedSlotLockBusinessException.slot_not_available(teacher_slot_id)


def slot_not_visible(teacher_slot_id: int) -> MemberFixedSlotLockBusinessException:
    """创建时间段不可见异常"""
    return MemberFixedSlotLockBusinessException.slot_not_visible(teacher_slot_id)


def invalid_status_transition(current_status: MemberFixedSlotLockStatus, target_status: MemberFixedSlotLockStatus) -> MemberFixedSlotLockBusinessException:
    """创建无效状态转换异常"""
    return MemberFixedSlotLockBusinessException.invalid_status_transition(current_status, target_status)


def batch_operation_failed(operation: str, failed_count: int, total_count: int) -> MemberFixedSlotLockBusinessException:
    """创建批量操作失败异常"""
    return MemberFixedSlotLockBusinessException.batch_operation_failed(operation, failed_count, total_count)


def lock_conflict(member_id: int, conflict_details: str) -> MemberFixedSlotLockBusinessException:
    """创建锁定冲突异常"""
    return MemberFixedSlotLockBusinessException.lock_conflict(member_id, conflict_details)


def insufficient_permissions(operation: str, user_role: str = None) -> MemberFixedSlotLockBusinessException:
    """创建权限不足异常"""
    return MemberFixedSlotLockBusinessException.insufficient_permissions(operation, user_role) 