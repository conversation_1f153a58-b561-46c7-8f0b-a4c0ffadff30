from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session

from .schemas import MemberCreate, MemberUpdate, MemberRead
from .models import  MemberType, MemberStatus
from .service import MemberService
from .exceptions import MemberErrorCode
from app.db.session import get_session
from app.core.context import UserContext
from app.core.dependencies import get_user_context
from app.api.common import (
    DataResponse,
    MessageResponse,
    ListResponse,
    success_response,
    message_response,
    list_response,
    ensure_found,
    ensure_success
)
from app.api.common.responses import create_error_responses, ErrorResponse, AUTH_ONLY_RESPONSES, PERMISSION_ONLY_RESPONSES, RESOURCE_RESPONSES

router = APIRouter()

# 会员模块的错误响应文档
MEMBER_ERROR_RESPONSES = create_error_responses([
    MemberErrorCode.PHONE_EXISTS,
    MemberErrorCode.EMAIL_EXISTS,
    MemberErrorCode.ACCOUNT_FROZEN
])


@router.post(
    "/", 
    response_model=DataResponse[MemberRead], 
    status_code=status.HTTP_201_CREATED,
    responses=MEMBER_ERROR_RESPONSES,
    summary="创建会员",
    description="""
    创建新会员账户
    
    **可能的错误码：**
    - `MEMBER_PHONE_EXISTS`: 手机号已存在
    - `MEMBER_EMAIL_EXISTS`: 邮箱已存在
    - `VALIDATION_ERROR`: 数据验证失败
    """
)
def create_member(
    member_data: MemberCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建会员"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    member = member_service.create_member(member_data)
    return success_response(member, "会员创建成功")


@router.get("/", response_model=ListResponse[MemberRead])
def get_members(
    skip: int = 0,
    limit: int = 100,
    member_type: Optional[MemberType] = None,
    member_status: Optional[MemberStatus] = None,
    sales_id: Optional[int] = None,
    agent_id: Optional[int] = None,
    search_keyword: Optional[str] = None,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员列表"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    members = member_service.get_members(
        skip=skip,
        limit=limit,
        member_type=member_type,
        member_status=member_status,
        sales_id=sales_id,
        agent_id=agent_id,
        search_keyword=search_keyword
    )
    
    total = member_service.count_members(
        member_type=member_type,
        member_status=member_status,
        sales_id=sales_id,
        agent_id=agent_id
    )
    
    return list_response(members, total, "获取会员列表成功")


@router.get("/count", response_model=DataResponse[int])
def count_members(
    member_type: Optional[MemberType] = None,
    member_status: Optional[MemberStatus] = None,
    sales_id: Optional[int] = None,
    agent_id: Optional[int] = None,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """统计会员数量"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    count = member_service.count_members(
        member_type=member_type,
        member_status=member_status,
        sales_id=sales_id,
        agent_id=agent_id
    )
    return success_response(count, "获取会员数量成功")


@router.get("/{member_id}", response_model=DataResponse[MemberRead])
def get_member(
    member_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员详情"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    member = member_service.get_member(member_id)
    member = ensure_found(member, "会员")
    return success_response(member, "获取会员详情成功")


@router.post("/{member_id}/update", response_model=DataResponse[MemberRead], responses=RESOURCE_RESPONSES)
def update_member(
    member_id: int,
    member_data: MemberUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新会员信息"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    updated_member = member_service.update_member(member_id, member_data)
    updated_member = ensure_found(updated_member, "会员")
    return success_response(updated_member, "会员信息更新成功")


@router.post("/{member_id}/update-status", response_model=DataResponse[MemberRead])
def update_member_status(
    member_id: int,
    status: MemberStatus,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新会员状态"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    updated_member = member_service.update_member_status(member_id, status)
    updated_member = ensure_found(updated_member, "会员")
    return success_response(updated_member, "会员状态更新成功")


@router.post("/{member_id}/update-stats", response_model=DataResponse[MemberRead])
def update_member_stats(
    member_id: int,
    class_completed: bool = False,
    class_cancelled: bool = False,
    class_no_show: bool = False,
    amount_spent: float = 0.0,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新会员统计信息"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    updated_member = member_service.update_member_stats(
        member_id,
        class_completed=class_completed,
        class_cancelled=class_cancelled,
        class_no_show=class_no_show,
        amount_spent=amount_spent
    )
    
    updated_member = ensure_found(updated_member, "会员")
    return success_response(updated_member, "会员统计信息更新成功")


@router.post("/{member_id}/delete", response_model=MessageResponse)
def delete_member(
    member_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """删除会员"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    success = member_service.delete_member(member_id)
    ensure_found(success, "会员不存在")
    return message_response("会员删除成功")


@router.post("/{member_id}/deactivate", response_model=MessageResponse, responses=RESOURCE_RESPONSES)
def deactivate_member(
    member_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """停用会员"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    updated_member = member_service.update_member_status(member_id, MemberStatus.FROZEN)
    updated_member = ensure_found(updated_member, "会员")
    return success_response(message="会员已停用")


@router.post("/{member_id}/activate", response_model=MessageResponse, responses=RESOURCE_RESPONSES)
def activate_member(
    member_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """激活会员"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    updated_member = member_service.update_member_status(member_id, MemberStatus.ACTIVE)
    updated_member = ensure_found(updated_member, "会员")
    return success_response(message="会员已激活") 