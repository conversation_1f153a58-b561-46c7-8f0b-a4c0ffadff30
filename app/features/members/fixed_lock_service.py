from typing import List, Optional, Tuple
from sqlmodel import Session, select, text, func, and_
from datetime import datetime, timezone

from .fixed_lock_models import (
    MemberFixedSlotLock, MemberFixedSlotLockStatus
)
from .fixed_lock_schemas import (
    MemberFixedSlotLockCreate, MemberFixedSlotLockUpdate, MemberFixedSlotLockQuery,
    MemberFixedSlotLockBatchCreate, MemberFixedSlotLockBatchUpdate, MemberFixedSlotLockBatchDelete,
    AvailableSlotQuery, LockConflictCheck, LockConflictResult
)
from .models import Member
from ..teachers.fixed_slots_models import TeacherFixedSlot
from .fixed_lock_exceptions import (
    MemberFixedSlotLockNotFoundError, MemberFixedSlotLockBusinessException
)


class MemberFixedSlotLockService:
    """会员固定课位锁定服务"""

    def __init__(self, session: Session, tenant_id: int):
        self.session = session
        self.tenant_id = tenant_id
        # 设置RLS上下文
        self.session.exec(text(f"SET app.current_tenant_id = '{tenant_id}'"))

    # ==================== CRUD操作 ====================

    def create_lock(self, lock_data: MemberFixedSlotLockCreate, created_by: int) -> MemberFixedSlotLock:
        """创建固定课位锁定"""
        # 验证会员是否存在
        member = self.session.get(Member, lock_data.member_id)
        if not member:
            raise MemberFixedSlotLockBusinessException.member_not_found(lock_data.member_id)

        # 验证教师固定时间段是否存在
        teacher_slot = self.session.get(TeacherFixedSlot, lock_data.teacher_fixed_slot_id)
        if not teacher_slot:
            raise MemberFixedSlotLockBusinessException.teacher_slot_not_found(lock_data.teacher_fixed_slot_id)

        # 检查时间段是否可用
        if not teacher_slot.is_available:
            raise MemberFixedSlotLockBusinessException.slot_not_available(
                lock_data.teacher_fixed_slot_id
            )

        # 检查时间段是否对会员可见
        if not teacher_slot.is_visible_to_members:
            raise MemberFixedSlotLockBusinessException.slot_not_visible(
                lock_data.teacher_fixed_slot_id
            )

        # 检查时间段是否已被锁定
        existing_lock = self._get_active_lock_by_slot(lock_data.teacher_fixed_slot_id)
        if existing_lock:
            raise MemberFixedSlotLockBusinessException.slot_already_locked(
                teacher_slot.teacher_id, teacher_slot.weekday, teacher_slot.start_time, existing_lock.member_id
            )

        # 创建锁定记录
        lock_dict = lock_data.model_dump(exclude={'created_by'})
        lock_dict['tenant_id'] = self.tenant_id
        lock_dict['created_by'] = created_by
        
        # 设置冗余字段
        lock_dict['teacher_id'] = teacher_slot.teacher_id
        lock_dict['weekday'] = teacher_slot.weekday
        lock_dict['start_time'] = teacher_slot.start_time

        lock = MemberFixedSlotLock(**lock_dict)
        now = datetime.now()
        lock.created_at = now
        lock.updated_at = now
        lock.locked_at = now

        self.session.add(lock)
        self.session.commit()
        self.session.refresh(lock)

        return lock

    def get_lock(self, lock_id: int) -> Optional[MemberFixedSlotLock]:
        """根据ID获取锁定记录"""
        return self.session.get(MemberFixedSlotLock, lock_id)

    def get_lock_by_slot(self, teacher_fixed_slot_id: int) -> Optional[MemberFixedSlotLock]:
        """根据教师固定时间段ID获取锁定记录"""
        statement = select(MemberFixedSlotLock).where(
            MemberFixedSlotLock.teacher_fixed_slot_id == teacher_fixed_slot_id
        )
        return self.session.exec(statement).first()

    def _get_active_lock_by_slot(self, teacher_fixed_slot_id: int) -> Optional[MemberFixedSlotLock]:
        """根据教师固定时间段ID获取激活状态的锁定记录"""
        statement = select(MemberFixedSlotLock).where(
            and_(
                MemberFixedSlotLock.teacher_fixed_slot_id == teacher_fixed_slot_id,
                MemberFixedSlotLock.status == MemberFixedSlotLockStatus.ACTIVE
            )
        )
        return self.session.exec(statement).first()

    def get_locks(self, query_params: MemberFixedSlotLockQuery) -> Tuple[List[MemberFixedSlotLock], int]:
        """获取锁定记录列表"""
        statement = select(MemberFixedSlotLock)

        # 筛选条件
        conditions = []
        
        if query_params.member_id:
            conditions.append(MemberFixedSlotLock.member_id == query_params.member_id)
        
        if query_params.teacher_id:
            conditions.append(MemberFixedSlotLock.teacher_id == query_params.teacher_id)
        
        if query_params.teacher_fixed_slot_id:
            conditions.append(MemberFixedSlotLock.teacher_fixed_slot_id == query_params.teacher_fixed_slot_id)
        
        if query_params.status:
            conditions.append(MemberFixedSlotLock.status == query_params.status)
        
        if query_params.weekday:
            conditions.append(MemberFixedSlotLock.weekday == query_params.weekday)
        
        if query_params.start_time_from:
            conditions.append(MemberFixedSlotLock.start_time >= query_params.start_time_from)
        
        if query_params.start_time_to:
            conditions.append(MemberFixedSlotLock.start_time <= query_params.start_time_to)
        
        if query_params.locked_at_from:
            conditions.append(MemberFixedSlotLock.locked_at >= query_params.locked_at_from)
        
        if query_params.locked_at_to:
            conditions.append(MemberFixedSlotLock.locked_at <= query_params.locked_at_to)

        if conditions:
            statement = statement.where(and_(*conditions))

        # 计算总数
        count_statement = select(func.count()).select_from(statement.subquery())
        total = self.session.exec(count_statement).one()

        # 排序
        statement = statement.order_by(
            MemberFixedSlotLock.weekday.asc(),
            MemberFixedSlotLock.start_time.asc(),
            MemberFixedSlotLock.locked_at.desc()
        )

        # 分页
        if query_params.page and query_params.size:
            offset = (query_params.page - 1) * query_params.size
            statement = statement.offset(offset).limit(query_params.size)

        locks = self.session.exec(statement).all()
        return locks, total

    def update_lock(self, lock_id: int, lock_data: MemberFixedSlotLockUpdate) -> Optional[MemberFixedSlotLock]:
        """更新锁定记录"""
        lock = self.session.get(MemberFixedSlotLock, lock_id)
        if not lock:
            raise MemberFixedSlotLockNotFoundError(lock_id)

        update_data = lock_data.model_dump(exclude_unset=True)

        # 更新字段
        for field, value in update_data.items():
            setattr(lock, field, value)

        lock.updated_at = datetime.now()

        self.session.add(lock)
        self.session.commit()
        self.session.refresh(lock)

        return lock

    def delete_lock(self, lock_id: int) -> bool:
        """删除锁定记录"""
        lock = self.session.get(MemberFixedSlotLock, lock_id)
        if not lock:
            return False

        self.session.delete(lock)
        self.session.commit()
        return True

    # ==================== 冲突检测和可用性检查 ====================

    def check_lock_conflict(self, conflict_check: LockConflictCheck) -> LockConflictResult:
        """检查锁定冲突"""
        # 检查教师固定时间段是否存在
        teacher_slot = self.session.get(TeacherFixedSlot, conflict_check.teacher_fixed_slot_id)
        if not teacher_slot:
            return LockConflictResult(
                has_conflict=True,
                conflict_type="slot_not_found",
                conflict_message="教师固定时间段不存在"
            )

        # 检查时间段是否已被锁定
        statement = select(MemberFixedSlotLock).where(
            and_(
                MemberFixedSlotLock.teacher_fixed_slot_id == conflict_check.teacher_fixed_slot_id,
                MemberFixedSlotLock.status == MemberFixedSlotLockStatus.ACTIVE
            )
        )

        if conflict_check.exclude_lock_id:
            statement = statement.where(MemberFixedSlotLock.id != conflict_check.exclude_lock_id)

        existing_lock = self.session.exec(statement).first()

        if existing_lock:
            return LockConflictResult(
                has_conflict=True,
                conflict_type="slot_already_locked",
                conflict_message=f"时间段已被会员 {existing_lock.member_id} 锁定",
                conflicting_lock_id=existing_lock.id
            )

        return LockConflictResult(
            has_conflict=False,
            conflict_type=None,
            conflict_message=None,
            conflicting_lock_id=None
        )

    def get_available_slots(self, query: AvailableSlotQuery) -> List[TeacherFixedSlot]:
        """获取可锁定的时间段"""
        statement = select(TeacherFixedSlot)

        conditions = []

        if query.teacher_id:
            conditions.append(TeacherFixedSlot.teacher_id == query.teacher_id)

        if query.only_available:
            conditions.append(TeacherFixedSlot.is_available == True)

        if query.only_visible:
            conditions.append(TeacherFixedSlot.is_visible_to_members == True)

        if query.weekdays:
            # 将整数列表转换为Weekday枚举列表
            from app.features.teachers.fixed_slots_models import Weekday
            weekday_enums = [Weekday(weekday) for weekday in query.weekdays]
            conditions.append(TeacherFixedSlot.weekday.in_(weekday_enums))

        if query.start_time_from:
            conditions.append(TeacherFixedSlot.start_time >= query.start_time_from)

        if query.start_time_to:
            conditions.append(TeacherFixedSlot.start_time <= query.start_time_to)

        if conditions:
            statement = statement.where(and_(*conditions))

        # 如果需要排除已锁定的时间段
        if query.exclude_locked:
            # 使用LEFT JOIN排除已被激活锁定的时间段
            locked_slots_subquery = select(MemberFixedSlotLock.teacher_fixed_slot_id).where(
                MemberFixedSlotLock.status == MemberFixedSlotLockStatus.ACTIVE
            )
            statement = statement.where(
                TeacherFixedSlot.id.not_in(locked_slots_subquery)
            )

        # 排序
        statement = statement.order_by(
            TeacherFixedSlot.weekday.asc(),
            TeacherFixedSlot.start_time.asc()
        )

        return self.session.exec(statement).all()

    def get_member_locks(self, member_id: int, status: Optional[MemberFixedSlotLockStatus] = None) -> List[MemberFixedSlotLock]:
        """获取会员的锁定记录"""
        statement = select(MemberFixedSlotLock).where(
            MemberFixedSlotLock.member_id == member_id
        )

        if status:
            statement = statement.where(MemberFixedSlotLock.status == status)

        statement = statement.order_by(
            MemberFixedSlotLock.weekday.asc(),
            MemberFixedSlotLock.start_time.asc()
        )

        return self.session.exec(statement).all()

    def get_teacher_slot_locks(self, teacher_id: int, status: Optional[MemberFixedSlotLockStatus] = None) -> List[MemberFixedSlotLock]:
        """获取教师时间段的锁定情况"""
        statement = select(MemberFixedSlotLock).where(
            MemberFixedSlotLock.teacher_id == teacher_id
        )

        if status:
            statement = statement.where(MemberFixedSlotLock.status == status)

        statement = statement.order_by(
            MemberFixedSlotLock.weekday.asc(),
            MemberFixedSlotLock.start_time.asc()
        )

        return self.session.exec(statement).all()

    # ==================== 批量操作 ====================

    def batch_create_locks(self, batch_data: MemberFixedSlotLockBatchCreate, created_by: int) -> List[MemberFixedSlotLock]:
        """批量创建锁定记录"""
        created_locks = []
        now = datetime.now()

        for slot_id in batch_data.teacher_fixed_slot_ids:
            try:
                # 验证教师固定时间段是否存在
                teacher_slot = self.session.get(TeacherFixedSlot, slot_id)
                if not teacher_slot:
                    continue  # 跳过不存在的时间段

                # 检查时间段是否可用
                if not teacher_slot.is_available or not teacher_slot.is_visible_to_members:
                    continue  # 跳过不可用的时间段

                # 检查时间段是否已被锁定
                existing_lock = self._get_active_lock_by_slot(slot_id)
                if existing_lock:
                    continue  # 跳过已被锁定的时间段

                # 创建锁定记录
                lock = MemberFixedSlotLock(
                    tenant_id=self.tenant_id,
                    member_id=batch_data.member_id,
                    teacher_fixed_slot_id=slot_id,
                    teacher_id=teacher_slot.teacher_id,
                    weekday=teacher_slot.weekday,
                    start_time=teacher_slot.start_time,
                    status=batch_data.status or MemberFixedSlotLockStatus.ACTIVE,
                    created_by=created_by,
                    created_at=now,
                    updated_at=now,
                    locked_at=now
                )

                self.session.add(lock)
                created_locks.append(lock)

            except Exception:
                # 跳过出错的记录，继续处理其他记录
                continue

        if created_locks:
            self.session.commit()
            for lock in created_locks:
                self.session.refresh(lock)

        return created_locks

    def batch_update_locks(self, batch_data: MemberFixedSlotLockBatchUpdate) -> List[MemberFixedSlotLock]:
        """批量更新锁定记录"""
        updated_locks = []
        now = datetime.now()

        for lock_id in batch_data.lock_ids:
            lock = self.session.get(MemberFixedSlotLock, lock_id)
            if not lock:
                continue  # 跳过不存在的记录

            # 更新字段
            update_data = batch_data.model_dump(exclude={'lock_ids'}, exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(lock, field):
                    setattr(lock, field, value)

            lock.updated_at = now
            self.session.add(lock)
            updated_locks.append(lock)

        if updated_locks:
            self.session.commit()
            for lock in updated_locks:
                self.session.refresh(lock)

        return updated_locks

    def batch_delete_locks(self, batch_data: MemberFixedSlotLockBatchDelete) -> int:
        """批量删除锁定记录"""
        deleted_count = 0

        for lock_id in batch_data.lock_ids:
            lock = self.session.get(MemberFixedSlotLock, lock_id)
            if lock:
                self.session.delete(lock)
                deleted_count += 1

        if deleted_count > 0:
            self.session.commit()

        return deleted_count

    # ==================== 状态管理 ====================

    def activate_lock(self, lock_id: int) -> Optional[MemberFixedSlotLock]:
        """激活锁定"""
        lock = self.session.get(MemberFixedSlotLock, lock_id)
        if not lock:
            raise MemberFixedSlotLockNotFoundError(lock_id)

        # 检查时间段是否已被其他会员锁定
        if lock.status != MemberFixedSlotLockStatus.ACTIVE:
            existing_lock = self._get_active_lock_by_slot(lock.teacher_fixed_slot_id)
            if existing_lock and existing_lock.id != lock_id:
                raise MemberFixedSlotLockBusinessException.slot_already_locked(
                    lock.teacher_id, lock.weekday, lock.start_time, existing_lock.member_id
                )

        lock.status = MemberFixedSlotLockStatus.ACTIVE
        lock.updated_at = datetime.now()

        self.session.add(lock)
        self.session.commit()
        self.session.refresh(lock)

        return lock

    def pause_lock(self, lock_id: int) -> Optional[MemberFixedSlotLock]:
        """暂停锁定"""
        lock = self.session.get(MemberFixedSlotLock, lock_id)
        if not lock:
            raise MemberFixedSlotLockNotFoundError(lock_id)

        lock.status = MemberFixedSlotLockStatus.PAUSED
        lock.updated_at = datetime.now()

        self.session.add(lock)
        self.session.commit()
        self.session.refresh(lock)

        return lock

    def cancel_lock(self, lock_id: int) -> Optional[MemberFixedSlotLock]:
        """取消锁定"""
        lock = self.session.get(MemberFixedSlotLock, lock_id)
        if not lock:
            raise MemberFixedSlotLockNotFoundError(lock_id)

        lock.status = MemberFixedSlotLockStatus.CANCELLED
        lock.updated_at = datetime.now()

        self.session.add(lock)
        self.session.commit()
        self.session.refresh(lock)

        return lock

    def batch_update_status(self, lock_ids: List[int], status: MemberFixedSlotLockStatus) -> List[MemberFixedSlotLock]:
        """批量更新锁定状态"""
        updated_locks = []
        now = datetime.now()

        for lock_id in lock_ids:
            lock = self.session.get(MemberFixedSlotLock, lock_id)
            if not lock:
                continue

            # 如果要激活锁定，需要检查冲突
            if status == MemberFixedSlotLockStatus.ACTIVE and lock.status != MemberFixedSlotLockStatus.ACTIVE:
                existing_lock = self._get_active_lock_by_slot(lock.teacher_fixed_slot_id)
                if existing_lock and existing_lock.id != lock_id:
                    continue  # 跳过有冲突的记录

            lock.status = status
            lock.updated_at = now
            self.session.add(lock)
            updated_locks.append(lock)

        if updated_locks:
            self.session.commit()
            for lock in updated_locks:
                self.session.refresh(lock)

        return updated_locks


def get_member_fixed_slot_lock_service(session: Session, tenant_id: int) -> MemberFixedSlotLockService:
    """获取会员固定课位锁定服务实例"""
    return MemberFixedSlotLockService(session, tenant_id)
