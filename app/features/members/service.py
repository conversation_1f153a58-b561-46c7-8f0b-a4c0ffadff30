from typing import List, Optional
from sqlmodel import Session, select, text
from datetime import datetime, timezone, timedelta
from decimal import Decimal

from .models import Member, MemberType, MemberStatus
from .schemas import MemberCreate, MemberUpdate

# 导入业务异常和便捷函数
from app.api.common.exceptions import BusinessException
from .exceptions import MemberBusinessException, MemberNotFoundError

# 导入会员卡相关服务和模型
from ..member_cards.card_service import MemberCardService
from ..member_cards.template_service import MemberCardTemplateService
from ..member_cards.models import CardType, MemberCard
from ..member_cards.schemas import MemberCardCreate, MemberCardTemplateCreate

class MemberService:
    """会员管理服务"""
    
    def __init__(self, session: Session, tenant_id: int):
        self.session = session
        self.tenant_id = tenant_id
        # 设置RLS上下文
        self.session.execute(text(f"SET app.current_tenant_id = '{tenant_id}'"))
    
    def create_member(self, member_data: MemberCreate, created_by: Optional[int] = None) -> Member:
        """创建新会员"""
        # 检查手机号是否已存在（租户内可重复，但需要明确处理）
        existing_member = self.get_member_by_phone(member_data.phone)
        if existing_member:
            raise MemberBusinessException.phone_already_exists(member_data.phone)
        
        # 检查邮箱是否已存在（租户内唯一）
        if member_data.email:
            existing_email = self.get_member_by_email(member_data.email)
            if existing_email:
                raise MemberBusinessException.email_already_exists(member_data.email)
        
        member_dict = member_data.model_dump()
        member_dict['tenant_id'] = self.tenant_id
        member_dict['created_by'] = created_by
        
        member = Member(**member_dict)
        # 设置创建时间和更新时间
        now = datetime.now()
        member.created_at = now
        member.updated_at = now
        member.registered_at = now

        try:
            # 在同一个事务中创建会员、默认模板和默认储值卡
            self.session.add(member)
            self.session.flush()  # 刷新以获取member.id，但不提交事务

            # 为新会员创建默认模板和储值卡
            default_card = self._create_default_template_and_card(member.id, created_by)

            # 更新会员的会员卡信息
            member.primary_member_card_id = default_card.id
            member.primary_member_card_name = f"默认储值卡-{member.name}"

            # 一起提交事务
            self.session.commit()
            self.session.refresh(member)

        except Exception as e:
            # 回滚事务
            self.session.rollback()
            # 使用统一异常处理
            raise MemberBusinessException.general_error(f"会员创建失败：{str(e)}")

        return member
    
    def get_member(self, member_id: int) -> Optional[Member]:
        """根据ID获取会员（RLS自动过滤租户）"""
        return self.session.get(Member, member_id)
    
    def get_member_by_phone(self, phone: str) -> Optional[Member]:
        """根据手机号获取会员（RLS自动过滤租户）"""
        statement = select(Member).where(Member.phone == phone)
        return self.session.exec(statement).first()
    
    def get_member_by_email(self, email: str) -> Optional[Member]:
        """根据邮箱获取会员（RLS自动过滤租户）"""
        statement = select(Member).where(Member.email == email)
        return self.session.exec(statement).first()
    
    def get_member_by_wechat(self, wechat_openid: str) -> Optional[Member]:
        """根据微信OpenID获取会员（RLS自动过滤租户）"""
        statement = select(Member).where(Member.wechat_openid == wechat_openid)
        return self.session.exec(statement).first()
    
    def get_members(self, 
                   skip: int = 0, 
                   limit: int = 100,
                   member_type: Optional[MemberType] = None,
                   member_status: Optional[MemberStatus] = None,
                   sales_id: Optional[int] = None,
                   agent_id: Optional[int] = None,
                   search_keyword: Optional[str] = None) -> List[Member]:
        """获取会员列表（RLS自动过滤租户）"""
        statement = select(Member)
        
        if member_type:
            statement = statement.where(Member.member_type == member_type)
        if member_status:
            statement = statement.where(Member.member_status == member_status)
        if sales_id:
            statement = statement.where(Member.sales_id == sales_id)
        if agent_id:
            statement = statement.where(Member.agent_id == agent_id)
        if search_keyword:
            # 支持按姓名、手机号、邮箱搜索
            from sqlalchemy import or_
            search_conditions = [
                Member.name.contains(search_keyword),
                Member.phone.contains(search_keyword)
            ]
            # 只有当email不为None时才添加邮箱搜索条件
            search_conditions.append(
                Member.email.contains(search_keyword)
            )
            statement = statement.where(or_(*search_conditions))
            
        statement = statement.offset(skip).limit(limit).order_by(Member.created_at.desc())
        return self.session.exec(statement).all()
    
    def count_members(self,
                     member_type: Optional[MemberType] = None,
                     member_status: Optional[MemberStatus] = None,
                     sales_id: Optional[int] = None,
                     agent_id: Optional[int] = None) -> int:
        """统计会员数量（RLS自动过滤租户）"""
        statement = select(Member)
        
        if member_type:
            statement = statement.where(Member.member_type == member_type)
        if member_status:
            statement = statement.where(Member.member_status == member_status)
        if sales_id:
            statement = statement.where(Member.sales_id == sales_id)
        if agent_id:
            statement = statement.where(Member.agent_id == agent_id)
            
        result = self.session.exec(statement)
        return len(result.all())
    
    def update_member(self, member_id: int, member_data: MemberUpdate) -> Optional[Member]:
        """更新会员信息"""
        member = self.session.get(Member, member_id)
        if not member:
            return None
        
        update_data = member_data.model_dump(exclude_unset=True)
        
        # 检查邮箱唯一性（如果要更新邮箱）
        if 'email' in update_data and update_data['email']:
            existing_email = self.get_member_by_email(update_data['email'])
            if existing_email and existing_email.id != member_id:
                raise MemberBusinessException.email_already_exists(update_data['email'])
        
        # 检查手机号（如果要更新手机号）
        if 'phone' in update_data:
            existing_phone = self.get_member_by_phone(update_data['phone'])
            if existing_phone and existing_phone.id != member_id:
                raise MemberBusinessException.phone_already_exists(update_data['phone'])
        
        for field, value in update_data.items():
            setattr(member, field, value)
        
        member.updated_at = datetime.now()
        
        self.session.add(member)
        self.session.commit()
        self.session.refresh(member)
        
        return member
    
    def update_member_status(self, member_id: int, status: MemberStatus) -> Optional[Member]:
        """更新会员状态"""
        member = self.session.get(Member, member_id)
        if not member:
            return None
        
        member.member_status = status
        member.updated_at = datetime.now()
        
        self.session.add(member)
        self.session.commit()
        self.session.refresh(member)
        
        return member
    
    def update_member_stats(self, member_id: int, 
                           class_completed: bool = False,
                           class_cancelled: bool = False,
                           class_no_show: bool = False,
                           amount_spent: float = 0.0) -> Optional[Member]:
        """更新会员统计信息"""
        member = self.session.get(Member, member_id)
        if not member:
            return None
        
        member.total_classes += 1
        if class_completed:
            member.completed_classes += 1
            member.last_class_at = datetime.now()
        elif class_cancelled:
            member.cancelled_classes += 1
        elif class_no_show:
            member.no_show_classes += 1
        
        if amount_spent > 0:
            member.total_spent += Decimal(str(amount_spent))
        
        member.updated_at = datetime.now()
        
        self.session.add(member)
        self.session.commit()
        self.session.refresh(member)
        
        return member
    
    def update_login_time(self, member_id: int) -> Optional[Member]:
        """更新会员最后登录时间"""
        member = self.session.get(Member, member_id)
        if not member:
            return None
        
        member.last_login_at = datetime.now()
        member.updated_at = datetime.now()
        
        self.session.add(member)
        self.session.commit()
        self.session.refresh(member)
        
        return member
    
    def delete_member(self, member_id: int) -> bool:
        """删除会员及其关联的会员卡"""
        member = self.session.get(Member, member_id)
        if not member:
            return False
        
        try:
            # 先删除该会员的所有会员卡
            from sqlmodel import select
            from ..member_cards.models import MemberCard
            
            # 查询该会员的所有会员卡
            member_cards_statement = select(MemberCard).where(MemberCard.member_id == member_id)
            member_cards = self.session.exec(member_cards_statement).all()
            
            # 删除所有会员卡
            for card in member_cards:
                self.session.delete(card)
            
            # 删除会员
            self.session.delete(member)
            self.session.commit()
            return True
            
        except Exception as e:
            # 回滚事务
            self.session.rollback()
            raise BusinessException(f"删除会员失败：{str(e)}")

    def _create_default_template_and_card(self, member_id: int, created_by: Optional[int] = None) -> 'MemberCard':
        """为新会员创建默认模板和储值卡"""
        # 创建会员卡模板服务实例
        template_service = MemberCardTemplateService(self.session, self.tenant_id)
        card_service = MemberCardService(self.session, self.tenant_id)

        # 1. 创建默认模板
        default_template_data = MemberCardTemplateCreate(
            name=f"默认储值卡模板-{member_id}",
            card_type=CardType.VALUE_LIMITED,
            sale_price=1000,
            available_balance=1000,  # 为测试方便设定可用余额为1000元
            validity_days=30,  # 30天有效期
            description="系统为新会员自动创建的默认储值卡模板"
        )

        default_template = template_service.create_template(default_template_data, created_by)

        # 2. 基于模板创建默认储值卡
        default_card_data = MemberCardCreate(
            member_id=member_id,
            template_id=default_template.id,
            card_type=CardType.VALUE_LIMITED,  # 有期限储值卡
            balance=1000,  # 初始余额为1000元，用于测试
            expires_at=datetime.now() + timedelta(days=30)  # 30天有效期
        )

        # 创建默认储值卡
        default_card = card_service.create_card(default_card_data, created_by)

        return default_card


def get_member_service(session: Session, tenant_id: int) -> MemberService:
    """获取会员服务实例"""
    return MemberService(session, tenant_id)