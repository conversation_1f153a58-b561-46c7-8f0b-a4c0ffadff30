from sqlmodel import SQLModel, Field, UniqueConstraint, Index, CheckConstraint
from typing import Optional
from datetime import datetime, timezone, time
from enum import Enum


class MemberFixedSlotLockStatus(str, Enum):
    """会员固定课位锁定状态枚举"""
    ACTIVE = "active"      # 激活锁定，参与排课
    PAUSED = "paused"      # 暂停锁定，跳过排课
    CANCELLED = "cancelled"  # 取消锁定，只能是老师或者管理员取消了该固定位，会员自己取消会删除数据


class MemberFixedSlotLockBase(SQLModel):
    """会员固定课位锁定基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    member_id: int = Field(foreign_key="members.id", description="会员ID")
    
    # 直接关联教师固定时间段
    teacher_fixed_slot_id: int = Field(foreign_key="teacher_fixed_slots.id", description="教师固定时间段ID")
    
    # 冗余字段（便于查询和显示，避免总是需要JOIN）
    teacher_id: int = Field(foreign_key="teachers.id", description="教师ID")
    weekday: int = Field(ge=1, le=7, description="星期几（1-7，1为星期一）")
    start_time: time = Field(description="开始时间（HH:MM格式）")
    
    # 状态
    status: MemberFixedSlotLockStatus = Field(default=MemberFixedSlotLockStatus.ACTIVE, description="锁定状态")
    
    # 锁定时间
    locked_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        description="锁定时间"
    )


class MemberFixedSlotLock(MemberFixedSlotLockBase, table=True):
    """会员固定课位锁定表"""
    __tablename__ = "member_fixed_slot_locks"
    
    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")
    
    # 审计字段
    created_by: Optional[int] = Field(default=None, foreign_key="users.id", description="创建者ID")
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        description="创建时间"
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        description="更新时间"
    )
    
    __table_args__ = (
        # 唯一约束：同一时间段只能被一个会员锁定
        UniqueConstraint(
            "teacher_fixed_slot_id",
            name="uk_teacher_fixed_slot_lock"
        ),
        
        # 检查约束：确保weekday在有效范围内
        CheckConstraint(
            "weekday >= 1 AND weekday <= 7",
            name="ck_weekday_range"
        ),
        
        # 创建索引提高查询性能
        Index("idx_member_locks_member", "member_id", "status"),
        Index("idx_member_locks_teacher_slot", "teacher_fixed_slot_id"),
        Index("idx_member_locks_teacher_time", "teacher_id", "weekday", "start_time"),  # 冗余字段索引
        Index("idx_member_locks_status", "tenant_id", "status"),
        Index("idx_member_locks_tenant", "tenant_id"),
        Index("idx_member_locks_teacher", "teacher_id"),
        Index("idx_member_locks_weekday", "weekday"),
        Index("idx_member_locks_locked_at", "locked_at"),
    )


# 工具函数
def get_lock_status_display(status: MemberFixedSlotLockStatus) -> str:
    """获取锁定状态的显示名称"""
    status_names = {
        MemberFixedSlotLockStatus.ACTIVE: "激活",
        MemberFixedSlotLockStatus.PAUSED: "暂停",
        MemberFixedSlotLockStatus.CANCELLED: "已取消"
    }
    return status_names.get(status, "未知")


def validate_lock_status(status: str) -> bool:
    """验证锁定状态是否有效"""
    return status in [s.value for s in MemberFixedSlotLockStatus]


def is_lock_active(status: MemberFixedSlotLockStatus) -> bool:
    """判断锁定是否处于激活状态（参与排课）"""
    return status == MemberFixedSlotLockStatus.ACTIVE


def get_weekday_display(weekday: int) -> str:
    """获取星期的中文显示名称"""
    weekday_names = {
        1: "星期一",
        2: "星期二",
        3: "星期三",
        4: "星期四",
        5: "星期五",
        6: "星期六",
        7: "星期日"
    }
    return weekday_names.get(weekday, "未知")


def format_time_slot(weekday: int, start_time: time) -> str:
    """格式化时间段显示"""
    weekday_display = get_weekday_display(weekday)
    time_display = start_time.strftime("%H:%M")
    return f"{weekday_display} {time_display}" 