"""简化版查询工具函数 - 提供常用查询模式"""

from typing import Optional, List, Type, TypeVar, Tuple, Any, Dict
from sqlmodel import Session, select, SQLModel, func
from sqlalchemy import or_, and_
from datetime import datetime, date

T = TypeVar('T', bound=SQLModel)


def apply_text_search(statement, model_class: Type[T], search_term: str, search_fields: List[str]):
    """应用多字段文本搜索"""
    if not search_term or not search_fields:
        return statement
    
    search_conditions = []
    for field_name in search_fields:
        if hasattr(model_class, field_name):
            field = getattr(model_class, field_name)
            search_conditions.append(field.ilike(f"%{search_term}%"))
    
    if search_conditions:
        statement = statement.where(or_(*search_conditions))
    
    return statement


def apply_filters(statement, model_class: Type[T], filters: Dict[str, Any]):
    """应用字段过滤条件"""
    for field_name, value in filters.items():
        if value is not None and hasattr(model_class, field_name):
            field = getattr(model_class, field_name)
            if isinstance(value, list):
                # IN 查询
                statement = statement.where(field.in_(value))
            else:
                # 等值查询
                statement = statement.where(field == value)
    
    return statement


def apply_date_range(statement, model_class: Type[T], field_name: str, 
                    start_date: Optional[date] = None, end_date: Optional[date] = None):
    """应用日期范围过滤"""
    if not hasattr(model_class, field_name):
        return statement
    
    field = getattr(model_class, field_name)
    
    if start_date:
        statement = statement.where(field >= start_date)
    if end_date:
        statement = statement.where(field <= end_date)
    
    return statement


def apply_datetime_range(statement, model_class: Type[T], field_name: str,
                        start_time: Optional[datetime] = None, end_time: Optional[datetime] = None):
    """应用时间范围过滤"""
    if not hasattr(model_class, field_name):
        return statement
    
    field = getattr(model_class, field_name)
    
    if start_time:
        statement = statement.where(field >= start_time)
    if end_time:
        statement = statement.where(field <= end_time)
    
    return statement


def apply_sorting(statement, model_class: Type[T], sort_field: str = "created_at", sort_desc: bool = True):
    """应用排序"""
    if hasattr(model_class, sort_field):
        field = getattr(model_class, sort_field)
        if sort_desc:
            statement = statement.order_by(field.desc())
        else:
            statement = statement.order_by(field.asc())
    
    return statement


def paginate_query(statement, page: int = 1, size: int = 20):
    """应用分页"""
    offset = (page - 1) * size
    return statement.offset(offset).limit(size)


def search_with_pagination(
    session: Session,
    model_class: Type[T],
    search_term: Optional[str] = None,
    search_fields: Optional[List[str]] = None,
    filters: Optional[Dict[str, Any]] = None,
    page: int = 1,
    size: int = 20,
    sort_field: str = "created_at",
    sort_desc: bool = True
) -> Tuple[List[T], int]:
    """通用搜索和分页 - 一站式解决方案"""
    
    # 基础查询
    statement = select(model_class)
    
    # 应用搜索
    if search_term and search_fields:
        statement = apply_text_search(statement, model_class, search_term, search_fields)
    
    # 应用过滤条件
    if filters:
        statement = apply_filters(statement, model_class, filters)
    
    # 计算总数（在分页前）
    count_statement = select(func.count()).select_from(statement.subquery())
    total = session.exec(count_statement).one()
    
    # 应用排序和分页
    statement = apply_sorting(statement, model_class, sort_field, sort_desc)
    statement = paginate_query(statement, page, size)
    
    # 执行查询
    items = session.exec(statement).all()
    
    return items, total


def find_by_unique_field(session: Session, model_class: Type[T], field_name: str, value: Any) -> Optional[T]:
    """根据唯一字段查找记录"""
    if not hasattr(model_class, field_name):
        return None
    
    field = getattr(model_class, field_name)
    statement = select(model_class).where(field == value)
    return session.exec(statement).first()


def count_by_filters(session: Session, model_class: Type[T], filters: Dict[str, Any]) -> int:
    """根据过滤条件计算数量"""
    statement = select(func.count(model_class.id))
    statement = apply_filters(statement, model_class, filters)
    return session.exec(statement).one()


def exists_by_field(session: Session, model_class: Type[T], field_name: str, value: Any, 
                   exclude_id: Optional[int] = None) -> bool:
    """检查字段值是否存在（用于唯一性验证）"""
    if not hasattr(model_class, field_name):
        return False
    
    field = getattr(model_class, field_name)
    statement = select(model_class).where(field == value)
    
    # 排除指定ID（用于更新时验证）
    if exclude_id and hasattr(model_class, 'id'):
        statement = statement.where(model_class.id != exclude_id)
    
    result = session.exec(statement).first()
    return result is not None


# 便捷的查询组合函数
def get_active_items(session: Session, model_class: Type[T], 
                    page: int = 1, size: int = 20) -> Tuple[List[T], int]:
    """获取活跃/启用状态的记录"""
    filters = {}
    
    # 尝试常见的状态字段
    if hasattr(model_class, 'status'):
        filters['status'] = 'active'
    elif hasattr(model_class, 'is_active'):
        filters['is_active'] = True
    elif hasattr(model_class, 'is_deleted'):
        filters['is_deleted'] = False
    
    return search_with_pagination(
        session=session,
        model_class=model_class,
        filters=filters,
        page=page,
        size=size
    )


def search_by_name(session: Session, model_class: Type[T], name: str, 
                  page: int = 1, size: int = 20) -> Tuple[List[T], int]:
    """按名称搜索记录"""
    search_fields = []
    
    # 尝试常见的名称字段
    for field_name in ['name', 'title', 'username', 'real_name']:
        if hasattr(model_class, field_name):
            search_fields.append(field_name)
    
    return search_with_pagination(
        session=session,
        model_class=model_class,
        search_term=name,
        search_fields=search_fields,
        page=page,
        size=size
    ) 