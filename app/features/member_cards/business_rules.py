"""
会员卡业务规则和验证逻辑

包含会员卡相关的业务约束、验证规则和辅助函数
"""

from typing import Optional, List
from sqlmodel import Session, select
from .models import (
    MemberCard,
    CardType,
    CardStatus,
    MemberCardOperationType
)


class MemberCardBusinessError(Exception):
    """会员卡业务异常"""
    pass


class MemberCardBusinessRules:
    """会员卡业务规则类"""
    
    @staticmethod
    def validate_member_card_limit(session: Session, member_id: int, card_type: CardType) -> bool:
        """
        验证会员卡数量限制
        
        业务规则：
        - 每个会员只能有一个储值卡
        - 可以有多个次卡
        
        Args:
            session: 数据库会话
            member_id: 会员ID
            card_type: 卡片类型
            
        Returns:
            bool: 是否符合限制规则
            
        Raises:
            MemberCardBusinessError: 违反业务规则时抛出
        """
        if card_type in [CardType.VALUE_LIMITED, CardType.VALUE_UNLIMITED]:
            # 检查是否已有储值卡
            existing_value_card = session.exec(
                select(MemberCard).where(
                    MemberCard.member_id == member_id,
                    MemberCard.card_type.in_([CardType.VALUE_LIMITED, CardType.VALUE_UNLIMITED]),
                    MemberCard.status != CardStatus.CANCELLED
                )
            ).first()
            
            if existing_value_card:
                raise MemberCardBusinessError(f"会员 {member_id} 已有储值卡，不能重复创建")
        
        return True
    
    @staticmethod
    def validate_balance_sufficient(session: Session, card_id: int, amount: int) -> bool:
        """
        验证余额是否充足
        
        Args:
            session: 数据库会话
            card_id: 会员卡ID
            amount: 需要扣除的金额
            
        Returns:
            bool: 余额是否充足
            
        Raises:
            MemberCardBusinessError: 余额不足时抛出
        """
        card = session.get(MemberCard, card_id)
        if not card:
            raise MemberCardBusinessError(f"会员卡 {card_id} 不存在")
        
        if card.status != CardStatus.ACTIVE:
            raise MemberCardBusinessError(f"会员卡 {card_id} 状态异常，当前状态：{card.status}")
        
        if card.balance < amount:
            raise MemberCardBusinessError(
                f"会员卡 {card_id} 余额不足，当前余额：{card.balance}，需要：{amount}"
            )
        
        return True
    
    @staticmethod
    def validate_card_status_transition(current_status: CardStatus, new_status: CardStatus) -> bool:
        """
        验证卡片状态转换是否合法
        
        合法的状态转换：
        - ACTIVE -> FROZEN, EXPIRED, CANCELLED
        - FROZEN -> ACTIVE, CANCELLED
        - EXPIRED -> CANCELLED (可选)
        - CANCELLED -> 不可转换到其他状态
        
        Args:
            current_status: 当前状态
            new_status: 目标状态
            
        Returns:
            bool: 状态转换是否合法
            
        Raises:
            MemberCardBusinessError: 非法状态转换时抛出
        """
        valid_transitions = {
            CardStatus.ACTIVE: [CardStatus.FROZEN, CardStatus.EXPIRED, CardStatus.CANCELLED],
            CardStatus.FROZEN: [CardStatus.ACTIVE, CardStatus.CANCELLED],
            CardStatus.EXPIRED: [CardStatus.CANCELLED],
            CardStatus.CANCELLED: []  # 注销状态不可转换
        }
        
        if new_status not in valid_transitions.get(current_status, []):
            raise MemberCardBusinessError(
                f"非法的状态转换：{current_status} -> {new_status}"
            )
        
        return True
    
    @staticmethod
    def calculate_balance_after_transaction(
        current_balance: int,
        operation_type: MemberCardOperationType,
        amount: int
    ) -> int:
        """
        计算交易后的余额

        Args:
            current_balance: 当前余额
            operation_type: 操作类型
            amount: 交易金额（绝对值）

        Returns:
            int: 交易后余额
        """
        # 增加余额的操作类型
        credit_types = [
            MemberCardOperationType.RECHARGE,
            MemberCardOperationType.INITIAL_BINDING,
            MemberCardOperationType.MEMBER_CANCEL_BOOKING,
            MemberCardOperationType.ADMIN_CANCEL_BOOKING,
            MemberCardOperationType.REFUND
        ]

        # 减少余额的操作类型
        debit_types = [
            MemberCardOperationType.DIRECT_BOOKING,
            MemberCardOperationType.FIXED_SCHEDULE_BOOKING,
            MemberCardOperationType.ADMIN_BOOKING,
            MemberCardOperationType.MANUAL_DEDUCTION
        ]

        if operation_type in credit_types:
            return current_balance + amount
        elif operation_type in debit_types:
            return current_balance - amount
        else:
            # 其他类型（如状态变更）不影响余额
            return current_balance
    
    @staticmethod
    def generate_card_number(session: Session, tenant_id: int) -> str:
        """
        生成会员卡号
        
        格式：{tenant_id}{timestamp}{sequence}
        例如：100120250703001
        
        Args:
            session: 数据库会话
            tenant_id: 租户ID
            
        Returns:
            str: 生成的卡号
        """
        from datetime import datetime
        
        # 获取当前时间戳（YYYYMMDD格式）
        timestamp = datetime.now().strftime("%Y%m%d")
        
        # 查询当天已生成的卡号数量
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_count = session.exec(
            select(MemberCard).where(
                MemberCard.tenant_id == tenant_id,
                MemberCard.created_at >= today_start,
                MemberCard.card_number.is_not(None)
            )
        ).all()
        
        sequence = len(today_count) + 1
        card_number = f"{tenant_id}{timestamp}{sequence:03d}"
        
        return card_number
    
    @staticmethod
    def validate_operation_amount(amount: int, operation_type: MemberCardOperationType) -> bool:
        """
        验证操作金额是否合法

        Args:
            amount: 操作金额
            operation_type: 操作类型

        Returns:
            bool: 金额是否合法

        Raises:
            MemberCardBusinessError: 金额不合法时抛出
        """
        if amount <= 0:
            raise MemberCardBusinessError(f"操作金额必须大于0，当前金额：{amount}")

        # 可以根据不同操作类型设置不同的金额限制
        max_amounts = {
            MemberCardOperationType.RECHARGE: 100000,  # 充值最大10万元
            MemberCardOperationType.DIRECT_BOOKING: 1000,  # 单次约课最大1000元
            MemberCardOperationType.MANUAL_DEDUCTION: 10000,  # 人工扣费最大1万元
            MemberCardOperationType.REFUND: 50000,  # 退款最大5万元
        }

        max_amount = max_amounts.get(operation_type, 100000)  # 默认最大10万元
        if amount > max_amount:
            raise MemberCardBusinessError(
                f"操作金额超过限制，最大允许：{max_amount}，当前金额：{amount}"
            )

        return True


class MemberCardQueryHelper:
    """会员卡查询辅助类"""
    
    @staticmethod
    def get_member_active_cards(session: Session, member_id: int) -> List[MemberCard]:
        """获取会员的所有激活状态会员卡"""
        return session.exec(
            select(MemberCard).where(
                MemberCard.member_id == member_id,
                MemberCard.status == CardStatus.ACTIVE
            )
        ).all()
    
    @staticmethod
    def get_member_value_card(session: Session, member_id: int) -> Optional[MemberCard]:
        """获取会员的储值卡"""
        return session.exec(
            select(MemberCard).where(
                MemberCard.member_id == member_id,
                MemberCard.card_type.in_([CardType.VALUE_LIMITED, CardType.VALUE_UNLIMITED]),
                MemberCard.status != CardStatus.CANCELLED
            )
        ).first()
    
    @staticmethod
    def get_member_times_cards(session: Session, member_id: int) -> List[MemberCard]:
        """获取会员的所有次卡"""
        return session.exec(
            select(MemberCard).where(
                MemberCard.member_id == member_id,
                MemberCard.card_type.in_([CardType.TIMES_LIMITED, CardType.TIMES_UNLIMITED]),
                MemberCard.status == CardStatus.ACTIVE
            )
        ).all()
