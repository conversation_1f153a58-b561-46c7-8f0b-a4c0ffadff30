"""
会员卡实例管理服务

提供会员卡实例的CRUD操作和业务逻辑
"""

from typing import List, Optional, Tuple
from sqlmodel import Session, select, and_, or_, func
from datetime import datetime, timezone, timedelta
import uuid

from app.features.base.base_service import TenantAwareService
from app.features.base.query_utils import search_with_pagination
from app.api.common.exceptions import BusinessException

from .models import MemberCard, MemberCardTemplate, CardType, CardStatus
from .schemas import (
    MemberCardCreate,
    MemberCardUpdate,
    MemberCardQuery,
    MemberCardList,
    MemberCardSummary
)
from .exceptions import (
    MemberCardBusinessException,
    MemberCardNotFoundError,
    MemberCardTemplateNotFoundError
)


class MemberCardService(TenantAwareService[MemberCard]):
    """会员卡实例管理服务"""
    
    @property
    def model_class(self):
        return MemberCard
    
    def create_card(self, card_data: MemberCardCreate, created_by: Optional[int] = None) -> MemberCard:
        """创建会员卡实例"""
        
        # 总是视为会员存在，不用做验证
        # 必须有指定模版，否则抛出异常

        # 验证模板必须存在
        if not card_data.template_id:
            raise BusinessException("创建会员卡必须指定模板ID")

        template = self.session.get(MemberCardTemplate, card_data.template_id)
        if not template:
            raise MemberCardTemplateNotFoundError(card_data.template_id)

        # 验证模板是否激活
        if not template.is_active:
            raise MemberCardBusinessException.invalid_status_transition("inactive_template", "create_card")
        
        # 业务验证：检查会员是否已有相同类型的卡片（储值卡只能有一张）
        if card_data.card_type in [CardType.VALUE_LIMITED, CardType.VALUE_UNLIMITED]:
            existing_card = self._get_member_value_card(card_data.member_id)
            if existing_card:
                raise MemberCardBusinessException.duplicate_card_for_member(
                    card_data.member_id, card_data.card_type.value
                )
        
        # 准备卡片数据
        card_dict = card_data.model_dump()
        
        # 生成卡号
        if not card_dict.get('card_number'):
            card_dict['card_number'] = self._generate_card_number(card_data.card_type)
        
        # 设置过期时间（如果是有期限卡片且模板有有效期配置）
        if template and template.validity_days and card_data.card_type in [CardType.TIMES_LIMITED, CardType.VALUE_LIMITED]:
            if not card_dict.get('expires_at'):
                card_dict['expires_at'] = datetime.now() + timedelta(days=template.validity_days)
        
        # 设置初始状态
        card_dict['status'] = CardStatus.ACTIVE
        card_dict['total_recharged'] = card_dict.get('balance', 0)
        card_dict['total_consumed'] = 0
        
        # 使用基础类的创建方法
        return self.create(card_dict, created_by)
    
    def update_card(self, card_id: int, update_data: MemberCardUpdate, updated_by: Optional[int] = None) -> Optional[MemberCard]:
        """更新会员卡"""
        
        # 检查卡片是否存在
        card = self.get_by_id(card_id)
        if not card:
            raise MemberCardNotFoundError(card_id)
        
        # 验证状态转换
        if update_data.status and update_data.status != card.status:
            if not self._is_valid_status_transition(card.status, update_data.status):
                raise MemberCardBusinessException.invalid_status_transition(
                    card.status.value, update_data.status.value
                )
        
        # 验证余额更新
        if update_data.balance is not None and update_data.balance < 0:
            raise MemberCardBusinessException.negative_balance_not_allowed(card_id)
        
        # 使用基础类的更新方法
        return self.update(card_id, update_data, updated_by)
    
    def get_card(self, card_id: int) -> Optional[MemberCard]:
        """获取会员卡"""
        return self.get_by_id(card_id)
    
    def get_cards(self, query_params: MemberCardQuery) -> Tuple[List[MemberCardList], int]:
        """获取会员卡列表（支持搜索、筛选、分页）"""
        
        # 构建筛选条件
        filters = {}
        if query_params.member_id:
            filters['member_id'] = query_params.member_id
        if query_params.card_type:
            filters['card_type'] = query_params.card_type
        if query_params.status:
            filters['status'] = query_params.status
        if query_params.template_id:
            filters['template_id'] = query_params.template_id
        
        # 使用工具函数进行搜索和分页
        cards, total = search_with_pagination(
            session=self.session,
            model_class=MemberCard,
            search_term=query_params.search_keyword,
            search_fields=['card_number'],  # 可以扩展到会员姓名等
            filters=filters,
            page=query_params.page,
            size=query_params.size,
            sort_field=query_params.sort_by,
            sort_desc=(query_params.sort_order == "desc")
        )
        
        # 转换为列表模型
        card_list = [
            MemberCardList(
                id=card.id,
                member_id=card.member_id,
                card_type=card.card_type,
                balance=card.balance,
                status=card.status,
                card_number=card.card_number,
                expires_at=card.expires_at,
                last_used_at=card.last_used_at,
                created_at=card.created_at
            )
            for card in cards
        ]
        
        return card_list, total
    
    def get_member_cards(self, member_id: int) -> List[MemberCardSummary]:
        """获取会员的所有卡片摘要"""
        statement = select(MemberCard).where(
            MemberCard.member_id == member_id
        ).order_by(MemberCard.created_at.desc())
        
        cards = self.session.exec(statement).all()
        
        return [
            MemberCardSummary(
                id=card.id,
                card_type=card.card_type,
                balance=card.balance,
                status=card.status,
                expires_at=card.expires_at,
                last_used_at=card.last_used_at
            )
            for card in cards
        ]
    
    def get_member_active_cards(self, member_id: int, card_type: Optional[CardType] = None) -> List[MemberCard]:
        """获取会员的激活状态卡片"""
        statement = select(MemberCard).where(
            and_(
                MemberCard.member_id == member_id,
                MemberCard.status == CardStatus.ACTIVE
            )
        )
        
        if card_type:
            statement = statement.where(MemberCard.card_type == card_type)
        
        # 检查过期时间
        now = datetime.now()
        statement = statement.where(
            or_(
                MemberCard.expires_at.is_(None),
                MemberCard.expires_at > now
            )
        )
        
        return self.session.exec(statement).all()
    
    def freeze_card(self, card_id: int, reason: str, updated_by: Optional[int] = None) -> Optional[MemberCard]:
        """冻结会员卡"""
        return self.update_card(
            card_id,
            MemberCardUpdate(status=CardStatus.FROZEN, freeze_reason=reason),
            updated_by
        )
    
    def unfreeze_card(self, card_id: int, updated_by: Optional[int] = None) -> Optional[MemberCard]:
        """解冻会员卡"""
        return self.update_card(
            card_id,
            MemberCardUpdate(status=CardStatus.ACTIVE, freeze_reason=None),
            updated_by
        )
    
    def cancel_card(self, card_id: int, reason: str, updated_by: Optional[int] = None) -> Optional[MemberCard]:
        """注销会员卡"""
        return self.update_card(
            card_id,
            MemberCardUpdate(status=CardStatus.CANCELLED, cancel_reason=reason),
            updated_by
        )
    
    def check_card_usability(self, card_id: int) -> dict:
        """检查会员卡可用性"""
        card = self.get_by_id(card_id)
        if not card:
            return {"usable": False, "reason": "卡片不存在"}
        
        # 检查状态
        if card.status != CardStatus.ACTIVE:
            return {"usable": False, "reason": f"卡片状态：{card.status.value}"}
        
        # 检查过期时间
        if card.expires_at and card.expires_at <= datetime.now():
            return {"usable": False, "reason": "卡片已过期"}
        
        # 检查余额
        if card.balance <= 0:
            return {"usable": False, "reason": "余额不足"}
        
        return {"usable": True, "reason": None}
    
    def get_expiring_cards(self, days_ahead: int = 7) -> List[MemberCard]:
        """获取即将过期的卡片"""
        future_date = datetime.now() + timedelta(days=days_ahead)
        
        statement = select(MemberCard).where(
            and_(
                MemberCard.status == CardStatus.ACTIVE,
                MemberCard.expires_at.is_not(None),
                MemberCard.expires_at <= future_date,
                MemberCard.expires_at > datetime.now()
            )
        ).order_by(MemberCard.expires_at.asc())
        
        return self.session.exec(statement).all()
    
    def update_last_used_time(self, card_id: int) -> bool:
        """更新最后使用时间"""
        card = self.get_by_id(card_id)
        if not card:
            return False
        
        card.last_used_at = datetime.now()
        self.session.add(card)
        self.session.commit()
        return True
    
    # 私有方法
    def _get_member_value_card(self, member_id: int) -> Optional[MemberCard]:
        """获取会员的储值卡（每个会员只能有一张储值卡）"""
        statement = select(MemberCard).where(
            and_(
                MemberCard.member_id == member_id,
                MemberCard.card_type.in_([CardType.VALUE_LIMITED, CardType.VALUE_UNLIMITED])
            )
        )
        
        return self.session.exec(statement).first()
    
    def _generate_card_number(self, card_type: CardType) -> str:
        """生成卡号"""
        # 根据卡片类型生成前缀
        prefix_map = {
            CardType.TIMES_LIMITED: "TL",
            CardType.TIMES_UNLIMITED: "TU", 
            CardType.VALUE_LIMITED: "VL",
            CardType.VALUE_UNLIMITED: "VU"
        }
        
        prefix = prefix_map.get(card_type, "MC")
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_suffix = str(uuid.uuid4())[:8].upper()
        
        return f"{prefix}{timestamp}{random_suffix}"
    
    def _is_valid_status_transition(self, current_status: CardStatus, target_status: CardStatus) -> bool:
        """验证状态转换是否有效"""
        valid_transitions = {
            CardStatus.ACTIVE: [CardStatus.FROZEN, CardStatus.CANCELLED],
            CardStatus.FROZEN: [CardStatus.ACTIVE, CardStatus.CANCELLED],
            CardStatus.EXPIRED: [CardStatus.CANCELLED],
            CardStatus.CANCELLED: []  # 注销状态不能转换到其他状态
        }
        
        return target_status in valid_transitions.get(current_status, [])


def get_member_card_service(session: Session, tenant_id: int) -> MemberCardService:
    """获取会员卡服务实例"""
    return MemberCardService(session, tenant_id)
