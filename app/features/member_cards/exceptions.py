"""
会员卡模块异常定义

包含会员卡相关的业务异常和错误处理
"""

from enum import Enum
from app.api.common.exceptions import BusinessException, NotFoundError


class MemberCardErrorCode(str, Enum):
    """会员卡错误代码枚举"""
    
    # 会员卡模板相关错误
    TEMPLATE_NAME_EXISTS = "TEMPLATE_NAME_EXISTS"
    TEMPLATE_NOT_FOUND = "TEMPLATE_NOT_FOUND"
    INVALID_TIMES_CARD_CONFIG = "INVALID_TIMES_CARD_CONFIG"
    INVALID_VALUE_CARD_CONFIG = "INVALID_VALUE_CARD_CONFIG"
    LIMITED_CARD_REQUIRES_VALIDITY = "LIMITED_CARD_REQUIRES_VALIDITY"
    TEMPLATE_IN_USE = "TEMPLATE_IN_USE"
    
    # 会员卡实例相关错误
    MEMBER_CARD_NOT_FOUND = "MEMBER_CARD_NOT_FOUND"
    MEMBER_CARD_EXPIRED = "MEMBER_CARD_EXPIRED"
    MEMBER_CARD_FROZEN = "MEMBER_CARD_FROZEN"
    MEMBER_CARD_CANCELLED = "MEMBER_CARD_CANCELLED"
    DUPLICATE_CARD_FOR_MEMBER = "DUPLICATE_CARD_FOR_MEMBER"
    INVALID_CARD_STATUS_TRANSITION = "INVALID_CARD_STATUS_TRANSITION"
    
    # 余额相关错误
    INSUFFICIENT_BALANCE = "INSUFFICIENT_BALANCE"
    INVALID_AMOUNT = "INVALID_AMOUNT"
    NEGATIVE_BALANCE_NOT_ALLOWED = "NEGATIVE_BALANCE_NOT_ALLOWED"
    
    # 充值相关错误
    RECHARGE_AMOUNT_TOO_SMALL = "RECHARGE_AMOUNT_TOO_SMALL"
    RECHARGE_AMOUNT_TOO_LARGE = "RECHARGE_AMOUNT_TOO_LARGE"
    INVALID_PAYMENT_METHOD = "INVALID_PAYMENT_METHOD"
    RECHARGE_FAILED = "RECHARGE_FAILED"
    
    # 消费相关错误
    CONSUMPTION_AMOUNT_INVALID = "CONSUMPTION_AMOUNT_INVALID"
    CONSUMPTION_FAILED = "CONSUMPTION_FAILED"
    REFUND_AMOUNT_INVALID = "REFUND_AMOUNT_INVALID"
    REFUND_FAILED = "REFUND_FAILED"
    
    # 操作记录相关错误
    OPERATION_NOT_FOUND = "OPERATION_NOT_FOUND"
    OPERATION_CANNOT_BE_REVERSED = "OPERATION_CANNOT_BE_REVERSED"
    
    # 业务规则相关错误
    MEMBER_NOT_FOUND = "MEMBER_NOT_FOUND"
    SCHEDULED_CLASS_NOT_FOUND = "SCHEDULED_CLASS_NOT_FOUND"
    CARD_TYPE_MISMATCH = "CARD_TYPE_MISMATCH"
    OPERATION_NOT_ALLOWED = "OPERATION_NOT_ALLOWED"


# ==================== 会员卡模板异常 ====================

class MemberCardTemplateNotFoundError(NotFoundError):
    """会员卡模板未找到异常"""

    def __init__(self, template_id: int):
        super().__init__(
            resource=f"会员卡模板(ID: {template_id})",
            details={"template_id": template_id}
        )


class MemberCardTemplateBusinessException(BusinessException):
    """会员卡模板业务异常"""
    
    @classmethod
    def template_name_already_exists(cls, name: str):
        return cls(
            message=f"会员卡模板名称已存在：{name}",
            code=MemberCardErrorCode.TEMPLATE_NAME_EXISTS,
            details={"template_name": name}
        )

    @classmethod
    def invalid_times_card_configuration(cls):
        return cls(
            message="次卡类型必须设置有效的可用次数",
            code=MemberCardErrorCode.INVALID_TIMES_CARD_CONFIG
        )

    @classmethod
    def invalid_value_card_configuration(cls):
        return cls(
            message="储值卡的可用余额不能超过售价",
            code=MemberCardErrorCode.INVALID_VALUE_CARD_CONFIG
        )

    @classmethod
    def limited_card_requires_validity(cls):
        return cls(
            message="有期限卡片必须设置有效期天数",
            code=MemberCardErrorCode.LIMITED_CARD_REQUIRES_VALIDITY
        )

    @classmethod
    def template_in_use(cls, template_id: int, card_count: int):
        return cls(
            message=f"模板正在使用中，无法删除。关联会员卡数量：{card_count}",
            code=MemberCardErrorCode.TEMPLATE_IN_USE,
            details={"template_id": template_id, "card_count": card_count}
        )


# ==================== 会员卡实例异常 ====================

class MemberCardNotFoundError(NotFoundError):
    """会员卡未找到异常"""

    def __init__(self, card_id: int):
        super().__init__(
            resource=f"会员卡(ID: {card_id})",
            details={"card_id": card_id}
        )


class MemberCardBusinessException(BusinessException):
    """会员卡业务异常"""
    
    @classmethod
    def card_expired(cls, card_id: int, expires_at: str):
        return cls(
            message=f"会员卡已过期：{expires_at}",
            code=MemberCardErrorCode.MEMBER_CARD_EXPIRED,
            details={"card_id": card_id, "expires_at": expires_at}
        )

    @classmethod
    def card_frozen(cls, card_id: int, reason: str = None):
        return cls(
            message=f"会员卡已冻结{f'：{reason}' if reason else ''}",
            code=MemberCardErrorCode.MEMBER_CARD_FROZEN,
            details={"card_id": card_id, "freeze_reason": reason}
        )

    @classmethod
    def card_cancelled(cls, card_id: int, reason: str = None):
        return cls(
            message=f"会员卡已注销{f'：{reason}' if reason else ''}",
            code=MemberCardErrorCode.MEMBER_CARD_CANCELLED,
            details={"card_id": card_id, "cancel_reason": reason}
        )

    @classmethod
    def duplicate_card_for_member(cls, member_id: int, card_type: str):
        return cls(
            message=f"会员已存在该类型的卡片：{card_type}",
            code=MemberCardErrorCode.DUPLICATE_CARD_FOR_MEMBER,
            details={"member_id": member_id, "card_type": card_type}
        )

    @classmethod
    def invalid_status_transition(cls, current_status: str, target_status: str):
        return cls(
            message=f"无效的状态转换：{current_status} -> {target_status}",
            code=MemberCardErrorCode.INVALID_CARD_STATUS_TRANSITION,
            details={"current_status": current_status, "target_status": target_status}
        )
    
    @classmethod
    def insufficient_balance(cls, card_id: int, current_balance: int, required_amount: int):
        return cls(
            message=f"余额不足。当前余额：{current_balance}，所需金额：{required_amount}",
            code=MemberCardErrorCode.INSUFFICIENT_BALANCE,
            details={
                "card_id": card_id,
                "current_balance": current_balance,
                "required_amount": required_amount,
                "shortage": required_amount - current_balance
            }
        )

    @classmethod
    def invalid_amount(cls, amount: int, reason: str = "金额无效"):
        return cls(
            message=f"{reason}：{amount}",
            code=MemberCardErrorCode.INVALID_AMOUNT,
            details={"amount": amount, "reason": reason}
        )

    @classmethod
    def negative_balance_not_allowed(cls, card_id: int):
        return cls(
            message="不允许余额为负数",
            code=MemberCardErrorCode.NEGATIVE_BALANCE_NOT_ALLOWED,
            details={"card_id": card_id}
        )


# ==================== 充值相关异常 ====================

class RechargeBusinessException(BusinessException):
    """充值业务异常"""
    
    @classmethod
    def amount_too_small(cls, amount: int, min_amount: int):
        return cls(
            message=f"充值金额过小。最小充值金额：{min_amount}元",
            code=MemberCardErrorCode.RECHARGE_AMOUNT_TOO_SMALL,
            details={"amount": amount, "min_amount": min_amount}
        )

    @classmethod
    def amount_too_large(cls, amount: int, max_amount: int):
        return cls(
            message=f"充值金额过大。最大充值金额：{max_amount}元",
            code=MemberCardErrorCode.RECHARGE_AMOUNT_TOO_LARGE,
            details={"amount": amount, "max_amount": max_amount}
        )

    @classmethod
    def invalid_payment_method(cls, payment_method: str):
        return cls(
            message=f"不支持的支付方式：{payment_method}",
            code=MemberCardErrorCode.INVALID_PAYMENT_METHOD,
            details={"payment_method": payment_method}
        )

    @classmethod
    def recharge_failed(cls, reason: str):
        return cls(
            message=f"充值失败：{reason}",
            code=MemberCardErrorCode.RECHARGE_FAILED,
            details={"reason": reason}
        )


# ==================== 消费相关异常 ====================

class ConsumptionBusinessException(BusinessException):
    """消费业务异常"""
    
    @classmethod
    def consumption_amount_invalid(cls, amount: int, reason: str):
        return cls(
            message=f"消费金额无效：{reason}",
            code=MemberCardErrorCode.CONSUMPTION_AMOUNT_INVALID,
            details={"amount": amount, "reason": reason}
        )

    @classmethod
    def consumption_failed(cls, reason: str):
        return cls(
            message=f"消费失败：{reason}",
            code=MemberCardErrorCode.CONSUMPTION_FAILED,
            details={"reason": reason}
        )

    @classmethod
    def refund_amount_invalid(cls, amount: int, max_refund: int):
        return cls(
            message=f"退款金额无效。最大可退款金额：{max_refund}元",
            code=MemberCardErrorCode.REFUND_AMOUNT_INVALID,
            details={"amount": amount, "max_refund": max_refund}
        )

    @classmethod
    def refund_failed(cls, reason: str):
        return cls(
            message=f"退款失败：{reason}",
            code=MemberCardErrorCode.REFUND_FAILED,
            details={"reason": reason}
        )


# ==================== 操作记录异常 ====================

class MemberCardOperationNotFoundError(NotFoundError):
    """会员卡操作记录未找到异常"""

    def __init__(self, operation_id: int):
        super().__init__(
            resource=f"会员卡操作记录(ID: {operation_id})",
            details={"operation_id": operation_id}
        )


class MemberCardOperationBusinessException(BusinessException):
    """会员卡操作业务异常"""
    
    @classmethod
    def operation_cannot_be_reversed(cls, operation_id: int, operation_type: str):
        return cls(
            message=f"操作无法撤销：{operation_type}",
            code=MemberCardErrorCode.OPERATION_CANNOT_BE_REVERSED,
            details={"operation_id": operation_id, "operation_type": operation_type}
        )


# ==================== 通用业务异常 ====================

class MemberCardGeneralException(BusinessException):
    """会员卡通用业务异常"""
    
    @classmethod
    def member_not_found(cls, member_id: int):
        return cls(
            message=f"会员不存在：ID {member_id}",
            code=MemberCardErrorCode.MEMBER_NOT_FOUND,
            details={"member_id": member_id}
        )

    @classmethod
    def scheduled_class_not_found(cls, class_id: int):
        return cls(
            message=f"课程不存在：ID {class_id}",
            code=MemberCardErrorCode.SCHEDULED_CLASS_NOT_FOUND,
            details={"class_id": class_id}
        )

    @classmethod
    def card_type_mismatch(cls, expected_type: str, actual_type: str):
        return cls(
            message=f"卡片类型不匹配。期望：{expected_type}，实际：{actual_type}",
            code=MemberCardErrorCode.CARD_TYPE_MISMATCH,
            details={"expected_type": expected_type, "actual_type": actual_type}
        )

    @classmethod
    def operation_not_allowed(cls, operation: str, reason: str):
        return cls(
            message=f"操作不被允许：{operation}。原因：{reason}",
            code=MemberCardErrorCode.OPERATION_NOT_ALLOWED,
            details={"operation": operation, "reason": reason}
        )
