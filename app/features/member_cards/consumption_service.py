"""
会员卡扣费/消费服务

提供会员卡扣费相关的业务逻辑，包括余额验证、扣费、退费等操作
"""

from typing import List, Optional, Tuple
from sqlmodel import Session, select, and_, or_, func
from datetime import datetime, timezone
import uuid

from app.features.base.base_service import TenantAwareService

from .models import (
    MemberCard, MemberCardOperation, CardStatus, CardType,
    MemberCardOperationType
)
from .schemas import (
    ConsumptionRequest,
    ConsumptionResponse,
    BalanceCheckRequest,
    BalanceCheckResponse,
    CourseBookingBalanceCheck,
    CourseBookingBalanceResponse,
    CourseBookingDeduction,
    MemberCardSummary
)
from .exceptions import (
    MemberCardNotFoundError,
    MemberCardBusinessException,
    ConsumptionBusinessException,
    MemberCardGeneralException,
    MemberCardOperationNotFoundError
)


class ConsumptionService(TenantAwareService[MemberCardOperation]):
    """扣费/消费服务"""
    
    @property
    def model_class(self):
        return MemberCardOperation
    
    def __init__(self, session: Session, tenant_id: int):
        super().__init__(session, tenant_id)
        # 扣费限制配置
        self.min_consumption_amount = 1  # 最小扣费金额（元）
        self.max_consumption_amount = 10000  # 最大单次扣费金额（元）
    
    def check_balance(self, request: BalanceCheckRequest) -> BalanceCheckResponse:
        """检查会员卡余额是否充足"""
        
        card = self.session.get(MemberCard, request.member_card_id)
        if not card:
            raise MemberCardNotFoundError(request.member_card_id)
        
        # 检查卡片可用性
        self._validate_card_for_consumption(card)
        
        current_balance = card.balance
        is_sufficient = current_balance >= request.required_amount
        shortage_amount = max(0, request.required_amount - current_balance)
        
        return BalanceCheckResponse(
            member_card_id=card.id,
            current_balance=current_balance,
            required_amount=request.required_amount,
            is_sufficient=is_sufficient,
            shortage_amount=shortage_amount
        )
    
    def check_course_booking_balance(self, request: CourseBookingBalanceCheck) -> CourseBookingBalanceResponse:
        """检查课程预约余额（支持多卡片选择）"""
        
        # 获取会员的所有可用卡片
        available_cards = self._get_member_available_cards(request.member_id)
        
        if not available_cards:
            return CourseBookingBalanceResponse(
                can_book=False,
                selected_card_id=None,
                selected_card_type=None,
                current_balance=None,
                required_amount=request.course_price,
                shortage_amount=request.course_price,
                available_cards=[]
            )
        
        # 如果指定了优先卡片，先检查该卡片
        selected_card = None
        if request.preferred_card_id:
            preferred_card = next((card for card in available_cards if card.id == request.preferred_card_id), None)
            if preferred_card and preferred_card.balance >= request.course_price:
                selected_card = preferred_card
        
        # 如果没有指定或指定的卡片余额不足，选择余额最充足的卡片
        if not selected_card:
            # 只会选择储值卡，不支持自动选择次卡
            value_cards = [card for card in available_cards if card.card_type in [CardType.VALUE_LIMITED, CardType.VALUE_UNLIMITED]]
            
            # 先从储值卡中选择余额充足的
            for card in sorted(value_cards, key=lambda x: x.balance, reverse=True):
                if card.balance >= request.course_price:
                    selected_card = card
                    break
            
        # 转换为摘要格式
        available_card_summaries = [
            MemberCardSummary(
                id=card.id,
                card_type=card.card_type,
                balance=card.balance,
                status=card.status,
                expires_at=card.expires_at,
                last_used_at=card.last_used_at
            )
            for card in available_cards
        ]
        
        if selected_card:
            return CourseBookingBalanceResponse(
                can_book=True,
                selected_card_id=selected_card.id,
                selected_card_type=selected_card.card_type,
                current_balance=selected_card.balance,
                required_amount=request.course_price,
                shortage_amount=0,
                available_cards=available_card_summaries
            )
        else:
            # 计算最大可用余额
            max_balance = max(card.balance for card in available_cards) if available_cards else 0
            shortage = request.course_price - max_balance
            
            return CourseBookingBalanceResponse(
                can_book=False,
                selected_card_id=None,
                selected_card_type=None,
                current_balance=max_balance,
                required_amount=request.course_price,
                shortage_amount=shortage,
                available_cards=available_card_summaries
            )
    
    def consume_without_commit(self, consumption_data: ConsumptionRequest, operator_id: Optional[int] = None) -> Tuple[ConsumptionResponse, MemberCardOperation]:
        """执行扣费操作但不提交事务，返回响应和操作记录对象
        
        Args:
            consumption_data: 消费请求数据
            operator_id: 操作员ID
            
        Returns:
            Tuple[ConsumptionResponse, MemberCardOperation]: 消费响应和操作记录对象
            
        Raises:
            MemberCardNotFoundError: 会员卡不存在
            MemberCardBusinessException: 会员卡状态异常
            ConsumptionBusinessException: 扣费业务异常
        """
        # 验证会员卡
        card = self.session.get(MemberCard, consumption_data.member_card_id)
        if not card:
            raise MemberCardNotFoundError(consumption_data.member_card_id)
        
        # 验证卡片状态
        self._validate_card_for_consumption(card)
        
        # 验证扣费金额
        self._validate_consumption_amount(consumption_data.amount, card.balance)
        
        # 验证关联课程（如果有）
        if consumption_data.scheduled_class_id:
            # TODO: 验证课程是否存在
            pass
        
        # 记录扣费前余额
        balance_before = card.balance
        balance_after = balance_before - consumption_data.amount
        
        # 更新会员卡余额和统计信息
        card.balance = balance_after
        card.total_consumed += consumption_data.amount
        card.last_used_at = datetime.now()
        card.updated_at = datetime.now()
        
        self.session.add(card)
        
        # 创建操作记录
        operation_data = {
            'tenant_id': self.tenant_id,
            'member_id': card.member_id,
            'member_card_id': card.id,
            'operation_type': self._get_consumption_operation_type(consumption_data),
            'operation_description': consumption_data.operation_description,
            'amount_change': -consumption_data.amount,  # 负数表示扣费
            'balance_before': balance_before,
            'balance_after': balance_after,
            'scheduled_class_id': consumption_data.scheduled_class_id,
            'notes': consumption_data.reason,
            'operator_id': operator_id,
            'status': 'completed'
        }
        
        # 创建操作记录但不提交事务
        operation = self.create(operation_data, operator_id, auto_commit=False)

        # 返回扣费响应和操作记录对象
        response = ConsumptionResponse(
            operation_id=operation.id,  # 现在应该有ID了
            member_card_id=card.id,
            amount=consumption_data.amount,
            balance_before=balance_before,
            balance_after=balance_after,
            scheduled_class_id=consumption_data.scheduled_class_id,
            operation_description=consumption_data.operation_description,
            created_at=operation.created_at
        )
        
        return response, operation

    def consume(self, consumption_data: ConsumptionRequest, operator_id: Optional[int] = None) -> ConsumptionResponse:
        """执行扣费操作"""
        
        try:
            # 使用不提交事务的方法
            response, operation = self.consume_without_commit(consumption_data, operator_id)
            
            # 提交事务
            self.session.commit()
            
            # 刷新对象
            card = self.session.get(MemberCard, consumption_data.member_card_id)
            self.session.refresh(card)
            self.session.refresh(operation)
            
            return response
            
        except Exception as e:
            self.session.rollback()
            raise ConsumptionBusinessException.consumption_failed(str(e))
    
    def refund_without_commit(self, original_operation_id: Optional[int], member_card_id: int,
                            refund_amount: int, reason: str, scheduled_class_id: Optional[int] = None,
                            operator_id: Optional[int] = None) -> Tuple[ConsumptionResponse, MemberCardOperation]:
        """执行退费操作但不提交事务，返回响应和操作记录对象

        Args:
            original_operation_id: 原始操作ID（可为空，用于直接退费）
            member_card_id: 会员卡ID
            refund_amount: 退费金额
            reason: 退费原因
            scheduled_class_id: 关联课程ID
            operator_id: 操作员ID

        Returns:
            Tuple[ConsumptionResponse, MemberCardOperation]: 退费响应和操作记录对象
        """
        # 如果有原始操作ID，验证原始操作
        original_operation = None
        if original_operation_id:
            original_operation = self.session.get(MemberCardOperation, original_operation_id)
            if not original_operation:
                raise MemberCardOperationNotFoundError(original_operation_id)

            # 验证原始操作是否可以退费
            if original_operation.operation_type not in [
                MemberCardOperationType.DIRECT_BOOKING,
                MemberCardOperationType.FIXED_SCHEDULE_BOOKING,
                MemberCardOperationType.ADMIN_BOOKING,
                MemberCardOperationType.MANUAL_DEDUCTION
            ]:
                raise ConsumptionBusinessException.refund_failed("该操作类型不支持退费")

            # 验证退费金额
            original_amount = abs(original_operation.amount_change or 0)
            if refund_amount <= 0 or refund_amount > original_amount:
                raise ConsumptionBusinessException.refund_amount_invalid(refund_amount, original_amount)
        else:
            # 直接退费，只验证金额大于0
            if refund_amount <= 0:
                raise ConsumptionBusinessException.refund_amount_invalid(refund_amount, 0)

        # 获取会员卡
        card = self.session.get(MemberCard, member_card_id)
        if not card:
            raise MemberCardNotFoundError(member_card_id)

        # 执行退费（相当于充值）
        balance_before = card.balance
        balance_after = balance_before + refund_amount

        # 更新会员卡余额
        card.balance = balance_after
        card.total_consumed -= refund_amount  # 减少总消费
        card.updated_at = datetime.now()

        self.session.add(card)

        # 创建退费记录
        operation_data = {
            'tenant_id': self.tenant_id,
            'member_id': card.member_id,
            'member_card_id': card.id,
            'operation_type': MemberCardOperationType.REFUND,
            'operation_description': f"退费：{reason}",
            'amount_change': refund_amount,  # 正数表示退费
            'balance_before': balance_before,
            'balance_after': balance_after,
            'scheduled_class_id': scheduled_class_id or (original_operation.scheduled_class_id if original_operation else None),
            'related_operation_id': original_operation_id,
            'notes': reason,
            'operator_id': operator_id,
            'status': 'completed'
        }

        operation = self.create(operation_data, operator_id, auto_commit=False)

        # 返回退费响应和操作记录对象
        response = ConsumptionResponse(
            operation_id=operation.id,
            member_card_id=card.id,
            amount=refund_amount,
            balance_before=balance_before,
            balance_after=balance_after,
            scheduled_class_id=scheduled_class_id or (original_operation.scheduled_class_id if original_operation else None),
            operation_description=f"退费：{reason}",
            created_at=operation.created_at
        )

        return response, operation

    def refund(self, original_operation_id: int, refund_amount: int, reason: str, operator_id: Optional[int] = None) -> ConsumptionResponse:
        """退费操作（自动提交事务）"""

        # 获取原始操作记录
        original_operation = self.session.get(MemberCardOperation, original_operation_id)
        if not original_operation:
            raise MemberCardOperationNotFoundError(original_operation_id)

        try:
            # 使用不提交事务的方法
            response, operation = self.refund_without_commit(
                original_operation_id=original_operation_id,
                member_card_id=original_operation.member_card_id,
                refund_amount=refund_amount,
                reason=reason,
                scheduled_class_id=original_operation.scheduled_class_id,
                operator_id=operator_id
            )

            # 提交事务
            self.session.commit()

            # 刷新对象
            card = self.session.get(MemberCard, original_operation.member_card_id)
            self.session.refresh(card)
            self.session.refresh(operation)

            return response

        except Exception as e:
            self.session.rollback()
            raise ConsumptionBusinessException.refund_failed(str(e))
    
    def get_consumption_history(self, member_card_id: int, limit: int = 50) -> List[MemberCardOperation]:
        """获取消费历史记录"""
        
        consumption_types = [
            MemberCardOperationType.DIRECT_BOOKING,
            MemberCardOperationType.FIXED_SCHEDULE_BOOKING,
            MemberCardOperationType.ADMIN_BOOKING,
            MemberCardOperationType.MANUAL_DEDUCTION,
            MemberCardOperationType.REFUND
        ]
        
        statement = select(MemberCardOperation).where(
            and_(
                MemberCardOperation.member_card_id == member_card_id,
                MemberCardOperation.operation_type.in_(consumption_types)
            )
        ).order_by(MemberCardOperation.created_at.desc()).limit(limit)
        
        return self.session.exec(statement).all()
    
    # 私有方法
    def _get_member_available_cards(self, member_id: int) -> List[MemberCard]:
        """获取会员的可用卡片"""
        
        statement = select(MemberCard).where(
            and_(
                MemberCard.member_id == member_id,
                MemberCard.status == CardStatus.ACTIVE,
                MemberCard.balance > 0
            )
        )
        
        # 检查过期时间
        now = datetime.now()
        statement = statement.where(
            or_(
                MemberCard.expires_at.is_(None),
                MemberCard.expires_at > now
            )
        )
        
        return self.session.exec(statement).all()
    
    def _validate_card_for_consumption(self, card: MemberCard):
        """验证会员卡是否可以扣费"""
        
        if card.status == CardStatus.CANCELLED:
            raise MemberCardBusinessException.card_cancelled(card.id, card.cancel_reason)
        
        if card.status == CardStatus.FROZEN:
            raise MemberCardBusinessException.card_frozen(card.id, card.freeze_reason)
        
        # 检查过期时间
        if card.expires_at and card.expires_at <= datetime.now():
            raise MemberCardBusinessException.card_expired(card.id, card.expires_at.isoformat())
    
    def _validate_consumption_amount(self, amount: int, current_balance: int):
        """验证扣费金额"""
        
        if amount < self.min_consumption_amount:
            raise ConsumptionBusinessException.consumption_amount_invalid(
                amount, f"扣费金额不能小于{self.min_consumption_amount}元"
            )
        
        if amount > self.max_consumption_amount:
            raise ConsumptionBusinessException.consumption_amount_invalid(
                amount, f"单次扣费金额不能超过{self.max_consumption_amount}元"
            )
        
        if amount > current_balance:
            raise MemberCardBusinessException.insufficient_balance(0, current_balance, amount)
    
    def _get_consumption_operation_type(self, consumption_data: ConsumptionRequest) -> MemberCardOperationType:
        """根据扣费数据确定操作类型"""
        
        if consumption_data.scheduled_class_id:
            # 如果有关联课程，默认为直接约课
            return MemberCardOperationType.DIRECT_BOOKING
        else:
            # 否则为人工扣费
            return MemberCardOperationType.MANUAL_DEDUCTION


def get_consumption_service(session: Session, tenant_id: int) -> ConsumptionService:
    """获取扣费服务实例"""
    return ConsumptionService(session, tenant_id)
