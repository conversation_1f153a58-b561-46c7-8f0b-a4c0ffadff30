"""
会员卡模板管理服务

提供会员卡模板的CRUD操作和业务逻辑
"""

from typing import List, Optional, Tuple
from sqlmodel import Session, select, and_, or_, func
from datetime import datetime, timezone

from app.features.base.base_service import TenantAwareService
from app.features.base.query_utils import search_with_pagination, exists_by_field

from .models import MemberCardTemplate, CardType
from .schemas import (
    MemberCardTemplateCreate,
    MemberCardTemplateUpdate,
    MemberCardTemplateQuery,
    MemberCardTemplateList
)
from .exceptions import (
    MemberCardTemplateBusinessException,
    MemberCardTemplateNotFoundError
)


class MemberCardTemplateService(TenantAwareService[MemberCardTemplate]):
    """会员卡模板管理服务"""
    
    @property
    def model_class(self):
        return MemberCardTemplate
    
    def create_template(self, template_data: MemberCardTemplateCreate, created_by: Optional[int] = None) -> MemberCardTemplate:
        """创建会员卡模板"""
        
        # 业务验证：检查模板名称唯一性
        if self._template_name_exists(template_data.name):
            raise MemberCardTemplateBusinessException.template_name_already_exists(template_data.name)
        
        # 业务验证：验证卡片类型和配置的一致性
        self._validate_template_configuration(template_data)
        
        # 使用基础类的创建方法
        template_dict = template_data.model_dump()
        return self.create(template_dict, created_by)
    
    def update_template(self, template_id: int, update_data: MemberCardTemplateUpdate, updated_by: Optional[int] = None) -> Optional[MemberCardTemplate]:
        """更新会员卡模板"""
        
        # 检查模板是否存在
        template = self.get_by_id(template_id)
        if not template:
            raise MemberCardTemplateNotFoundError(template_id)
        
        # 如果更新名称，检查唯一性
        if update_data.name and update_data.name != template.name:
            if self._template_name_exists(update_data.name):
                raise MemberCardTemplateBusinessException.template_name_already_exists(update_data.name)
        
        # 验证更新后的配置一致性
        if (hasattr(update_data, 'card_type') and update_data.card_type) or \
           (hasattr(update_data, 'available_balance') and update_data.available_balance is not None) or \
           (hasattr(update_data, 'validity_days') and update_data.validity_days is not None):
            self._validate_template_update_configuration(template, update_data)
        
        # 使用基础类的更新方法
        return self.update(template_id, update_data, updated_by)
    
    def get_template(self, template_id: int) -> Optional[MemberCardTemplate]:
        """获取会员卡模板"""
        return self.get_by_id(template_id)
    
    def get_templates(self, query_params: MemberCardTemplateQuery) -> Tuple[List[MemberCardTemplateList], int]:
        """获取会员卡模板列表（支持搜索、筛选、分页）"""
        
        # 构建基础查询
        statement = select(MemberCardTemplate)
        
        # 应用筛选条件
        filters = {}
        if query_params.card_type:
            filters['card_type'] = query_params.card_type
        if query_params.is_active is not None:
            filters['is_active'] = query_params.is_active
        if query_params.is_agent_exclusive is not None:
            filters['is_agent_exclusive'] = query_params.is_agent_exclusive
        
        # 使用工具函数进行搜索和分页
        templates, total = search_with_pagination(
            session=self.session,
            model_class=MemberCardTemplate,
            search_term=query_params.search_keyword,
            search_fields=['name', 'description'],
            filters=filters,
            page=query_params.page,
            size=query_params.size,
            sort_field=query_params.sort_by,
            sort_desc=(query_params.sort_order == "desc")
        )
        
        # 转换为列表模型
        template_list = [
            MemberCardTemplateList(
                id=template.id,
                name=template.name,
                card_type=template.card_type,
                sale_price=template.sale_price,
                available_balance=template.available_balance,
                validity_days=template.validity_days,
                is_agent_exclusive=template.is_agent_exclusive,
                is_active=template.is_active,
                created_at=template.created_at
            )
            for template in templates
        ]
        
        return template_list, total
    
    def get_active_templates(self, card_type: Optional[CardType] = None) -> List[MemberCardTemplate]:
        """获取激活的会员卡模板"""
        statement = select(MemberCardTemplate).where(
            MemberCardTemplate.is_active == True
        )
        
        if card_type:
            statement = statement.where(MemberCardTemplate.card_type == card_type)
        
        statement = statement.order_by(MemberCardTemplate.sale_price.asc())
        
        return self.session.exec(statement).all()
    
    def get_templates_for_agent(self, is_agent: bool = True) -> List[MemberCardTemplate]:
        """获取代理可售或非代理可售的模板"""
        statement = select(MemberCardTemplate).where(
            and_(
                MemberCardTemplate.is_active == True,
                MemberCardTemplate.is_agent_exclusive == is_agent
            )
        ).order_by(MemberCardTemplate.sale_price.asc())
        
        return self.session.exec(statement).all()
    
    def activate_template(self, template_id: int, updated_by: Optional[int] = None) -> Optional[MemberCardTemplate]:
        """激活模板"""
        return self.update_template(
            template_id,
            MemberCardTemplateUpdate(is_active=True),
            updated_by
        )
    
    def deactivate_template(self, template_id: int, updated_by: Optional[int] = None) -> Optional[MemberCardTemplate]:
        """停用模板"""
        return self.update_template(
            template_id,
            MemberCardTemplateUpdate(is_active=False),
            updated_by
        )
    
    def get_template_statistics(self) -> dict:
        """获取模板统计信息"""
        # 总模板数
        total_count = self.session.exec(
            select(func.count(MemberCardTemplate.id))
        ).first()
        
        # 激活模板数
        active_count = self.session.exec(
            select(func.count(MemberCardTemplate.id)).where(
                MemberCardTemplate.is_active == True
            )
        ).first()
        
        # 按类型统计
        type_stats = {}
        for card_type in CardType:
            count = self.session.exec(
                select(func.count(MemberCardTemplate.id)).where(
                    and_(
                        MemberCardTemplate.card_type == card_type,
                        MemberCardTemplate.is_active == True
                    )
                )
            ).first()
            type_stats[card_type.value] = count
        
        return {
            'total_templates': total_count,
            'active_templates': active_count,
            'inactive_templates': total_count - active_count,
            'type_statistics': type_stats
        }
    
    # 私有方法
    def _template_name_exists(self, name: str, exclude_id: Optional[int] = None) -> bool:
        """检查模板名称是否已存在"""
        statement = select(MemberCardTemplate).where(MemberCardTemplate.name == name)
        if exclude_id:
            statement = statement.where(MemberCardTemplate.id != exclude_id)
        
        return self.session.exec(statement).first() is not None
    
    def _validate_template_configuration(self, template_data: MemberCardTemplateCreate):
        """验证模板配置的一致性"""
        
        # 次卡类型必须设置可用余额（次数）
        if template_data.card_type in [CardType.TIMES_LIMITED, CardType.TIMES_UNLIMITED]:
            if not template_data.available_balance or template_data.available_balance <= 0:
                raise MemberCardTemplateBusinessException.invalid_times_card_configuration()
        
        # 储值卡类型的可用余额应该与售价相关
        if template_data.card_type in [CardType.VALUE_LIMITED, CardType.VALUE_UNLIMITED]:
            if template_data.available_balance and template_data.available_balance > template_data.sale_price:
                raise MemberCardTemplateBusinessException.invalid_value_card_configuration()
        
        # 有期限卡片必须设置有效期
        if template_data.card_type in [CardType.TIMES_LIMITED, CardType.VALUE_LIMITED]:
            if not template_data.validity_days or template_data.validity_days <= 0:
                raise MemberCardTemplateBusinessException.limited_card_requires_validity()
    
    def _validate_template_update_configuration(self, template: MemberCardTemplate, update_data: MemberCardTemplateUpdate):
        """验证模板更新配置的一致性"""
        
        # 获取更新后的值
        card_type = update_data.card_type or template.card_type
        available_balance = update_data.available_balance if update_data.available_balance is not None else template.available_balance
        validity_days = update_data.validity_days if update_data.validity_days is not None else template.validity_days
        sale_price = update_data.sale_price if update_data.sale_price is not None else template.sale_price
        
        # 应用相同的验证逻辑
        if card_type in [CardType.TIMES_LIMITED, CardType.TIMES_UNLIMITED]:
            if not available_balance or available_balance <= 0:
                raise MemberCardTemplateBusinessException.invalid_times_card_configuration()
        
        if card_type in [CardType.VALUE_LIMITED, CardType.VALUE_UNLIMITED]:
            if available_balance and available_balance > sale_price:
                raise MemberCardTemplateBusinessException.invalid_value_card_configuration()
        
        if card_type in [CardType.TIMES_LIMITED, CardType.VALUE_LIMITED]:
            if not validity_days or validity_days <= 0:
                raise MemberCardTemplateBusinessException.limited_card_requires_validity()


def get_member_card_template_service(session: Session, tenant_id: int) -> MemberCardTemplateService:
    """获取会员卡模板服务实例"""
    return MemberCardTemplateService(session, tenant_id)
