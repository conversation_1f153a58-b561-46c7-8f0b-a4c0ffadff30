"""
会员卡充值服务

提供会员卡充值相关的业务逻辑
"""

from typing import List, Optional
from sqlmodel import Session, select, and_, func
from datetime import datetime, timezone
import uuid

from app.features.base.base_service import TenantAwareService

from .models import (
    MemberCard, MemberCardOperation, CardStatus, PaymentMethod, 
    MemberCardOperationType
)
from .schemas import (
    RechargeRequest,
    RechargeResponse,
    BatchRechargeRequest,
    BatchRechargeResponse
)
from .exceptions import (
    MemberCardNotFoundError,
    MemberCardBusinessException,
    RechargeBusinessException
)


class RechargeService(TenantAwareService[MemberCardOperation]):
    """充值服务"""
    
    @property
    def model_class(self):
        return MemberCardOperation
    
    def __init__(self, session: Session, tenant_id: int):
        super().__init__(session, tenant_id)
        # 充值限制配置
        self.min_recharge_amount = 1  # 最小充值金额（元）
        self.max_recharge_amount = 50000  # 最大充值金额（元）
        self.max_bonus_rate = 0.5  # 最大赠送比例（50%）
    
    def recharge(self, recharge_data: RechargeRequest, operator_id: Optional[int] = None) -> RechargeResponse:
        """执行充值操作"""
        
        # 验证会员卡
        card = self.session.get(MemberCard, recharge_data.member_card_id)
        if not card:
            raise MemberCardNotFoundError(recharge_data.member_card_id)
        
        # 验证卡片状态
        self._validate_card_for_recharge(card)
        
        # 验证充值金额
        self._validate_recharge_amount(recharge_data.amount, recharge_data.bonus_amount)
        
        # 验证支付方式
        self._validate_payment_method(recharge_data.payment_method)
        
        # 记录充值前余额
        balance_before = card.balance
        
        # 计算充值后余额
        total_amount = recharge_data.amount + recharge_data.bonus_amount
        balance_after = balance_before + total_amount
        
        # 生成交易ID
        transaction_id = self._generate_transaction_id()
        
        try:
            # 开始事务
            # 更新会员卡余额和统计信息
            card.balance = balance_after
            card.total_recharged += total_amount
            card.last_used_at = datetime.now()
            
            # 设置审计字段
            card.updated_at = datetime.now()
            
            self.session.add(card)
            
            # 创建操作记录
            operation_data = {
                'tenant_id': self.tenant_id,
                'member_id': card.member_id,
                'member_card_id': card.id,
                'operation_type': MemberCardOperationType.RECHARGE,
                'operation_description': f"充值 {recharge_data.amount}元" + 
                                       (f"，赠送 {recharge_data.bonus_amount}元" if recharge_data.bonus_amount > 0 else ""),
                'amount_change': total_amount,
                'balance_before': balance_before,
                'balance_after': balance_after,
                'payment_method': recharge_data.payment_method,
                'payment_amount': recharge_data.amount,
                'bonus_amount': recharge_data.bonus_amount,
                'transaction_id': transaction_id,
                'notes': recharge_data.notes,
                'operator_id': operator_id,
                'status': 'completed'
            }
            
            operation = self.create(operation_data, operator_id, auto_commit=False)
            
            # 提交事务
            self.session.commit()
            self.session.refresh(card)
            self.session.refresh(operation)
            
            # 返回充值响应
            return RechargeResponse(
                operation_id=operation.id,
                member_card_id=card.id,
                amount=recharge_data.amount,
                bonus_amount=recharge_data.bonus_amount,
                total_amount=total_amount,
                balance_before=balance_before,
                balance_after=balance_after,
                payment_method=recharge_data.payment_method,
                transaction_id=transaction_id,
                created_at=operation.created_at
            )
            
        except Exception as e:
            self.session.rollback()
            raise RechargeBusinessException.recharge_failed(str(e))
    
    def batch_recharge(self, batch_request: BatchRechargeRequest, operator_id: Optional[int] = None) -> BatchRechargeResponse:
        """批量充值"""
        
        success_items = []
        failed_items = []
        
        for recharge_item in batch_request.recharge_items:
            try:
                result = self.recharge(recharge_item, operator_id)
                success_items.append(result)
            except Exception as e:
                failed_items.append({
                    "member_card_id": recharge_item.member_card_id,
                    "amount": recharge_item.amount,
                    "error": str(e)
                })
        
        return BatchRechargeResponse(
            success_count=len(success_items),
            failed_count=len(failed_items),
            success_items=success_items,
            failed_items=failed_items
        )
    
    def get_recharge_history(self, member_card_id: int, limit: int = 50) -> List[MemberCardOperation]:
        """获取充值历史记录"""
        
        statement = select(MemberCardOperation).where(
            and_(
                MemberCardOperation.member_card_id == member_card_id,
                MemberCardOperation.operation_type == MemberCardOperationType.RECHARGE
            )
        ).order_by(MemberCardOperation.created_at.desc()).limit(limit)
        
        return self.session.exec(statement).all()
    
    def get_member_recharge_statistics(self, member_id: int) -> dict:
        """获取会员充值统计"""
        
        # 总充值次数和金额
        recharge_stats = self.session.exec(
            select(
                func.count(MemberCardOperation.id).label('total_count'),
                func.sum(MemberCardOperation.payment_amount).label('total_amount'),
                func.sum(MemberCardOperation.bonus_amount).label('total_bonus')
            ).where(
                and_(
                    MemberCardOperation.member_id == member_id,
                    MemberCardOperation.operation_type == MemberCardOperationType.RECHARGE,
                    MemberCardOperation.status == 'completed'
                )
            )
        ).first()
        
        # 按支付方式统计
        payment_stats = {}
        for payment_method in PaymentMethod:
            stats = self.session.exec(
                select(
                    func.count(MemberCardOperation.id).label('count'),
                    func.sum(MemberCardOperation.payment_amount).label('amount')
                ).where(
                    and_(
                        MemberCardOperation.member_id == member_id,
                        MemberCardOperation.operation_type == MemberCardOperationType.RECHARGE,
                        MemberCardOperation.payment_method == payment_method,
                        MemberCardOperation.status == 'completed'
                    )
                )
            ).first()
            
            payment_stats[payment_method.value] = {
                'count': stats.count or 0,
                'amount': stats.amount or 0
            }
        
        return {
            'total_recharge_count': recharge_stats.total_count or 0,
            'total_recharge_amount': recharge_stats.total_amount or 0,
            'total_bonus_amount': recharge_stats.total_bonus or 0,
            'payment_method_stats': payment_stats
        }
    
    def get_daily_recharge_statistics(self, date_from: datetime, date_to: datetime) -> dict:
        """获取指定时间段的每日充值统计"""
        
        statement = select(
            func.date(MemberCardOperation.created_at).label('date'),
            func.count(MemberCardOperation.id).label('count'),
            func.sum(MemberCardOperation.payment_amount).label('amount'),
            func.sum(MemberCardOperation.bonus_amount).label('bonus')
        ).where(
            and_(
                MemberCardOperation.operation_type == MemberCardOperationType.RECHARGE,
                MemberCardOperation.status == 'completed',
                MemberCardOperation.created_at >= date_from,
                MemberCardOperation.created_at <= date_to
            )
        ).group_by(func.date(MemberCardOperation.created_at)).order_by('date')
        
        results = self.session.exec(statement).all()
        
        return {
            'daily_stats': [
                {
                    'date': str(result.date),
                    'count': result.count,
                    'amount': result.amount or 0,
                    'bonus': result.bonus or 0
                }
                for result in results
            ]
        }
    
    # 私有验证方法
    def _validate_card_for_recharge(self, card: MemberCard):
        """验证会员卡是否可以充值"""
        
        if card.status == CardStatus.CANCELLED:
            raise MemberCardBusinessException.card_cancelled(card.id, card.cancel_reason)
        
        if card.status == CardStatus.FROZEN:
            raise MemberCardBusinessException.card_frozen(card.id, card.freeze_reason)
        
        # 检查过期时间（过期卡片不能充值）
        if card.expires_at and card.expires_at <= datetime.now():
            raise MemberCardBusinessException.card_expired(card.id, card.expires_at.isoformat())
    
    def _validate_recharge_amount(self, amount: int, bonus_amount: int):
        """验证充值金额"""
        
        if amount < self.min_recharge_amount:
            raise RechargeBusinessException.amount_too_small(amount, self.min_recharge_amount)
        
        if amount > self.max_recharge_amount:
            raise RechargeBusinessException.amount_too_large(amount, self.max_recharge_amount)
        
        if bonus_amount < 0:
            raise MemberCardBusinessException.invalid_amount(bonus_amount, "赠送金额不能为负数")
        
        # 验证赠送比例
        if bonus_amount > amount * self.max_bonus_rate:
            raise MemberCardBusinessException.invalid_amount(
                bonus_amount, 
                f"赠送金额不能超过充值金额的{int(self.max_bonus_rate * 100)}%"
            )
    
    def _validate_payment_method(self, payment_method: PaymentMethod):
        """验证支付方式"""
        
        # 这里可以添加支付方式的业务验证
        # 例如：某些支付方式在特定时间不可用等
        valid_methods = [PaymentMethod.WECHAT, PaymentMethod.ALIPAY, PaymentMethod.MANUAL, PaymentMethod.BANK_TRANSFER]
        
        if payment_method not in valid_methods:
            raise RechargeBusinessException.invalid_payment_method(payment_method.value)
    
    def _generate_transaction_id(self) -> str:
        """生成交易ID"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_suffix = str(uuid.uuid4())[:8].upper()
        return f"RC{timestamp}{random_suffix}"


def get_recharge_service(session: Session, tenant_id: int) -> RechargeService:
    """获取充值服务实例"""
    return RechargeService(session, tenant_id)
