from sqlmodel import SQLModel, Field, UniqueConstraint, Index
from typing import Optional
from datetime import datetime, timezone
from enum import Enum


class CardType(str, Enum):
    """卡片类型枚举"""
    TIMES_LIMITED = "times_limited"      # 次卡-有期限
    TIMES_UNLIMITED = "times_unlimited"  # 次卡-无期限
    VALUE_LIMITED = "value_limited"      # 储值卡-有期限
    VALUE_UNLIMITED = "value_unlimited"  # 储值卡-无期限


class CardStatus(str, Enum):
    """卡片状态枚举"""
    ACTIVE = "active"        # 激活
    FROZEN = "frozen"        # 冻结
    EXPIRED = "expired"      # 过期
    CANCELLED = "cancelled"  # 注销


class PaymentMethod(str, Enum):
    """支付方式枚举"""
    WECHAT = "wechat"        # 微信支付
    ALIPAY = "alipay"        # 支付宝
    MANUAL = "manual"        # 手动充值
    BANK_TRANSFER = "bank_transfer"  # 银行转账


class MemberCardOperationType(str, Enum):
    """会员卡操作类型枚举 - 统一记录所有会员卡相关操作"""

    # 卡片管理操作
    CREATE_CARD = "create_card"                    # 创建会员卡
    UPDATE_CARD_INFO = "update_card_info"          # 更新卡片信息
    FREEZE_CARD = "freeze_card"                    # 冻结卡片
    UNFREEZE_CARD = "unfreeze_card"                # 解冻卡片
    CANCEL_CARD = "cancel_card"                    # 注销卡片

    # 余额变动操作
    RECHARGE = "recharge"                          # 充值
    INITIAL_BINDING = "initial_binding"            # 首次绑卡
    DIRECT_BOOKING = "direct_booking"              # 直接约课
    FIXED_SCHEDULE_BOOKING = "fixed_schedule_booking"  # 固定课生成排课
    ADMIN_BOOKING = "admin_booking"                # 管理员约课
    MANUAL_DEDUCTION = "manual_deduction"          # 人工扣费
    MEMBER_CANCEL_BOOKING = "member_cancel_booking"    # 会员取消约课
    ADMIN_CANCEL_BOOKING = "admin_cancel_booking"      # 管理员取消约课
    REFUND = "refund"                              # 退款

    # 其他操作
    OTHER = "other"                                # 其他


# ==================== 会员卡模板模型 ====================

class MemberCardTemplateBase(SQLModel):
    """会员卡模板基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")

    # 基础信息
    name: str = Field(max_length=100, description="卡片模板名称")
    card_type: CardType = Field(description="卡片类型")
    sale_price: int = Field(ge=0, description="售卖价格（元）")

    # 扩展字段（完整版本）
    available_balance: Optional[int] = Field(
        default=None,
        ge=0,
        description="可用余额（元或次数）"
    )
    validity_days: Optional[int] = Field(default=None, description="有效期(天)")
    is_agent_exclusive: bool = Field(default=False, description="是否代理专售")
    allow_repeat_purchase: bool = Field(default=True, description="是否允许重复购买")
    allow_renewal: bool = Field(default=True, description="是否支持线上续费")
    description: Optional[str] = Field(default=None, description="模板描述")

    # 状态字段
    is_active: bool = Field(default=True, description="是否启用")


class MemberCardTemplate(MemberCardTemplateBase, table=True):
    """会员卡模板数据库模型"""
    __tablename__ = "member_card_templates"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    updated_at: Optional[datetime] = Field(default=None)
    created_by: Optional[int] = Field(default=None, foreign_key="users.id", description="创建者")
    
    __table_args__ = (
        # 租户内模板名称唯一
        UniqueConstraint('tenant_id', 'name', name='uq_tenant_template_name'),
        # 索引
        Index('idx_template_tenant', 'tenant_id', 'is_active'),
        Index('idx_template_type', 'tenant_id', 'card_type'),
        Index('idx_template_created_by', 'tenant_id', 'created_by'),
    )


# ==================== 会员卡实例模型 ====================

class MemberCardBase(SQLModel):
    """会员卡基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    member_id: int = Field(foreign_key="members.id", description="会员ID")
    template_id: Optional[int] = Field(
        default=None,
        foreign_key="member_card_templates.id",
        description="模板ID"
    )

    # 卡片信息
    card_type: CardType = Field(description="卡片类型")
    balance: int = Field(
        default=0,
        ge=0,
        description="当前余额（元或次数）"
    )
    status: CardStatus = Field(default=CardStatus.ACTIVE, description="卡片状态")

    # 扩展字段（完整版本）
    card_number: Optional[str] = Field(default=None, max_length=50, description="卡号")
    total_recharged: int = Field(
        default=0,
        ge=0,
        description="总充值金额（元）"
    )
    total_consumed: int = Field(
        default=0,
        ge=0,
        description="总消费金额（元）"
    )
    expires_at: Optional[datetime] = Field(default=None, description="过期时间")

    # 业务扩展字段
    last_used_at: Optional[datetime] = Field(default=None, description="最后使用时间")
    freeze_reason: Optional[str] = Field(default=None, max_length=200, description="冻结原因")
    cancel_reason: Optional[str] = Field(default=None, max_length=200, description="注销原因")


class MemberCard(MemberCardBase, table=True):
    """会员卡数据库模型"""
    __tablename__ = "member_cards"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # 业务扩展字段
    last_used_at: Optional[datetime] = Field(default=None, description="最后使用时间")
    freeze_reason: Optional[str] = Field(default=None, description="冻结原因")
    cancel_reason: Optional[str] = Field(default=None, description="注销原因")

    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    updated_at: Optional[datetime] = Field(default=None)
    created_by: Optional[int] = Field(default=None, foreign_key="users.id", description="创建者")
    
    __table_args__ = (
        # 卡号全局唯一（如果有卡号）
        UniqueConstraint('card_number', name='uq_card_number'),
        # 索引
        Index('idx_card_tenant_member', 'tenant_id', 'member_id'),
        Index('idx_card_status', 'tenant_id', 'status'),
        Index('idx_card_template', 'template_id'),
        Index('idx_card_created_by', 'tenant_id', 'created_by'),
    )



# ==================== 会员卡操作记录模型（统一记录所有操作）====================

class MemberCardOperationBase(SQLModel):
    """会员卡操作记录基础模型 - 统一记录所有会员卡相关操作"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    member_id: int = Field(foreign_key="members.id", description="会员ID")
    member_card_id: int = Field(foreign_key="member_cards.id", description="会员卡ID")

    # 操作基础信息
    operation_type: MemberCardOperationType = Field(description="操作类型")
    operation_description: str = Field(max_length=200, description="操作描述")

    # 余额变动信息（仅余额变动操作需要）
    amount_change: Optional[int] = Field(default=None, description="余额变化金额（元，正数表示增加，负数表示减少）")
    balance_before: Optional[int] = Field(default=None, ge=0, description="操作前余额（元）")
    balance_after: Optional[int] = Field(default=None, ge=0, description="操作后余额（元）")

    # 状态变更信息（仅状态变更操作需要）
    status_before: Optional[CardStatus] = Field(default=None, description="操作前状态")
    status_after: Optional[CardStatus] = Field(default=None, description="操作后状态")

    # 会员卡信息（冗余字段，便于显示和查询）
    member_card_name: Optional[str] = Field(default=None, max_length=100, description="会员卡名称")

    # 充值相关字段（仅充值操作需要）
    bonus_amount: Optional[int] = Field(default=None, ge=0, description="赠送金额（元）")
    actual_amount: Optional[int] = Field(default=None, ge=0, description="实收金额（元）")
    payment_method: Optional[PaymentMethod] = Field(default=None, description="支付方式")
    payment_status: Optional[str] = Field(default=None, max_length=20, description="支付状态")
    transaction_id: Optional[str] = Field(default=None, max_length=100, description="第三方交易ID")

    # 课程相关字段（仅约课操作需要）
    scheduled_class_id: Optional[int] = Field(
        default=None,
        foreign_key="scheduled_classes.id",
        description="关联课程ID"
    )

    # 操作人信息
    operator_id: Optional[int] = Field(default=None, foreign_key="users.id", description="操作人ID（为空表示会员自己操作）")
    operator_name: Optional[str] = Field(default=None, max_length=50, description="操作人姓名")
    operator_type: Optional[str] = Field(default=None, max_length=20, description="操作人类型（member/admin/system）")

    # 操作原因和备注
    reason: Optional[str] = Field(default=None, description="操作原因")
    notes: Optional[str] = Field(default=None, description="备注信息")

    # 客户端信息（可选，用于审计）
    client_ip: Optional[str] = Field(default=None, max_length=45, description="客户端IP")
    user_agent: Optional[str] = Field(default=None, max_length=500, description="用户代理")

    # 操作状态
    status: Optional[str] = Field(default="completed", max_length=20, description="操作状态")


class MemberCardOperation(MemberCardOperationBase, table=True):
    """会员卡操作记录数据库模型"""
    __tablename__ = "member_card_operations"

    id: Optional[int] = Field(default=None, primary_key=True)

    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    updated_at: Optional[datetime] = Field(default=None)

    __table_args__ = (
        # 基础索引
        Index('idx_operation_tenant_member', 'tenant_id', 'member_id'),
        Index('idx_operation_card', 'member_card_id'),
        Index('idx_operation_type', 'tenant_id', 'operation_type'),
        Index('idx_operation_operator', 'operator_id'),
        Index('idx_operation_date', 'tenant_id', 'created_at'),
        Index('idx_operation_class', 'scheduled_class_id'),
        Index('idx_operation_payment', 'transaction_id'),

        # 业务查询索引
        # 余额变动操作查询（充值、扣费、退款等）
        Index('idx_operation_balance_change', 'tenant_id', 'operation_type', 'created_at'),
        # 状态变更操作查询（冻结、解冻、注销等）
        Index('idx_operation_status_change', 'tenant_id', 'operation_type', 'status_before', 'status_after'),
        # 会员卡流水查询
        Index('idx_operation_card_flow', 'member_card_id', 'created_at'),
        # 操作人查询
        Index('idx_operation_operator_date', 'operator_id', 'created_at'),
        # 约课相关操作查询
        Index('idx_operation_booking', 'tenant_id', 'operation_type', 'scheduled_class_id'),
        # 充值相关操作查询
        Index('idx_operation_recharge', 'tenant_id', 'operation_type', 'payment_method'),
    )
