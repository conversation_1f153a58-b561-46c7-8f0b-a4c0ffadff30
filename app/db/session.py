from sqlmodel import SQLModel, create_engine, Session
from sqlalchemy import text, select, func, event
from app.core.config import settings
from typing import Generator
import logging
import os

logger = logging.getLogger(__name__)

# 确定是否启用 SQL echo
# 在测试环境中禁用 echo，使用日志系统控制
def should_echo_sql():
    """确定是否应该启用 SQL echo"""
    # 如果是测试环境，不启用 echo，让日志系统控制
    if os.getenv("TESTING") == "true":
        return False
    # 如果明确设置了 SQLALCHEMY_ECHO 环境变量，使用该设置
    if os.getenv("SQLALCHEMY_ECHO"):
        return os.getenv("SQLALCHEMY_ECHO").lower() in ("true", "1", "yes")
    # 否则使用 DEBUG 设置
    return settings.DEBUG

# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    echo=should_echo_sql(),
    pool_pre_ping=True,
    pool_size=20,
    max_overflow=30,
    pool_recycle=3600,
    connect_args={"options": "-c timezone=Asia/Shanghai"}  # 使用东八区
)


def get_session() -> Generator[Session, None, None]:
    """获取普通数据库会话"""
    with Session(engine) as session:
        yield session


def get_tenant_session(tenant_id: int) -> Generator[Session, None, None]:
    """获取租户数据库会话，并设置RLS上下文"""
    with Session(engine) as session:
        # 设置当前租户ID用于RLS
        session.execute(text(f"SET app.current_tenant_id = '{tenant_id}'"))
        yield session


def get_global_session() -> Generator[Session, None, None]:
    """获取全局Schema会话（用于租户管理等全局操作）"""
    with Session(engine) as session:
        # 清除租户上下文，允许访问所有数据
        try:
            session.execute(text("RESET app.current_tenant_id"))
        except:
            pass  # 忽略RESET错误
        yield session
