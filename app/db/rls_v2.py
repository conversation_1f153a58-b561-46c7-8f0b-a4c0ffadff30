"""
新版本的Row Level Security (RLS) 策略实现

修复问题：
1. 租户用户不应该看到超级管理员
2. 提供两种超级管理员标识方案：NULL 和 tenant_id=0
3. 更清晰的策略逻辑和更好的可维护性
"""
import logging
from sqlmodel import Session, text
from typing import Literal

logger = logging.getLogger(__name__)


class RLSPolicyManager:
    """RLS策略管理器"""
    
    def __init__(self, engine):
        self.engine = engine
    
    def setup_rls_policies_v2(self, super_admin_mode: Literal["null", "zero"] = "zero"):
        """
        设置新版本的RLS策略
        
        Args:
            super_admin_mode: 超级管理员标识方式
                - "null": tenant_id IS NULL (当前方式)
                - "zero": tenant_id = 0 (推荐方式)
        """
        logger.debug(f"Setting up RLS policies v2 with super_admin_mode={super_admin_mode}")
        
        # 为需要租户隔离的表启用RLS
        tables_with_rls = [
            ('users', 'tenant_id'),           # 用户表
            ('user_sessions', None),          # 用户会话表（通过users表关联）
            ('members', 'tenant_id'),         # 会员表
            ('tag_categories', 'tenant_id'),  # 标签分类表
            ('tags', 'tenant_id'),            # 标签表
            ('teachers', 'tenant_id'),        # 教师表
            ('teacher_tags', None),           # 教师标签关联表（通过教师表关联）
            ('teacher_fixed_slots', 'tenant_id'),  # 教师固定时间占位表
            ('course_system_configs', 'tenant_id'),  # 课程系统配置表
            ('scheduled_classes', 'tenant_id'),  # 已排课表
            ('member_fixed_slot_locks', 'tenant_id'),  # 会员固定课位锁定表
            ('member_card_templates', 'tenant_id'),  # 会员卡模板表
            ('member_cards', 'tenant_id'),    # 会员卡表
            ('recharge_records', 'tenant_id'),  # 充值记录表
            ('consumption_records', 'tenant_id'),  # 消费记录表
        ]

        for table_name, tenant_column in tables_with_rls:
            self._setup_table_rls_v2(table_name, tenant_column, super_admin_mode)
        
        logger.debug("RLS policies v2 setup completed")
    
    def _setup_table_rls_v2(self, table_name: str, tenant_column: str, super_admin_mode: str):
        """为单个表设置RLS策略"""
        with Session(self.engine) as session:
            try:
                # 删除旧策略（如果存在）
                session.exec(text(f"DROP POLICY IF EXISTS {table_name}_tenant_isolation ON {table_name}"))
                session.exec(text(f"DROP POLICY IF EXISTS {table_name}_tenant_isolation_v2 ON {table_name}"))

                # 启用RLS（强制模式，即使对表所有者也生效）
                session.exec(text(f"ALTER TABLE {table_name} ENABLE ROW LEVEL SECURITY"))
                session.exec(text(f"ALTER TABLE {table_name} FORCE ROW LEVEL SECURITY"))

                if tenant_column:
                    if table_name == 'users':
                        # 用户表特殊处理
                        policy_sql = self._get_users_table_policy_v2(table_name, tenant_column, super_admin_mode)
                    else:
                        # 普通表：只能访问当前租户的数据
                        policy_sql = self._get_regular_table_policy_v2(table_name, tenant_column)
                else:
                    # 通过关联表进行过滤
                    policy_sql = self._get_association_table_policy_v2(table_name, super_admin_mode)
                
                session.exec(text(policy_sql))
                session.commit()
                logger.debug(f"RLS policy v2 created for table: {table_name}")

            except Exception as e:
                logger.warning(f"RLS setup failed for table {table_name}: {e}")
                session.rollback()
    
    def _get_users_table_policy_v2(self, table_name: str, tenant_column: str, super_admin_mode: str) -> str:
        """获取用户表的RLS策略SQL"""
        if super_admin_mode == "zero":
            # 方案2：使用 tenant_id=0 标识超级管理员
            return f"""
                CREATE POLICY {table_name}_tenant_isolation_v2 ON {table_name}
                FOR ALL
                TO PUBLIC
                USING (
                    CASE
                        -- 全局模式：可以看到所有数据
                        WHEN current_setting('app.current_tenant_id', true) IS NULL OR
                             current_setting('app.current_tenant_id', true) = '' THEN true
                        -- 租户模式：只能看到本租户数据，不能看到超级管理员(tenant_id=0)
                        ELSE {tenant_column} = current_setting('app.current_tenant_id', true)::int AND {tenant_column} > 0
                    END
                )
                WITH CHECK (
                    CASE
                        -- 全局模式：可以插入/更新任何数据
                        WHEN current_setting('app.current_tenant_id', true) IS NULL OR
                             current_setting('app.current_tenant_id', true) = '' THEN true
                        -- 租户模式：只能插入/更新本租户数据，不能操作超级管理员
                        ELSE {tenant_column} = current_setting('app.current_tenant_id', true)::int AND {tenant_column} > 0
                    END
                )
            """
        else:
            # 方案1：使用 tenant_id IS NULL 标识超级管理员（修复版）
            return f"""
                CREATE POLICY {table_name}_tenant_isolation_v2 ON {table_name}
                FOR ALL
                TO PUBLIC
                USING (
                    CASE
                        -- 全局模式：可以看到所有数据
                        WHEN current_setting('app.current_tenant_id', true) IS NULL OR
                             current_setting('app.current_tenant_id', true) = '' THEN true
                        -- 租户模式：只能看到本租户数据，不能看到超级管理员(tenant_id IS NULL)
                        ELSE {tenant_column} = current_setting('app.current_tenant_id', true)::int
                    END
                )
                WITH CHECK (
                    CASE
                        -- 全局模式：可以插入/更新任何数据
                        WHEN current_setting('app.current_tenant_id', true) IS NULL OR
                             current_setting('app.current_tenant_id', true) = '' THEN true
                        -- 租户模式：只能插入/更新本租户数据，不能操作超级管理员
                        ELSE {tenant_column} = current_setting('app.current_tenant_id', true)::int
                    END
                )
            """
    
    def _get_regular_table_policy_v2(self, table_name: str, tenant_column: str) -> str:
        """获取普通表的RLS策略SQL"""
        return f"""
            CREATE POLICY {table_name}_tenant_isolation_v2 ON {table_name}
            FOR ALL
            TO PUBLIC
            USING (
                CASE
                    -- 全局模式：可以看到所有数据
                    WHEN current_setting('app.current_tenant_id', true) IS NULL OR
                         current_setting('app.current_tenant_id', true) = '' THEN true
                    -- 租户模式：只能看到本租户数据
                    ELSE {tenant_column} = current_setting('app.current_tenant_id', true)::int
                END
            )
            WITH CHECK (
                CASE
                    -- 全局模式：可以插入/更新任何数据
                    WHEN current_setting('app.current_tenant_id', true) IS NULL OR
                         current_setting('app.current_tenant_id', true) = '' THEN true
                    -- 租户模式：只能插入/更新本租户数据
                    ELSE {tenant_column} = current_setting('app.current_tenant_id', true)::int
                END
            )
        """
    
    def _get_association_table_policy_v2(self, table_name: str, super_admin_mode: str) -> str:
        """获取关联表的RLS策略SQL"""
        if table_name == 'user_sessions':
            if super_admin_mode == "zero":
                return f"""
                    CREATE POLICY {table_name}_tenant_isolation_v2 ON {table_name}
                    FOR ALL
                    TO PUBLIC
                    USING (
                        user_id IN (
                            SELECT id FROM users
                            WHERE CASE
                                WHEN current_setting('app.current_tenant_id', true) IS NULL OR
                                     current_setting('app.current_tenant_id', true) = '' THEN true
                                ELSE tenant_id = current_setting('app.current_tenant_id', true)::int AND tenant_id > 0
                            END
                        )
                    )
                    WITH CHECK (
                        user_id IN (
                            SELECT id FROM users
                            WHERE CASE
                                WHEN current_setting('app.current_tenant_id', true) IS NULL OR
                                     current_setting('app.current_tenant_id', true) = '' THEN true
                                ELSE tenant_id = current_setting('app.current_tenant_id', true)::int AND tenant_id > 0
                            END
                        )
                    )
                """
            else:
                return f"""
                    CREATE POLICY {table_name}_tenant_isolation_v2 ON {table_name}
                    FOR ALL
                    TO PUBLIC
                    USING (
                        user_id IN (
                            SELECT id FROM users
                            WHERE CASE
                                WHEN current_setting('app.current_tenant_id', true) IS NULL OR
                                     current_setting('app.current_tenant_id', true) = '' THEN true
                                ELSE tenant_id = current_setting('app.current_tenant_id', true)::int
                            END
                        )
                    )
                    WITH CHECK (
                        user_id IN (
                            SELECT id FROM users
                            WHERE CASE
                                WHEN current_setting('app.current_tenant_id', true) IS NULL OR
                                     current_setting('app.current_tenant_id', true) = '' THEN true
                                ELSE tenant_id = current_setting('app.current_tenant_id', true)::int
                            END
                        )
                    )
                """
        elif table_name == 'teacher_tags':
            return f"""
                CREATE POLICY {table_name}_tenant_isolation_v2 ON {table_name}
                FOR ALL
                TO PUBLIC
                USING (
                    teacher_id IN (
                        SELECT id FROM teachers
                        WHERE CASE
                            WHEN current_setting('app.current_tenant_id', true) IS NULL OR
                                 current_setting('app.current_tenant_id', true) = '' THEN true
                            ELSE tenant_id = current_setting('app.current_tenant_id', true)::int
                        END
                    )
                )
                WITH CHECK (
                    teacher_id IN (
                        SELECT id FROM teachers
                        WHERE CASE
                            WHEN current_setting('app.current_tenant_id', true) IS NULL OR
                                 current_setting('app.current_tenant_id', true) = '' THEN true
                            ELSE tenant_id = current_setting('app.current_tenant_id', true)::int
                        END
                    )
                )
            """
        else:
            raise ValueError(f"Unknown association table: {table_name}")
    
    def cleanup_old_policies(self):
        """清理旧的RLS策略"""
        tables_with_rls = [
            'users', 'user_sessions', 'members', 'tag_categories', 'tags',
            'teachers', 'teacher_tags', 'teacher_fixed_slots', 'course_system_configs',
            'scheduled_classes', 'member_fixed_slot_locks', 'member_card_templates',
            'member_cards', 'recharge_records', 'consumption_records'
        ]
        
        with Session(self.engine) as session:
            for table_name in tables_with_rls:
                try:
                    # 删除旧策略
                    session.exec(text(f"DROP POLICY IF EXISTS {table_name}_tenant_isolation ON {table_name}"))
                    logger.debug(f"Dropped old policy for table: {table_name}")
                except Exception as e:
                    logger.warning(f"Failed to drop old policy for table {table_name}: {e}")
            
            session.commit()
        
        logger.debug("Old RLS policies cleanup completed")


def setup_rls_policies_v2(super_admin_mode: Literal["null", "zero"] = "zero"):
    """
    设置新版本的RLS策略
    
    Args:
        super_admin_mode: 超级管理员标识方式
            - "null": tenant_id IS NULL (当前方式，修复版)
            - "zero": tenant_id = 0 (推荐方式)
    """
    from app.db.session import engine
    
    manager = RLSPolicyManager(engine)
    
    # 清理旧策略
    manager.cleanup_old_policies()
    
    # 设置新策略
    manager.setup_rls_policies_v2(super_admin_mode)
    
    logger.debug(f"RLS policies v2 setup completed with super_admin_mode={super_admin_mode}")
