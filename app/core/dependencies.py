from typing import Optional, Union
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlmodel import Session, select, text
from app.db.session import get_session, get_tenant_session, get_global_session
from app.features.users.models import User, UserRole
from app.features.members.models import Member
from app.features.tenants.models import Tenant
from app.core.security import verify_token
from .context import TenantContext, UserContext, MemberContext
from app.api.common.exceptions import PermissionError, AuthenticationError, NotFoundError


# HTTP Bearer token 认证
security = HTTPBearer(auto_error=False)


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    session: Session = Depends(get_session)
) -> User:
    """获取当前CMS用户（管理员）"""
    
    # 检查是否提供了认证信息
    if not credentials:
        raise AuthenticationError("未提供认证凭据")
    
    # 验证token并获取用户信息
    token_data = verify_token(credentials.credentials)
    if not token_data or not token_data.get('sub'):
        raise AuthenticationError("无效的认证凭据")
    
    # 从token中获取信息
    user_id = token_data.get('sub')
    tenant_id = token_data.get('tenant_id')  # 可能为None（超级管理员）
    user_type = token_data.get('user_type')
    
    # 验证用户类型
    if user_type != 'admin':
        raise AuthenticationError("用户类型不匹配")
    
    # 设置租户上下文（如果有）
    if tenant_id:
        session.execute(text(f"SET app.current_tenant_id = '{tenant_id}'"))
    else:
        # 超级管理员，清除租户上下文
        try:
            session.execute(text("RESET app.current_tenant_id"))
        except:
            pass
    
    # 查找用户（RLS会自动过滤）
    user = session.get(User, user_id)
    
    if user is None:
        raise AuthenticationError("用户不存在")
    
    if user.status != "active":
        raise AuthenticationError("用户账户已被禁用")
    
    return user


async def get_current_member(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    session: Session = Depends(get_session)
) -> Member:
    """获取当前会员"""
    
    # 检查是否提供了认证信息
    if not credentials:
        raise AuthenticationError("未提供认证凭据")
    
    # 验证token并获取会员信息
    token_data = verify_token(credentials.credentials)
    if not token_data:
        raise AuthenticationError("无效的认证凭据")
    
    # 从token中获取信息
    member_id = token_data.get('sub')
    tenant_id = token_data.get('tenant_id')
    user_type = token_data.get('user_type')
    
    # 验证用户类型和必要字段
    if user_type != 'member':
        raise AuthenticationError("用户类型不匹配")
    
    if not member_id:
        raise AuthenticationError("会员ID缺失")
    
    if not tenant_id:
        raise AuthenticationError("租户ID缺失")
    
    # 设置租户上下文
    session.execute(text(f"SET app.current_tenant_id = '{tenant_id}'"))
    
    # 查找会员（RLS会自动过滤）
    member = session.get(Member, member_id)
    
    if member is None:
        raise AuthenticationError("会员不存在")
    
    if member.member_status != "active":
        raise AuthenticationError("会员账户已被禁用")
    
    return member


async def get_user_context(
    user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
) -> UserContext:
    """获取用户上下文"""
    tenant_context = None
    
    if user.tenant_id:  # 非超级管理员
        tenant = session.get(Tenant, user.tenant_id)
        if not tenant:
            raise NotFoundError("租户")
        tenant_context = TenantContext(tenant_id=user.tenant_id, tenant=tenant)
    
    return UserContext(user=user, tenant_context=tenant_context)


async def get_member_context(
    member: Member = Depends(get_current_member),
    session: Session = Depends(get_session)
) -> MemberContext:
    """获取会员上下文"""
    tenant = session.get(Tenant, member.tenant_id)
    if not tenant:
        raise NotFoundError("租户")
    
    tenant_context = TenantContext(tenant_id=member.tenant_id, tenant=tenant)
    return MemberContext(member=member, tenant_context=tenant_context)


def require_roles(*allowed_roles: UserRole):
    """角色权限装饰器"""
    def role_checker(user_context: UserContext = Depends(get_user_context)):
        if user_context.user.role not in allowed_roles:
            raise PermissionError("角色权限不足")
        return user_context
    return role_checker


def require_super_admin(user_context: UserContext = Depends(get_user_context)):
    """要求超级管理员权限"""
    if not user_context.is_super_admin:
        raise PermissionError("需要超级管理员权限")
    return user_context


# 便捷的依赖注入函数
async def get_tenant_session_dep(
    user_context: UserContext = Depends(get_user_context)
) -> Session:
    """获取租户数据库会话依赖（用户版）"""
    if not user_context.tenant_context:
        raise PermissionError("需要租户上下文")
    return get_tenant_session(user_context.tenant_context.tenant_id)


async def get_member_session_dep(
    member_context: MemberContext = Depends(get_member_context)
) -> Session:
    """获取租户数据库会话依赖（会员版）"""
    return get_tenant_session(member_context.tenant_context.tenant_id)
