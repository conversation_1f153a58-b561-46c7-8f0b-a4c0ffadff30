"""
统一日志系统
支持开发、测试、生产环境的不同日志配置
"""
import os
import sys
import logging
import logging.config
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

from app.core.config import settings


class LoggerConfig:
    """日志配置类"""
    
    @staticmethod
    def get_log_dir() -> Path:
        """获取日志目录"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        return log_dir
    
    @staticmethod
    def get_config(
        environment: str = "development",
        log_level: str = "INFO",
        enable_file_logging: bool = True,
        enable_console_logging: bool = True,
        filter_sqlalchemy: bool = True
    ) -> Dict[str, Any]:
        """获取日志配置"""
        
        log_dir = LoggerConfig.get_log_dir()
        
        # 基础格式器
        formatters = {
            "detailed": {
                "format": "%(asctime)s | %(name)s | %(levelname)s | %(filename)s:%(lineno)d | %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "simple": {
                "format": "%(asctime)s | %(levelname)s | %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "json": {
                "()": "app.core.logging.JSONFormatter"
            }
        }
        
        # 处理器
        handlers = {}
        
        # 控制台处理器
        if enable_console_logging:
            handlers["console"] = {
                "class": "logging.StreamHandler",
                "level": log_level,
                "formatter": "simple" if environment == "production" else "detailed",
                "stream": "ext://sys.stdout"
            }
        
        # 文件处理器
        if enable_file_logging:
            # 应用日志
            handlers["app_file"] = {
                "class": "logging.handlers.RotatingFileHandler",
                "level": log_level,
                "formatter": "detailed",
                "filename": str(log_dir / "app.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8"
            }
            
            # 错误日志
            handlers["error_file"] = {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filename": str(log_dir / "error.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8"
            }
            
            # 测试日志（仅测试环境）
            if environment == "testing":
                handlers["test_file"] = {
                    "class": "logging.handlers.RotatingFileHandler",
                    "level": "DEBUG",
                    "formatter": "detailed",
                    "filename": str(log_dir / "test.log"),
                    "maxBytes": 5242880,  # 5MB
                    "backupCount": 3,
                    "encoding": "utf8"
                }
        
        # 过滤器
        filters = {}
        if filter_sqlalchemy:
            filters["sqlalchemy_filter"] = {
                "()": "app.core.logging.SQLAlchemyFilter"
            }
        
        # 记录器配置
        loggers = {
            "app": {
                "level": log_level,
                "handlers": list(handlers.keys()),
                "propagate": False
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console"] if enable_console_logging else [],
                "propagate": False
            },
            "fastapi": {
                "level": "INFO", 
                "handlers": ["console"] if enable_console_logging else [],
                "propagate": False
            }
        }
        
        # SQLAlchemy日志配置
        if filter_sqlalchemy:
            loggers["sqlalchemy.engine"] = {
                "level": "WARNING",  # 只记录警告和错误
                "handlers": ["console"] if enable_console_logging else [],
                "propagate": False,
                "filters": ["sqlalchemy_filter"] if "sqlalchemy_filter" in filters else []
            }
        else:
            loggers["sqlalchemy.engine"] = {
                "level": "INFO",
                "handlers": ["console"] if enable_console_logging else [],
                "propagate": False
            }
        
        return {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": formatters,
            "filters": filters,
            "handlers": handlers,
            "loggers": loggers,
            "root": {
                "level": log_level,
                "handlers": list(handlers.keys())
            }
        }


class SQLAlchemyFilter(logging.Filter):
    """SQLAlchemy日志过滤器"""
    
    def filter(self, record):
        """过滤SQLAlchemy日志"""
        # 过滤掉常见的数据库操作日志
        if hasattr(record, 'msg'):
            msg = str(record.msg)
            
            # 过滤的SQL语句类型
            filtered_patterns = [
                "SELECT pg_catalog",
                "DROP POLICY",
                "CREATE POLICY", 
                "ALTER TABLE",
                "BEGIN (implicit)",
                "COMMIT",
                "ROLLBACK",
                "show standard_conforming_strings",
                "select current_schema()",
                "select pg_catalog.version()"
            ]
            
            for pattern in filtered_patterns:
                if pattern in msg:
                    return False
        
        return True


class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record):
        """格式化为JSON"""
        import json
        
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'user_id'):
            log_entry["user_id"] = record.user_id
        if hasattr(record, 'tenant_id'):
            log_entry["tenant_id"] = record.tenant_id
        if hasattr(record, 'request_id'):
            log_entry["request_id"] = record.request_id
        
        return json.dumps(log_entry, ensure_ascii=False)


def is_logging_configured() -> bool:
    """检查日志系统是否已经配置"""
    # 检查是否有自定义的日志处理器
    root_logger = logging.getLogger()
    app_logger = logging.getLogger("app")

    # 如果有非默认的处理器，说明已经配置过了
    return (len(root_logger.handlers) > 0 and
            any(handler.__class__.__name__ != 'StreamHandler' or
                hasattr(handler, 'baseFilename') for handler in root_logger.handlers)) or \
           (len(app_logger.handlers) > 0)


def get_current_log_level() -> str:
    """获取当前日志级别"""
    app_logger = logging.getLogger("app")
    return logging.getLevelName(app_logger.level)


def setup_logging(
    environment: Optional[str] = None,
    log_level: Optional[str] = None,
    enable_file_logging: bool = True,
    filter_sqlalchemy: bool = True,
    force_reconfigure: bool = False
):
    """设置日志系统"""

    # 确定环境
    if environment is None:
        if os.getenv("TESTING") == "true":
            environment = "testing"
        elif settings.DEBUG:
            environment = "development"
        else:
            environment = "production"

    # 在测试环境中，如果日志已经配置且不强制重新配置，则跳过
    if environment == "testing" and not force_reconfigure and is_logging_configured():
        logger = logging.getLogger("app")
        current_level = get_current_log_level()
        logger.info(f"检测到测试环境，保持现有日志配置 - 级别: {current_level}")
        return logger

    # 确定日志级别
    if log_level is None:
        log_level = os.getenv("LOG_LEVEL", "INFO")

    # 测试环境特殊处理
    if environment == "testing":
        filter_sqlalchemy = True  # 测试时总是过滤SQLAlchemy日志
        enable_file_logging = True  # 测试时启用文件日志便于调试
        if log_level == "INFO":  # 如果没有明确设置，测试环境使用DEBUG级别
            log_level = "DEBUG"
    
    # 获取配置
    config = LoggerConfig.get_config(
        environment=environment,
        log_level=log_level,
        enable_file_logging=enable_file_logging,
        filter_sqlalchemy=filter_sqlalchemy
    )
    
    # 应用配置
    logging.config.dictConfig(config)
    
    # 获取应用日志器
    logger = logging.getLogger("app")
    logger.info(f"日志系统已初始化 - 环境: {environment}, 级别: {log_level}")
    
    return logger


def get_logger(name: str = "app") -> logging.Logger:
    """获取日志器"""
    return logging.getLogger(name)


# 便捷函数
def log_request(request_id: str, method: str, path: str, user_id: Optional[int] = None):
    """记录请求日志"""
    logger = get_logger("app.request")
    extra = {"request_id": request_id}
    if user_id:
        extra["user_id"] = user_id
    
    logger.info(f"{method} {path}", extra=extra)


def log_error(error: Exception, context: Optional[Dict[str, Any]] = None):
    """记录错误日志"""
    logger = get_logger("app.error")
    extra = context or {}
    logger.error(f"错误: {str(error)}", exc_info=True, extra=extra)


def log_business_event(event: str, details: Optional[Dict[str, Any]] = None):
    """记录业务事件日志"""
    logger = get_logger("app.business")
    extra = details or {}
    logger.info(f"业务事件: {event}", extra=extra)
