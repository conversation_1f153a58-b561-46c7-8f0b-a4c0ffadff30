from typing import Optional
from app.features.users.models import User, UserRole
from app.features.tenants.models import Tenant
from app.features.members.models import Member

class TenantContext:
    """租户上下文"""
    def __init__(self, tenant_id: int, tenant: Tenant):
        self.tenant_id = tenant_id
        self.tenant = tenant


class UserContext:
    """用户上下文（CMS用户）"""
    def __init__(self, user: User, tenant_context: Optional[TenantContext] = None):
        self.user = user
        self.tenant_context = tenant_context
        self.is_super_admin = user.role == UserRole.SUPER_ADMIN


class MemberContext:
    """会员上下文（前端会员）"""
    def __init__(self, member: Member, tenant_context: TenantContext):
        self.member = member
        self.tenant_context = tenant_context