from decouple import config
from typing import Optional


class Settings:
    """应用程序配置"""
    
    # 数据库配置
    DATABASE_URL: str = config("DATABASE_URL", default="postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_db")
    
    # 安全配置
    SECRET_KEY: str = config("SECRET_KEY", default="your-super-secret-key-here-111")
    ALGORITHM: str = config("ALGORITHM", default="HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = config("ACCESS_TOKEN_EXPIRE_MINUTES", default=30, cast=int)
    
    # 应用配置
    APP_NAME: str = config("APP_NAME", default="Course Booking API")
    APP_VERSION: str = config("APP_VERSION", default="1.0.0")
    DEBUG: bool = config("DEBUG", default=True, cast=bool)
    
    # API配置
    API_V1_STR: str = "/api/v1"


settings = Settings()
