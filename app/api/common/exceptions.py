"""统一异常处理"""

from typing import Optional, Any, Dict
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
from datetime import datetime, timezone
from enum import Enum


class BaseErrorCode(str, Enum):
    """错误码基类 - 所有错误码枚举都应继承此类"""
    pass


class GlobalErrorCode(BaseErrorCode):
    """全局状态码 - 包含成功和错误状态码"""
    # 成功状态码
    SUCCESS = "SUCCESS"

    # 通用业务错误
    BUSINESS_ERROR = "BUSINESS_ERROR"
    INVALID_PARAMETER = "INVALID_PARAMETER"
    VALIDATION_ERROR = "VALIDATION_ERROR"

    # 认证授权相关 - 需要全局处理
    AUTHENTICATION_FAILED = "AUTHENTICATION_FAILED"
    PERMISSION_DENIED = "PERMISSION_DENIED"
    TOKEN_INVALID = "TOKEN_INVALID"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"

    # 系统级错误 - 需要全局处理
    DATABASE_ERROR = "DATABASE_ERROR"
    INTERNAL_ERROR = "INTERNAL_ERROR"


class ErrorLevel(str, Enum):
    """错误级别"""
    INFO = "info"       # 信息提示
    WARNING = "warning" # 警告  
    ERROR = "error"     # 错误
    CRITICAL = "critical" # 严重错误


class APIException(HTTPException):
    """统一API异常基类"""
    
    def __init__(
        self,
        message: str,
        code: BaseErrorCode = GlobalErrorCode.BUSINESS_ERROR,
        http_status_code: int = 400,
        level: ErrorLevel = ErrorLevel.ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.code = code
        self.level = level
        self.details = details or {}
        self.http_status_code = http_status_code  # 保存HTTP状态码
        super().__init__(status_code=http_status_code, detail=self.to_dict())
    
    def __str__(self) -> str:
        """返回错误消息字符串"""
        return self.message
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "success": False,
            "message": self.message,
            "http_code": self.http_status_code,  # HTTP状态码
            "business_code": self.code.value,  # 业务错误码
            "level": self.level.value,
            "details": self.details,
            "timestamp": datetime.now().isoformat()
        }


class BusinessException(APIException):
    """业务异常 - 大部分业务错误使用这个"""
    def __init__(
        self, 
        message: str, 
        code: BaseErrorCode = GlobalErrorCode.BUSINESS_ERROR,
        level: ErrorLevel = ErrorLevel.ERROR,
        details: Optional[Dict] = None
    ):
        super().__init__(message, code, 400, level, details)


class NotFoundError(APIException):
    """资源不存在异常"""
    def __init__(
        self, 
        resource: str = "资源", 
        details: Optional[Dict] = None
    ):
        message = f"{resource}不存在"
        super().__init__(message, GlobalErrorCode.BUSINESS_ERROR, 404, ErrorLevel.WARNING, details)


class ValidationError(APIException):
    """数据验证异常"""
    def __init__(
        self, 
        message: str = "数据验证失败", 
        details: Optional[Dict] = None
    ):
        super().__init__(message, GlobalErrorCode.VALIDATION_ERROR, 422, ErrorLevel.WARNING, details)


class PermissionError(APIException):
    """权限异常"""
    def __init__(
        self, 
        message: str = "权限不足", 
        details: Optional[Dict] = None
    ):
        super().__init__(message, GlobalErrorCode.PERMISSION_DENIED, 403, ErrorLevel.ERROR, details)


class AuthenticationError(APIException):
    """认证异常"""
    def __init__(
        self, 
        message: str = "认证失败", 
        details: Optional[Dict] = None
    ):
        super().__init__(message, GlobalErrorCode.AUTHENTICATION_FAILED, 401, ErrorLevel.ERROR, details)


# 异常处理器
async def api_exception_handler(request: Request, exc: APIException) -> JSONResponse:
    """API异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content=exc.to_dict()  # 直接使用exc的字典格式
    )


async def validation_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """数据验证异常处理器"""
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "数据验证失败",
            "http_code": 422,
            "business_code": GlobalErrorCode.VALIDATION_ERROR.value,
            "level": ErrorLevel.WARNING.value,
            "details": {"validation_errors": str(exc)},
            "timestamp": datetime.now().isoformat()
        }
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """通用异常处理器"""
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "http_code": 500,
            "business_code": GlobalErrorCode.INTERNAL_ERROR.value,
            "level": ErrorLevel.CRITICAL.value,
            "details": {"error": str(exc)},
            "timestamp": datetime.now().isoformat()
        }
    )
