"""分页查询参数"""

from typing import Optional
from pydantic import BaseModel, Field


class PaginationParams(BaseModel):
    """分页参数"""
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页记录数")
    
    @property
    def skip(self) -> int:
        """计算跳过的记录数"""
        return (self.page - 1) * self.size
    
    @property
    def limit(self) -> int:
        """获取限制记录数"""
        return self.size


class SearchParams(PaginationParams):
    """搜索参数"""
    search: Optional[str] = Field(default=None, description="搜索关键词")
    
    
def get_pagination_params(
    page: int = 1,
    size: int = 20
) -> PaginationParams:
    """获取分页参数依赖"""
    return PaginationParams(page=page, size=size)
