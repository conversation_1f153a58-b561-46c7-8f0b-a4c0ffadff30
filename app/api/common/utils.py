"""API工具函数"""

from typing import Optional, Any
from .exceptions import (
    NotFoundError, 
    BusinessException,
)


def ensure_found(obj: Optional[Any], resource_name: str = "资源") -> Any:
    """确保对象存在，否则抛出NotFoundError"""
    if not obj:
        raise NotFoundError(resource_name)
    return obj


def ensure_success(success: bool, message: str = "操作失败") -> None:
    """确保操作成功，否则抛出BusinessException"""
    if not success:
        raise BusinessException(message)


def handle_service_error(func):
    """服务层错误处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValueError as e:
            raise BusinessException(str(e))
        except Exception as e:
            raise BusinessException(f"操作失败-未知错误: {str(e)}")
    return wrapper
