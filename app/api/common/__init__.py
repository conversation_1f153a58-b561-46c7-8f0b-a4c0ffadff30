"""API通用模块"""

from .responses import (
    BaseResponse,
    DataResponse,
    ListResponse,
    PageResponse,
    MessageResponse,
    success_response,
    list_response,
    page_response,
    message_response
)

from .exceptions import (
    APIException,
    BusinessException,
    NotFoundError,
    ValidationError,
    PermissionError,
    AuthenticationError,
    api_exception_handler,
    validation_exception_handler,
    general_exception_handler
)

from .utils import (
    ensure_found,
    ensure_success,
    handle_service_error
)

from .pagination import (
    PaginationParams,
    SearchParams,
    get_pagination_params
)

__all__ = [
    # 响应模型
    "BaseResponse",
    "DataResponse", 
    "ListResponse",
    "PageResponse",
    "MessageResponse",
    "success_response",
    "list_response", 
    "page_response",
    "message_response",
    
    # 异常处理
    "APIException",
    "BusinessException",
    "NotFoundError",
    "ValidationError", 
    "PermissionError",
    "AuthenticationError",
    "api_exception_handler",
    "validation_exception_handler",
    "general_exception_handler",
    
    # 工具函数
    "ensure_found",
    "ensure_success",
    "handle_service_error",
    
    # 分页
    "PaginationParams",
    "SearchParams", 
    "get_pagination_params"
]
