"""统一API响应模型"""

from typing import Generic, TypeVar, List, Optional, Any, Dict
from pydantic import BaseModel, Field
from app.api.common.exceptions import BaseErrorCode, ErrorLevel, GlobalErrorCode

T = TypeVar('T')


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(True, description="请求是否成功")
    message: str = Field("操作成功", description="响应消息")
    http_code: int = Field(200, description="HTTP状态码")
    business_code: str = Field(GlobalErrorCode.SUCCESS.value, description="业务状态码")
    timestamp: Optional[str] = None


class DataResponse(BaseResponse, Generic[T]):
    """单个数据响应模型"""
    data: Optional[T] = Field(None, description="响应数据")


class ListResponse(BaseResponse, Generic[T]):
    """列表响应模型"""
    data: List[T] = Field([], description="数据列表")
    total: int = Field(0, description="总记录数")


class PageResponse(BaseResponse, Generic[T]):
    """分页响应模型"""
    data: List[T] = Field([], description="数据列表")
    total: int = Field(0, description="总记录数")
    page: int = Field(1, description="当前页码")
    size: int = Field(20, description="每页记录数")
    pages: int = Field(0, description="总页数")

    @classmethod
    def create(cls, items: List[T], total: int, page: int, size: int):
        """创建分页响应"""
        pages = (total + size - 1) // size if size > 0 else 0
        return cls(
            data=items,
            total=total,
            page=page,
            size=size,
            pages=pages
        )


class MessageResponse(BaseResponse):
    """消息响应模型（无数据）"""
    pass


class SuccessResponse(BaseResponse):
    """成功响应模型"""
    success: bool = True
    data: Optional[Any] = None


class ErrorResponse(BaseResponse):
    """错误响应模型"""
    success: bool = False
    message: str = "操作失败"
    http_code: int = 400
    business_code: str = GlobalErrorCode.BUSINESS_ERROR
    level: str = ErrorLevel.ERROR
    details: Optional[Dict[str, Any]] = None


class ValidationErrorResponse(ErrorResponse):
    """数据验证错误响应"""
    http_code: int = 422
    business_code: str = GlobalErrorCode.VALIDATION_ERROR
    level: str = ErrorLevel.WARNING
    message: str = "数据验证失败"


# API文档用的错误响应示例
def create_error_responses(codes: List[BaseErrorCode]) -> Dict[int, Dict]:
    """为API文档创建错误响应示例"""
    responses = {}
    
    # 400 - 业务错误响应
    responses[400] = {
        "description": "业务逻辑错误 - 请求在业务层面不符合要求",
        "model": ErrorResponse,
        "content": {
            "application/json": {
                "examples": {
                    "business_error": {
                        "summary": "通用业务错误",
                        "description": "业务逻辑验证失败时返回的错误信息",
                        "value": {
                            "success": False,
                            "message": "操作失败，请检查请求参数",
                            "http_code": 400,
                            "business_code": "BUSINESS_ERROR",
                            "level": "error",
                            "details": {},
                            "timestamp": "2024-01-01T00:00:00Z"
                        }
                    }
                }
            }
        }
    }
    
    # 为每个业务错误码创建示例
    for code in codes:
        if code.value != GlobalErrorCode.BUSINESS_ERROR:  # 避免重复
            example_key = code.value.lower()
            responses[400]["content"]["application/json"]["examples"][example_key] = {
                "summary": f"错误码: {code.value}",
                "description": f"特定业务场景的错误：{code.value}",
                "value": {
                    "success": False,
                    "message": f"示例错误信息 - {code.value}",
                    "http_code": 400,
                    "business_code": code.value,
                    "level": "error",
                    "details": {},
                    "timestamp": "2024-01-01T00:00:00Z"
                }
            }
    
    # 401 - 认证失败
    responses[401] = {
        "description": "认证失败 - 未提供有效的身份验证凭据",
        "model": ErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": "认证失败，请先登录",
                    "http_code": 401,
                    "business_code": "AUTHENTICATION_FAILED",
                    "level": "error",
                    "details": {},
                    "timestamp": "2024-01-01T00:00:00Z"
                }
            }
        }
    }
    
    # 403 - 权限不足
    responses[403] = {
        "description": "权限不足 - 当前用户无权执行此操作",
        "model": ErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": "权限不足，无法执行此操作",
                    "http_code": 403,
                    "business_code": "PERMISSION_DENIED",
                    "level": "error",
                    "details": {},
                    "timestamp": "2024-01-01T00:00:00Z"
                }
            }
        }
    }
    
    # 404 - 资源不存在
    responses[404] = {
        "description": "资源不存在 - 请求的资源未找到",
        "model": ErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": "请求的资源不存在",
                    "http_code": 404,
                    "business_code": "BUSINESS_ERROR",
                    "level": "warning",
                    "details": {},
                    "timestamp": "2024-01-01T00:00:00Z"
                }
            }
        }
    }
    
    return responses


# 只需要认证的接口响应（401）
AUTH_ONLY_RESPONSES = {
    401: {
        "description": "认证失败 - 未提供有效的身份验证凭据",
        "model": ErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": "认证失败，请先登录",
                    "http_code": 401,
                    "business_code": "AUTHENTICATION_FAILED",
                    "level": "error",
                    "details": {},
                    "timestamp": "2024-01-01T00:00:00Z"
                }
            }
        }
    }
}

# 只需要权限的接口响应（403）
PERMISSION_ONLY_RESPONSES = {
    403: {
        "description": "权限不足 - 当前用户无权执行此操作",
        "model": ErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": "权限不足，无法执行此操作",
                    "http_code": 403,
                    "business_code": "PERMISSION_DENIED",
                    "level": "error",
                    "details": {},
                    "timestamp": "2024-01-01T00:00:00Z"
                }
            }
        }
    }
}

# 资源操作响应（需要认证+权限的接口）
RESOURCE_RESPONSES = {**AUTH_ONLY_RESPONSES, **PERMISSION_ONLY_RESPONSES}


# 便捷创建函数
def success_response(data: T = None, message: str = "操作成功") -> DataResponse[T]:
    """创建成功响应"""
    return DataResponse(data=data, message=message)


def list_response(items: List[T], total: int = None, message: str = "获取成功") -> ListResponse[T]:
    """创建列表响应"""
    if total is None:
        total = len(items)
    return ListResponse(data=items, total=total, message=message)


def page_response(items: List[T], total: int, page: int, size: int, message: str = "获取成功") -> PageResponse[T]:
    """创建分页响应"""
    return PageResponse.create(items, total, page, size).model_copy(update={"message": message})


def message_response(message: str = "操作成功", http_code: int = 200, business_code: str = GlobalErrorCode.SUCCESS.value) -> MessageResponse:
    """创建消息响应"""
    return MessageResponse(message=message, http_code=http_code, business_code=business_code)
