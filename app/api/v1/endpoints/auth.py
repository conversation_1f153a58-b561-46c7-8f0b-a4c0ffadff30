from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, status
from sqlmodel import Session, select, text
from pydantic import BaseModel

from app.features.users.models import User, UserRole, UserStatus
from app.features.members.models import Member, MemberStatus
from app.features.members.schemas import MemberLogin
from app.features.tenants.models import Tenant
from app.db.session import get_session
from app.utils.security import verify_password, create_access_token
from app.core.config import settings

# 导入新的响应模型和异常处理
from app.api.common.responses import DataResponse, success_response
from app.api.common.exceptions import BusinessException, NotFoundError, AuthenticationError
from app.api.common.utils import ensure_found

router = APIRouter()


class AdminLoginRequest(BaseModel):
    """管理员登录请求"""
    email: str
    password: str
    tenant_code: str = None  # 可选，超级管理员不需要


class AdminLoginResponse(BaseModel):
    """管理员登录响应"""
    access_token: str
    token_type: str
    user: dict
    tenant: dict = None


class MemberLoginResponse(BaseModel):
    """会员登录响应"""
    access_token: str
    token_type: str
    member: dict
    tenant: dict


@router.post("/admin/login", response_model=DataResponse[AdminLoginResponse])
def admin_login(
    login_data: AdminLoginRequest,
    session: Session = Depends(get_session)
):
    """CMS管理员登录"""
    tenant = None
    tenant_id = None
    
    if login_data.tenant_code:
        # 租户管理员登录
        tenant_stmt = select(Tenant).where(Tenant.code == login_data.tenant_code)
        tenant = session.exec(tenant_stmt).first()
        
        if not tenant:
            raise NotFoundError("租户")
        
        # 设置租户上下文
        session.execute(text(f"SET app.current_tenant_id = '{tenant.id}'"))
        tenant_id = tenant.id
    else:
        # 超级管理员登录，清除租户上下文
        try:
            session.execute(text("RESET app.current_tenant_id"))
        except:
            pass
    
    # 查找用户
    user_stmt = select(User).where(User.email == login_data.email)
    user = session.exec(user_stmt).first()
    
    if not user or not verify_password(login_data.password, user.password_hash):
        raise AuthenticationError("邮箱或密码错误")
    
    if user.status != UserStatus.ACTIVE:
        raise AuthenticationError("用户账户已被禁用")
    
    # 验证用户是否属于正确的租户
    if tenant_id and user.tenant_id != tenant_id:
        raise AuthenticationError("用户不属于该租户")
    
    if not tenant_id and user.role != UserRole.SUPER_ADMIN:
        raise AuthenticationError("只有超级管理员可以直接登录")
    
    # 更新登录信息
    from datetime import datetime, timezone
    user.last_login_at = datetime.now()
    user.login_count += 1
    user.failed_login_attempts = 0
    session.add(user)
    session.commit()
    
    # 创建访问令牌 - 按照文档要求的格式
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    token_data = {
        "sub": str(user.id),  # 使用用户UUID作为主题
        "user_type": "admin",  # 按文档要求使用admin
        "role": user.role.value,
        "email": user.email
    }
    
    if tenant:
        token_data.update({
            "tenant_id": str(tenant.id),
            "tenant_code": tenant.code
        })
    
    access_token = create_access_token(
        data=token_data,
        expires_delta=access_token_expires
    )
    
    response_data = {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": str(user.id),
            "username": user.username,
            "email": user.email,
            # "real_name": user.real_name,
            "role": user.role.value
        }
    }
    
    if tenant:
        response_data["tenant"] = {
            "id": str(tenant.id),
            "code": tenant.code,
            "name": tenant.name
        }
    
    return success_response(response_data, "登录成功")


@router.post("/member/login", response_model=DataResponse[MemberLoginResponse])
def member_login(
    login_data: MemberLogin,
    session: Session = Depends(get_session)
):
    """会员登录（手机号+验证码）"""
    # 查找租户
    tenant_stmt = select(Tenant).where(Tenant.code == login_data.tenant_code)
    tenant = ensure_found(session.exec(tenant_stmt).first(), "租户")
    
    # 设置租户上下文
    session.execute(text(f"SET app.current_tenant_id = '{tenant.id}'"))
    
    # 验证验证码（这里简化处理，实际应该从Redis或数据库验证）
    # TODO: 实现验证码验证逻辑
    if login_data.verification_code != "1234":  # 临时验证码
        raise AuthenticationError("验证码错误")
    
    # 查找会员
    member_stmt = select(Member).where(
        Member.phone == login_data.phone,
        Member.tenant_id == tenant.id
    )
    member = ensure_found(session.exec(member_stmt).first(), "会员")
    
    if member.member_status != MemberStatus.ACTIVE:
        raise AuthenticationError("会员账户已被禁用")
    
    # 更新登录信息
    from datetime import datetime, timezone
    member.last_login_at = datetime.now()
    session.add(member)
    session.commit()
    
    # 创建访问令牌 - 按照文档要求的格式
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={
            "sub": str(member.id),  # 使用会员UUID作为主题
            "user_type": "member",
            "tenant_id": str(tenant.id),
            "tenant_code": tenant.code,
            "membership_level": member.member_type.value if member.member_type else "regular"
        },
        expires_delta=access_token_expires
    )
    
    response_data = {
        "access_token": access_token,
        "token_type": "bearer",
        "member": {
            "id": str(member.id),
            "phone": member.phone,
            # "real_name": member.real_name,
            # "nickname": member.nickname,
            "member_type": member.member_type.value if member.member_type else "regular"
        },
        "tenant": {
            "id": str(tenant.id),
            "code": tenant.code,
            "name": tenant.name
        }
    }
    
    return success_response(response_data, "登录成功")


@router.post("/member/send-code", response_model=DataResponse[dict])
def send_verification_code(
    phone: str,
    tenant_code: str,
    session: Session = Depends(get_session)
):
    """发送验证码"""
    # 查找租户
    tenant_stmt = select(Tenant).where(Tenant.code == tenant_code)
    tenant = ensure_found(session.exec(tenant_stmt).first(), "租户")
    
    # TODO: 实现发送验证码逻辑
    # 这里应该：
    # 1. 生成验证码
    # 2. 存储到Redis（带过期时间）
    # 3. 调用短信服务发送
    
    # 临时返回成功
    return success_response({"sent": True}, "验证码发送成功") 