from fastapi import APIRouter, Depends, status, Query
from sqlmodel import Session
from typing import List, Optional
from datetime import date, time
from app.features.members.schemas import MemberUpdate, MemberRead
from app.features.members.service import get_member_service
from app.core.dependencies import MemberContext, get_member_context
from app.db.session import get_session

# 导入新的响应模型和异常处理
from app.api.common.responses import DataResponse, PageResponse, success_response, page_response
from app.api.common.exceptions import BusinessException
from app.api.common.utils import ensure_success

# 导入课程相关的模块
from app.features.courses.scheduled_classes_service import ScheduledClassService
from app.features.courses.scheduled_classes_schemas import (
    ScheduledClassList, ScheduledClassRead, MemberClassBooking
)

router = APIRouter()


@router.get("/me", response_model=DataResponse[MemberRead])
def get_my_profile(
    member_context: MemberContext = Depends(get_member_context)
):
    """获取我的个人信息"""
    return success_response(member_context.member, "获取个人信息成功")


@router.put("/me", response_model=DataResponse[MemberRead])
def update_my_profile(
    member_data: MemberUpdate,
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """更新我的个人信息"""
    member_service = get_member_service(session, member_context.tenant_context.tenant_id)
    
    # 会员只能更新部分信息，过滤掉敏感字段
    allowed_fields = {
        'name', 'email', 'gender', 'birthday', 'address', 
        'city', 'province', 'notes'
    }
    
    update_data = member_data.model_dump(exclude_unset=True)
    filtered_data = {k: v for k, v in update_data.items() if k in allowed_fields}
    
    if not filtered_data:
        raise BusinessException("没有可更新的字段")
    
    # 创建过滤后的更新对象
    filtered_update = MemberUpdate(**filtered_data)
    
    updated_member = member_service.update_member(
        member_context.member.id, 
        filtered_update
    )
    
    ensure_success(updated_member is not None, "更新失败")
    
    return success_response(updated_member, "个人信息更新成功")


@router.get("/my-stats", response_model=DataResponse[dict])
def get_my_stats(
    member_context: MemberContext = Depends(get_member_context)
):
    """获取我的统计信息"""
    member = member_context.member
    
    stats_data = {
        "total_classes": member.total_classes,
        "completed_classes": member.completed_classes,
        "cancelled_classes": member.cancelled_classes,
        "no_show_classes": member.no_show_classes,
        "total_spent": float(member.total_spent),
        "avg_rating": float(member.avg_rating),
        "rating_count": member.rating_count,
        "last_class_at": member.last_class_at,
        "member_since": member.registered_at
    }
    
    return success_response(stats_data, "获取统计信息成功")


@router.get("/tenant-info", response_model=DataResponse[dict])
def get_tenant_info(
    member_context: MemberContext = Depends(get_member_context)
):
    """获取租户信息"""
    tenant = member_context.tenant_context.tenant
    
    tenant_info = {
        "id": tenant.id,
        "name": tenant.name,
        "code": tenant.code,
        "description": tenant.description,
        "contact_email": tenant.contact_email,
        "contact_phone": tenant.contact_phone
    }
    
    return success_response(tenant_info, "获取租户信息成功")


# ==================== 课程相关接口 ====================

@router.get("/courses/available", response_model=PageResponse[ScheduledClassList])
def get_available_courses(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    teacher_id: Optional[int] = Query(None, gt=0, description="教师ID"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    time_from: Optional[time] = Query(None, description="时间段开始"),
    time_to: Optional[time] = Query(None, description="时间段结束"),
    max_price: Optional[int] = Query(None, ge=0, description="最高价格"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取可预约课程列表"""
    tenant_id = member_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)

    # 获取可用课程列表
    from datetime import datetime
    available_classes = service.get_available_classes(
        teacher_id=teacher_id,
        date_from=datetime.combine(date_from, time.min) if date_from else None,
        date_to=datetime.combine(date_to, time.max) if date_to else None
    )

    # 价格过滤
    if max_price is not None:
        available_classes = [cls for cls in available_classes if cls.price is None or cls.price <= max_price]

    # 转换为响应模型
    classes_response = [
        ScheduledClassList.model_validate(cls)
        for cls in available_classes
    ]

    return page_response(
        items=classes_response,
        total=len(classes_response),
        page=page,
        size=size,
        message="获取可预约课程列表成功"
    )


@router.get("/courses/my", response_model=PageResponse[ScheduledClassList])
def get_my_courses(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    status: Optional[str] = Query(None, description="课程状态"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的课程列表"""
    tenant_id = member_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)

    # 获取会员课程
    from datetime import datetime
    member_classes = service.get_member_classes(
        member_id=member_context.member.id,
        date_from=datetime.combine(date_from, time.min) if date_from else None,
        date_to=datetime.combine(date_to, time.max) if date_to else None,
        status=status
    )

    # 转换为响应模型
    classes_response = [
        ScheduledClassList.model_validate(cls)
        for cls in member_classes
    ]

    return page_response(
        items=classes_response,
        total=len(classes_response),
        page=page,
        size=size,
        message="获取我的课程列表成功"
    )


@router.post("/courses/{class_id}/book", response_model=DataResponse[ScheduledClassRead])
def book_course(
    class_id: int,
    booking_data: MemberClassBooking,
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """预约课程 - 会员自主预约（支持会员卡自动扣费）"""
    tenant_id = member_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)

    # 设置课程ID
    booking_data.class_id = class_id

    # 会员自主预约课程
    scheduled_class = service.book_member_class(
        member_id=member_context.member.id,
        member_booking_data=booking_data,
        created_by=member_context.member.id  # 会员自己操作
    )

    # 转换为响应模型
    class_response = ScheduledClassRead.model_validate(scheduled_class)

    return success_response(class_response, "课程预约成功")