from typing import List, Optional
from fastapi import APIRouter, Depends, status, Query
from sqlmodel import Session
from app.db.session import get_session
from app.features.users.models import User
from app.features.users.service import UserService
from app.core.dependencies import get_current_user

from pydantic import BaseModel, EmailStr, Field
import datetime

# 导入新的响应模型
from app.api.common.responses import DataResponse, success_response

class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponseData(BaseModel):
    message: str
    username: str
    roles: List[str]
    accessToken: str
    refreshToken: str
    expires: int
    pureAdminBackend: str

class LoginResponse(BaseModel):
    success: bool
    data: LoginResponseData

class RouteMeta(BaseModel):
    title: str
    icon: str = None
    rank: int = None
    roles: List[str] = None
    auths: List[str] = None
    showLink: bool = None
    showParent: bool = None

class RouteChild(BaseModel):
    path: str
    name: str = None
    component: str = None
    meta: RouteMeta
    children: List['RouteChild'] = None

class RouteItem(BaseModel):
    path: str
    # redirect: str = None
    redirect: Optional[str] = None #允许接口最终返回null
    meta: RouteMeta
    children: List[RouteChild] = None

class SyncRoutesResponseData(BaseModel):
    success: bool
    data: List[RouteItem]

router = APIRouter()


@router.post("/login", response_model=LoginResponse)
async def login(login_request: LoginRequest):
    """获取当前用户信息"""
    print(login_request)
    return {
        "success": True,
        "data": {
            "message": "success",
            "username": login_request.username,
            "roles": ["admin"],
            "accessToken": "eyJhbGciOiJIUzUxMiJ9.admin",
            "refreshToken": "eyJhbGciOiJIUzUxMiJ9.adminRefresh",
            "expires": int(datetime.datetime.now().timestamp() * 1000) + 24 * 60 * 60 * 1000,  # 24 hours from now
            "pureAdminBackend": "这个标识是ks-admin-backend真实后端返回的接口，只是为了演示"
        }
    }

@router.get("/syncRoutes", response_model=SyncRoutesResponseData, response_model_exclude_unset=True)
async def syncRoutes():
    """同步路由"""
    return {
        "success": True,
        "data": [
            {
                "path": "/permission",
                "meta": {
                    "title": "权限管理-后台",
                    "icon": "ep:lollipop",
                    "rank": 10
                },
                "children": [
                    {
                        "path": "/permission/page/index",
                        "name": "PermissionPage",
                        "meta": {
                            "title": "页面权限-后台",
                            "roles": ["admin", "common"]
                        }
                    },
                    {
                        "path": "/permission/button",
                        "meta": {
                            "title": "按钮权限",
                            "roles": ["admin", "common"]
                        },
                        "children": [
                            {
                                "path": "/permission/button/router",
                                "component": "permission/button/index",
                                "name": "PermissionButtonRouter",
                                "meta": {
                                    "title": "路由返回按钮权限",
                                    "auths": [
                                        "permission:btn:add",
                                        "permission:btn:edit",
                                        "permission:btn:delete"
                                    ]
                                }
                            },
                            {
                                "path": "/permission/button/login",
                                "component": "permission/button/perms",
                                "name": "PermissionButtonLogin",
                                "meta": {
                                    "title": "登录接口返回按钮权限"
                                }
                            }
                        ]
                    }
                ]
            },
            {
                "path": "/test",
                "redirect": None,
                "meta": {
                    "icon": "skill-icons:apple-dark",
                    "title": "测试页面",
                    "rank": 20
                },
                "children": [
                    {
                        "path": "/test/index",
                        "name": "test-index",
                        "component": "test",
                        "meta": {
                            "title": "test-index",
                            "showParent": True
                        }
                    },
                    {
                        "path": "/test/index/2",
                        "name": "test-index-2",
                        "component": "test/index",
                        "meta": {
                            "title": "test-index-2",
                            "showParent": True
                        }
                    }
                ]
            }
        ]
    }