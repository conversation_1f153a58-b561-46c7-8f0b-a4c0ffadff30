"""
公共API - 公开信息
"""
from fastapi import APIRouter
from app.api.common.responses import DataResponse, success_response

router = APIRouter()

@router.get("/health")
def health_check():
    """健康检查"""
    return {"status": "ok", "message": "服务正常运行"}

@router.get("/version")
def get_version():
    """获取API版本信息"""
    return success_response({
        "version": "1.0.0",
        "api_version": "v1",
        "description": "KS English Admin Backend API"
    }, "获取版本信息成功")
