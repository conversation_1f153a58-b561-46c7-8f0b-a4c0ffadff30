"""
管理端 - 会员卡管理API
"""
from fastapi import APIRouter, Depends, Query, status
from sqlmodel import Session
from typing import List, Optional

from app.db.session import get_session
from app.core.dependencies import get_user_context, UserContext
from app.api.common.responses import DataResponse, PageResponse, success_response, page_response

# 会员卡相关导入
from app.features.member_cards.template_service import MemberCardTemplateService
from app.features.member_cards.card_service import MemberCardService
from app.features.member_cards.recharge_service import RechargeService
from app.features.member_cards.consumption_service import ConsumptionService
from app.features.member_cards.schemas import (
    # 模板相关
    MemberCardTemplateCreate, MemberCardTemplateUpdate, MemberCardTemplateRead, 
    MemberCardTemplateList, MemberCardTemplateQuery,
    # 卡片相关
    MemberCardCreate, MemberCardUpdate, MemberCardRead, MemberCardList, MemberCardQuery,
    # 充值相关
    RechargeRequest, RechargeResponse,
    # 操作记录相关
    MemberCardOperationRead, MemberCardOperationQuery
)

router = APIRouter()

# ==================== 会员卡模板管理 ====================

@router.get("/templates", response_model=PageResponse[MemberCardTemplateList])
def get_card_templates(
    query: MemberCardTemplateQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡模板列表"""
    service = MemberCardTemplateService(session, user_context.tenant_context.tenant_id)
    templates, total = service.get_templates(query)
    return page_response(templates, total, query.page, query.size, "获取模板列表成功")

@router.post("/templates", response_model=DataResponse[MemberCardTemplateRead], status_code=status.HTTP_201_CREATED)
def create_card_template(
    template_data: MemberCardTemplateCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建会员卡模板"""
    service = MemberCardTemplateService(session, user_context.tenant_context.tenant_id)
    template = service.create_template(template_data, created_by=user_context.user.id)
    return success_response(template, "创建模板成功")

# ==================== 会员卡管理 ====================

@router.get("/cards", response_model=PageResponse[MemberCardList])
def get_member_cards(
    query: MemberCardQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡列表"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    cards, total = service.get_cards(query)
    return page_response(cards, total, query.page, query.size, "获取会员卡列表成功")

@router.post("/cards", response_model=DataResponse[MemberCardRead], status_code=status.HTTP_201_CREATED)
def create_member_card(
    card_data: MemberCardCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建会员卡"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    card = service.create_card(card_data, created_by=user_context.user.id)
    return success_response(card, "创建会员卡成功")

# ==================== 充值管理 ====================

@router.post("/cards/{card_id}/recharge", response_model=DataResponse[RechargeResponse])
def recharge_member_card(
    card_id: int,
    recharge_data: RechargeRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """会员卡充值"""
    service = RechargeService(session, user_context.tenant_context.tenant_id)
    result = service.recharge(card_id, recharge_data, operator_id=user_context.user.id)
    return success_response(result, "充值成功")

# ==================== 操作记录查询 ====================

@router.get("/operations", response_model=PageResponse[MemberCardOperationRead])
def get_card_operations(
    query: MemberCardOperationQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡操作记录"""
    service = ConsumptionService(session, user_context.tenant_context.tenant_id)
    operations, total = service.get_operations(query)
    return page_response(operations, total, query.page, query.size, "获取操作记录成功")
