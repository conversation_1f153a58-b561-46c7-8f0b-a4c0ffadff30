from fastapi import APIRouter
from app.api.v1.endpoints import auth, member_app, test
from app.features.tenants.router import router as tenants_router
from app.features.users.router import router as users_router
from app.features.members.router import router as members_router
from app.features.members.fixed_lock_router import router as member_fixed_locks_router
from app.features.tags.router import router as tags_router
from app.features.teachers.router import router as teachers_router
from app.features.courses.config_router import router as course_config_router
from app.features.courses.scheduled_classes_router import router as scheduled_classes_router


api_router = APIRouter()

# 注册路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users_router, prefix="/users", tags=["用户管理"])
api_router.include_router(members_router, prefix="/members", tags=["会员管理"])
api_router.include_router(member_fixed_locks_router, prefix="/members/fixed-locks", tags=["会员固定课位锁定"])
api_router.include_router(tenants_router, prefix="/tenants", tags=["租户管理"])
api_router.include_router(tags_router, prefix="/tags", tags=["标签管理"])
api_router.include_router(teachers_router, prefix="/teachers", tags=["教师管理"])
api_router.include_router(course_config_router, prefix="/courses", tags=["课程系统配置"])
api_router.include_router(scheduled_classes_router, prefix="/courses/classes", tags=["已排课表"])
api_router.include_router(member_app.router, prefix="/member-app", tags=["会员端"])
api_router.include_router(test.router, prefix="/test", tags=["测试"])