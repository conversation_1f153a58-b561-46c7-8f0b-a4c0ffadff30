"""
会员端 - 课程预约API
"""
from fastapi import APIRouter, Depends, Query
from sqlmodel import Session
from typing import Optional
from datetime import date, time

from app.db.session import get_session
from app.core.dependencies import get_member_context, MemberContext
from app.api.common.responses import DataResponse, PageResponse, success_response, page_response
from app.features.courses.scheduled_classes_service import ScheduledClassService
from app.features.courses.scheduled_classes_schemas import (
    ScheduledClassList, ScheduledClassRead, MemberClassBooking
)

router = APIRouter()

@router.get("/available", response_model=PageResponse[ScheduledClassList])
def get_available_courses(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    teacher_id: Optional[int] = Query(None, gt=0, description="教师ID"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    max_price: Optional[int] = Query(None, ge=0, description="最高价格"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取可预约课程列表"""
    tenant_id = member_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    from datetime import datetime
    available_classes = service.get_available_classes(
        teacher_id=teacher_id,
        date_from=datetime.combine(date_from, time.min) if date_from else None,
        date_to=datetime.combine(date_to, time.max) if date_to else None
    )
    
    if max_price is not None:
        available_classes = [cls for cls in available_classes if cls.price is None or cls.price <= max_price]
    
    classes_response = [ScheduledClassList.model_validate(cls) for cls in available_classes]
    
    return page_response(classes_response, len(classes_response), page, size, "获取可预约课程列表成功")

@router.get("/my", response_model=PageResponse[ScheduledClassList])
def get_my_courses(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    status: Optional[str] = Query(None, description="课程状态"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的课程列表"""
    tenant_id = member_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    from datetime import datetime
    member_classes = service.get_member_classes(
        member_id=member_context.member.id,
        date_from=datetime.combine(date_from, time.min) if date_from else None,
        date_to=datetime.combine(date_to, time.max) if date_to else None,
        status=status
    )
    
    classes_response = [ScheduledClassList.model_validate(cls) for cls in member_classes]
    
    return page_response(classes_response, len(classes_response), page, size, "获取我的课程列表成功")

@router.post("/{class_id}/book", response_model=DataResponse[ScheduledClassRead])
def book_course(
    class_id: int,
    booking_data: MemberClassBooking,
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """预约课程 - 会员自主预约（支持会员卡自动扣费）"""
    tenant_id = member_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    booking_data.class_id = class_id
    
    scheduled_class = service.book_member_class(
        member_id=member_context.member.id,
        member_booking_data=booking_data,
        created_by=member_context.member.id
    )
    
    class_response = ScheduledClassRead.model_validate(scheduled_class)
    return success_response(class_response, "课程预约成功")
