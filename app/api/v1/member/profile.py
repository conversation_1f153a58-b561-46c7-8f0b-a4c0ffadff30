"""
会员端 - 个人信息管理API
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session

from app.db.session import get_session
from app.core.dependencies import get_member_context, MemberContext
from app.api.common.responses import DataResponse, success_response
from app.features.members.schemas import MemberRead, MemberUpdate
from app.features.members.service import get_member_service

router = APIRouter()

@router.get("/me", response_model=DataResponse[MemberRead])
def get_my_profile(member_context: MemberContext = Depends(get_member_context)):
    """获取我的个人信息"""
    return success_response(member_context.member, "获取个人信息成功")

@router.put("/me", response_model=DataResponse[MemberRead])
def update_my_profile(
    update_data: MemberUpdate,
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """更新我的个人信息"""
    service = get_member_service(session, member_context.tenant_context.tenant_id)
    updated_member = service.update_member(
        member_context.member.id, update_data
    )
    return success_response(updated_member, "更新个人信息成功")
