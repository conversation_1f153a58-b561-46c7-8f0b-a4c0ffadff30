"""
会员端 - 记录查询API
"""
from fastapi import APIRouter, Depends, Query
from sqlmodel import Session
from typing import Optional
from datetime import date

from app.db.session import get_session
from app.core.dependencies import get_member_context, MemberContext
from app.api.common.responses import PageResponse, page_response
from app.features.member_cards.consumption_service import ConsumptionService
from app.features.member_cards.schemas import MemberCardOperationRead, MemberCardOperationQuery

router = APIRouter()

@router.get("/operations", response_model=PageResponse[MemberCardOperationRead])
def get_my_card_operations(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    operation_type: Optional[str] = Query(None, description="操作类型"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的会员卡操作记录"""
    service = ConsumptionService(session, member_context.tenant_context.tenant_id)
    
    query = MemberCardOperationQuery(
        member_id=member_context.member.id,
        operation_type=operation_type,
        date_from=date_from,
        date_to=date_to,
        page=page,
        size=size
    )
    
    operations, total = service.get_operations(query)
    return page_response(operations, total, page, size, "获取操作记录成功")
