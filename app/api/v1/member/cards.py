"""
会员端 - 会员卡管理API
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session
from typing import List

from app.db.session import get_session
from app.core.dependencies import get_member_context, MemberContext
from app.api.common.responses import DataResponse, success_response
from app.features.member_cards.card_service import MemberCardService
from app.features.member_cards.schemas import MemberCardRead, MemberCardSummary

router = APIRouter()

@router.get("/my", response_model=DataResponse[List[MemberCardRead]])
def get_my_cards(
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的会员卡列表"""
    service = MemberCardService(session, member_context.tenant_context.tenant_id)
    cards = service.get_member_active_cards(member_context.member.id)
    cards_response = [MemberCardRead.model_validate(card) for card in cards]
    return success_response(cards_response, "获取我的会员卡成功")

@router.get("/my/primary", response_model=DataResponse[MemberCardRead])
def get_my_primary_card(
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的主要会员卡"""
    service = MemberCardService(session, member_context.tenant_context.tenant_id)
    card = service.get_card(member_context.member.primary_member_card_id)
    return success_response(card, "获取主要会员卡成功")
