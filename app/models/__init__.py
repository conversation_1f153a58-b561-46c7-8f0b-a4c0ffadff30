"""
导入所有SQLModel模型以确保表结构被正确创建

这个文件的作用是在调用 SQLModel.metadata.create_all() 时，
确保所有模型都被导入，从而创建对应的数据库表。
"""

# 导入用户相关模型
from app.features.users.models import User, UserSession

# 导入租户相关模型
from app.features.tenants.models import Tenant, TenantPlanTemplate

# 导入会员相关模型
from app.features.members.models import Member
from app.features.members.fixed_lock_models import MemberFixedSlotLock

# 导入教师相关模型
from app.features.teachers.models import Teacher, TeacherTag
from app.features.teachers.fixed_slots_models import TeacherFixedSlot

# 导入标签相关模型
from app.features.tags.models import TagCategory, Tag

# 导入课程相关模型
from app.features.courses.config_models import CourseSystemConfig
from app.features.courses.scheduled_classes_models import ScheduledClass

# 导入会员卡相关模型
from app.features.member_cards.models import (
    MemberCardTemplate,
    MemberCard,
    RechargeRecord,
    ConsumptionRecord
)

# 导入共享模型
from app.models.shared import (
    SystemConfig,
    SystemAdmin,
    TenantUsageStats,
    TenantUsageMonthly,
    SystemAuditLog
)

# 导出所有模型，确保它们被注册到SQLModel.metadata中
__all__ = [
    # 用户模型
    "User", "UserSession",

    # 租户模型
    "Tenant", "TenantPlanTemplate",

    # 会员模型
    "Member", "MemberFixedSlotLock",

    # 教师模型
    "Teacher", "TeacherFixedSlot",

    # 标签模型
    "TagCategory", "Tag", "TeacherTag",

    # 课程模型
    "CourseSystemConfig", "ScheduledClass",

    # 会员卡模型
    "MemberCardTemplate", "MemberCard", "RechargeRecord", "ConsumptionRecord",

    # 共享模型
    "SystemConfig", "SystemAdmin", "TenantUsageStats", "TenantUsageMonthly", "SystemAuditLog",
]