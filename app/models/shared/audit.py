from sqlmodel import SQLModel, Field, Column
from sqlalchemy import <PERSON><PERSON><PERSON>
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum
from sqlalchemy.dialects.postgresql import JSONB


class AuditStatus(str, Enum):
    """审计状态枚举"""
    SUCCESS = "success"
    FAILED = "failed"
    ERROR = "error"


# 系统操作日志模型
class SystemAuditLogBase(SQLModel):
    """系统操作日志基础模型"""
    action: str = Field(max_length=50, description="操作动作")
    resource_type: Optional[str] = Field(default=None, max_length=50, description="资源类型")
    resource_id: Optional[int] = Field(default=None, description="资源ID")

    # 操作者信息
    admin_id: Optional[int] = Field(default=None, foreign_key="system_admins.id", description="管理员ID")
    admin_username: Optional[str] = Field(default=None, max_length=50, description="管理员用户名")
    tenant_id: Optional[int] = Field(default=None, foreign_key="tenants.id", description="租户ID")

    # 请求信息
    ip_address: Optional[str] = Field(default=None, description="IP地址")
    user_agent: Optional[str] = Field(default=None, description="用户代理")
    request_id: Optional[str] = Field(default=None, max_length=100, description="请求ID")

    # 操作描述
    description: Optional[str] = Field(default=None, description="操作描述")

    # 结果
    status: AuditStatus = Field(default=AuditStatus.SUCCESS, description="操作状态")
    error_message: Optional[str] = Field(default=None, description="错误信息")


class SystemAuditLog(SystemAuditLogBase, table=True):
    """系统操作日志数据库模型"""
    __tablename__ = "system_audit_logs"

    id: Optional[int] = Field(default=None, primary_key=True)
    operation_time: datetime = Field(default_factory=datetime.utcnow, description="操作时间")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    
    # 操作详情 (JSON字段)
    old_values: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSONB), description="操作前的值")
    new_values: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSONB), description="操作后的值")
    extra_data: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSONB), description="额外信息")

# API模型
class AuditLogCreate(SystemAuditLogBase):
    """创建审计日志请求模型"""
    pass 