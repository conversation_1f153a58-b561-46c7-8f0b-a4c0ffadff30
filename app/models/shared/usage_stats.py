from sqlmodel import SQLModel, Field
from typing import Optional
from datetime import datetime, date
from decimal import Decimal
from enum import Enum


class BillingStatus(str, Enum):
    """计费状态枚举"""
    PENDING = "pending"
    BILLED = "billed"
    PAID = "paid"
    OVERDUE = "overdue"


# 租户使用量统计模型
class TenantUsageStatsBase(SQLModel):
    """租户使用量统计基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    stat_date: date = Field(description="统计日期")
    stat_month: str = Field(max_length=7, description="统计月份 YYYY-MM")
    stat_year: int = Field(description="统计年份")

    # 课程统计
    total_classes: int = Field(default=0, description="总课程数")
    completed_classes: int = Field(default=0, description="完成课程数")
    cancelled_classes: int = Field(default=0, description="取消课程数")
    no_show_classes: int = Field(default=0, description="缺席课程数")

    # 用户活跃度
    active_teachers: int = Field(default=0, description="活跃教师数")
    active_members: int = Field(default=0, description="活跃会员数")
    new_members: int = Field(default=0, description="新增会员数")
    new_teachers: int = Field(default=0, description="新增教师数")

    # 财务数据
    total_revenue: Decimal = Field(default=Decimal("0.00"), max_digits=12, decimal_places=2, description="总收入")
    total_transactions: int = Field(default=0, description="总交易数")
    avg_transaction_amount: Decimal = Field(default=Decimal("0.00"), max_digits=10, decimal_places=2, description="平均交易金额")

    # 系统使用量
    api_calls: int = Field(default=0, description="API调用次数")
    storage_used_mb: int = Field(default=0, description="存储使用量(MB)")
    login_sessions: int = Field(default=0, description="登录会话数")

    # 计费相关
    billable_classes: int = Field(default=0, description="计费课程数")
    billing_amount: Decimal = Field(default=Decimal("0.00"), max_digits=10, decimal_places=2, description="计费金额")


class TenantUsageStats(TenantUsageStatsBase, table=True):
    """租户使用量统计数据库模型"""
    __tablename__ = "tenant_usage_stats"

    id: Optional[int] = Field(default=None, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)


# 租户使用量月度汇总模型
class TenantUsageMonthlyBase(SQLModel):
    """租户使用量月度汇总基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    stat_month: str = Field(max_length=7, description="统计月份 YYYY-MM")
    stat_year: int = Field(description="统计年份")

    # 课程统计汇总
    total_classes: int = Field(default=0, description="总课程数")
    completed_classes: int = Field(default=0, description="完成课程数")
    cancelled_classes: int = Field(default=0, description="取消课程数")
    billable_classes: int = Field(default=0, description="计费课程数")

    # 用户统计汇总
    max_active_teachers: int = Field(default=0, description="月内最大活跃教师数")
    max_active_members: int = Field(default=0, description="月内最大活跃会员数")
    avg_active_teachers: Decimal = Field(default=Decimal("0.00"), max_digits=5, decimal_places=2, description="月平均活跃教师数")
    avg_active_members: Decimal = Field(default=Decimal("0.00"), max_digits=5, decimal_places=2, description="月平均活跃会员数")

    # 财务统计汇总
    total_revenue: Decimal = Field(default=Decimal("0.00"), max_digits=12, decimal_places=2, description="总收入")
    total_transactions: int = Field(default=0, description="总交易数")

    # 系统使用量汇总
    total_api_calls: int = Field(default=0, description="总API调用次数")
    max_storage_used_mb: int = Field(default=0, description="最大存储使用量(MB)")
    total_login_sessions: int = Field(default=0, description="总登录会话数")

    # 计费信息
    base_fee: Decimal = Field(default=Decimal("0.00"), max_digits=10, decimal_places=2, description="基础费用")
    usage_fee: Decimal = Field(default=Decimal("0.00"), max_digits=10, decimal_places=2, description="使用费用")
    total_billing_amount: Decimal = Field(default=Decimal("0.00"), max_digits=10, decimal_places=2, description="总计费金额")
    billing_status: BillingStatus = Field(default=BillingStatus.PENDING, description="计费状态")


class TenantUsageMonthly(TenantUsageMonthlyBase, table=True):
    """租户使用量月度汇总数据库模型"""
    __tablename__ = "tenant_usage_monthly"

    id: Optional[int] = Field(default=None, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)


# API模型
class UsageStatsCreate(TenantUsageStatsBase):
    """创建使用量统计请求模型"""
    pass 