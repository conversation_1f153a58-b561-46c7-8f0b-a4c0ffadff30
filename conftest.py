"""
全局测试配置和fixture导入

这个文件负责：
1. 全局pytest配置
2. 导入所有分离的fixtures
3. 提供全局常量和配置
"""
import pytest
import os
from typing import Generator
from fastapi.testclient import TestClient
from sqlmodel import SQLModel, create_engine, Session
from sqlalchemy import text

from app.main import app
from app.db.session import get_global_session
from app.core.config import settings

# 测试数据库配置
TEST_POSTGRES_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
TEST_DATABASE_URL = TEST_POSTGRES_URL

# 导入数据库相关fixtures
from tests.fixtures.database import test_engine, test_session

# 导入API客户端fixtures
from tests.fixtures.client import client

# 导入业务fixtures
from tests.fixtures.business.tenant import (
    sample_tenant_data, 
    sample_plan_template, 
    created_tenant
)
from tests.fixtures.business.user import (
    sample_user_data,
    admin_user_data,
    super_admin_user_data,
    created_admin_user,
    created_second_tenant_admin,
    admin_token,
    super_admin_token,
    second_tenant_admin_token
)
from tests.fixtures.business.member import (
    sample_member_data,
    created_member,
    created_member_2,
    member_token
)
from tests.fixtures.business.member_card import (
    sample_template_data,
    sample_value_template_data,
    created_template,
    created_value_template,
    sample_card_data,
    sample_value_card_data,
    created_card,
    created_value_card,
    sample_recharge_data,
    created_recharge_operation,
    created_consumption_operation
)
from tests.fixtures.business.tag import (
    sample_tag_category_data,
    sample_tag_data,
    created_tag_category,
    created_tag,
    multiple_tag_categories,
    multiple_tags,
    batch_tag_names
)
from tests.fixtures.business.teacher import (
    sample_teacher_data,
    sample_teacher_data_with_tags,
    created_teacher,
    created_teacher_with_tags,
    created_teacher_2,
    multiple_teachers,
    teacher_update_data,
    teacher_query_params,
    teacher_tag_assign_data,
    teacher_tag_batch_data,
    teacher_status_update_data
)
from tests.fixtures.business.teacher_fixed_slot import (
    sample_fixed_slot_data,
    sample_fixed_slot_data_list,
    weekly_schedule_data,
    conflict_time_data,
    created_fixed_slot,
    created_multiple_fixed_slots,
    created_weekly_schedule,
    created_teacher_2_with_slots,
    batch_create_data,
    batch_update_data,
    query_test_data
)
from tests.fixtures.business.course_config import (
    sample_course_config_data,
    minimal_course_config_data,
    invalid_course_config_data,
    created_course_config,
    default_course_config,
    course_config_update_data,
    course_config_partial_update_data,
    course_config_time_update_data,
    course_config_invalid_update_data,
    course_config_field_validation_data,
    course_config_time_validation_data,
    course_config_consistency_test_data,
    multiple_tenant_configs
)
from tests.fixtures.business.member_fixed_slot_lock import (
    sample_lock_data,
    sample_lock_data_list,
    batch_lock_data,
    status_transition_data,
    conflict_test_data,
    created_lock,
    created_multiple_locks,
    created_member_2_locks,
    query_test_data,
    available_slot_query_data
)
from tests.fixtures.business.scheduled_class import (
    sample_scheduled_class_data,
    sample_booked_class_data,
    sample_teacher_class_create_data,
    sample_member_booking_data,
    sample_admin_class_create_data,
    created_scheduled_class,
    created_booked_class,
    multiple_scheduled_classes,
    conflict_time_scenarios,
    batch_class_creation_data,
    sample_scheduled_class,
    sample_available_class,
    sample_booked_class,
    multiple_available_classes,
    multiple_booked_classes
)

# pytest配置
pytest_plugins = [
    "tests.fixtures.database",
    "tests.fixtures.client",
    "tests.fixtures.business.tenant",
    "tests.fixtures.business.user",
    "tests.fixtures.business.member",
    "tests.fixtures.business.tag",
    "tests.fixtures.business.teacher",
    "tests.fixtures.business.teacher_fixed_slot",
    "tests.fixtures.business.course_config",
    "tests.fixtures.business.member_fixed_slot_lock",
    "tests.fixtures.business.scheduled_class",
]

# 全局测试配置
def pytest_configure(config):
    """pytest配置钩子"""
    # 添加自定义标记
    config.addinivalue_line("markers", "unit: 单元测试")
    config.addinivalue_line("markers", "integration: 集成测试")
    config.addinivalue_line("markers", "e2e: 端到端测试")
    config.addinivalue_line("markers", "performance: 性能测试")
    config.addinivalue_line("markers", "slow: 慢速测试")


def pytest_collection_modifyitems(config, items):
    """修改测试收集项"""
    # 为不同目录的测试自动添加标记
    for item in items:
        if "unit/" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration/" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "e2e/" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)
        elif "performance/" in str(item.fspath):
            item.add_marker(pytest.mark.performance)


# 注意：所有fixtures已经移动到tests/fixtures/目录下的独立文件中
# 这里只保留必要的导入和配置

