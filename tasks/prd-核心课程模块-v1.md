# 核心课程模块

学生=会员

## 课节 slot 定义

课节（slot）是一个时间段，是教师和学生上课的唯一单位

- 持续时长分钟数为固定值，默认为 25 分钟，可设置为 5 的倍数
- 课节间隔：默认间隔 5 分钟，可设置为 5 的倍数
- 开始时间 hh:mm
- 结束时间 hh:mm + 固定持续时长分钟数

## 1 对 1 课相关

- 提供会员两种约课方式：直接约课（临时约课）和固定课表约课

直接约课也就是原来我把叫做临时约课的，因为概念上更倾向直接约课，是会员是直接对某个老师某天的某课节 slot 进行确定预约，该操作会直接扣费并写入该课节表里（**[已排课表]**）

而固定课表约课，只是更方便会员提前锁定未来 x 周的课，系统或者管理员会定期一次性扣费并生成对应时间周期里的这些课节，实际上最终也是生成了一批课节到课节表里（**[已排课表]**），只不过是已被会员预约的课节而已

### 直接约课（临时约课）

- 老师或者管理员可以直接打开具体某天某个时间段的课节，生成可约 slots 到**[已排课表]**，默认为空课，也就是无预定人 member_id
- 支持批量生成到 **[已排课表]**
- 会员可以直接对某个老师某天的某节课 slot，进行确定预约，该操作会直接扣费并写入该节课表里**[已排课表]**

#### 相关设置（课放到二期）

需要有一个表能设置临时约课的一些自定义项

##### 对会员做限制

- 支持预约范围：会员最多可预约 x 天的课程，默认无限制
- 预约截止时间：上课前 x 小时，默认无限制
- 取消截止时间：上课前 x 小时，默认为限制
- 预约操作限制：制会员预约和取消预约的操作时间 from 每天 xx 到 yy 点，默认无限制
- 会员预约时是否必须选教材？：默认是（仅限制会员端约临时课时）

##### 对老师的限制

是否允许老师在「老师端」自主增加课时：默认是

是否允许老师在「老师端」删除空课时：默认是

是否允许老师在「老师端」取消学生的预约？：默认否

学生预约后是否需要老师确认 ？ 默认否 低优先级需求

### 固定课表约课

1. 每个老师可以开放一个**[固定时间占位表]**，以自然周为维度，可以开放周 1-周日内，任何一个时间段的课节

   - 遵守课节 slot 规则，即默认以 25 分钟为一节课，然后间隔 5 分钟才可以排另一节课

   每个老师的固定时间位置数据对后端 api 来说表示为一个数组，或者 key 为 week1，week2.... week7 的字典，值为具体的课节时间 hh:mm

2. 会员可以对某个老师开放的**[固定时间占位表]**进行任意选择占位

3. 由管理员手动点击触发排课流程

4. 系统可以设定每个月固定某个时间点（某天的某个 hh:mm）触发排课流程，默认为 每个月 22 日 14:00

#### 固定课排课

1. 管理员会设定具体时间或者规则，触发排课，且可以设置排课周期为任意 x 周
2. 排课逻辑：按照**[固定时间占位表]**，对所有老师已经被会员选定占位的课程进行排课，排课到**[已排课表]**

#### 排课流程

- 按照教师为维度开始排课
- 优先欧美和南非教师，其次菲教
- 编号从大到小的顺序
- 筛选出该教师固定课位置被预定的所有会员
- 依次排每个会员该老师的所有课
- 排课实际就是生成具体该老师的某天的某节课到**[已排课表]**，对应某个会员

#### 排课支持详细设置

1. 开始排课的日期 年月日，但只能是每个周的周一

   也就是实际生成的**[已排课表]**是从哪天开始

2. 循环周次数，默认为 1

3. 遇到重复时间是否终止， 默认是

4. 是否跳过卡余额不足的学生，默认是

   对应排课流程里-> 依次排每个会员该老师的所有课时

   其他会员卡余额够的学生会正常排该老师的课

5. 是否移除卡余额不足学生的固定位，默认是

   遇到卡余额不足的学生，会自动移除该学生的固定位，该固定位会为空，其他学生可以选

每次排课会按照这些设置进行

#### 排课日志

排课如果出问题影响很大，所以需要考虑较完善的日志，比如

1.可查看本次排课的参数，参考：

```
{"teacherUidList":[37691,39219,25382,39544,38504,40217,40549,20650,40801,22393,41092,38226,38646,37701,38186,38258,39293,39726,40029,40880,41028,25182],"fromDate":"2025-06-30","arrangeWeeks":2,"interrupt":1,"memberNoCancel":0,"memberAction":2,"isOnline":1,"remark":""}
```

2.本次排课的详细日志

```
2025-06-23 13:06:21 为【030-Jaz】开始执行排课
2025-06-23 13:06:21 为【030-Jaz】执行排课完成
2025-06-23 13:06:21 为【031-Shinry】开始执行排课
2025-06-23 13:06:21 为【031-Shinry】执行排课完成
2025-06-23 13:06:25 为【053-Nicole Campbell 】开始执行排课
2025-06-23 13:06:25 【2025-07-02 18:00】时间冲突，结束排课
2025-06-23 13:06:25 【2025-07-02 18:30】时间冲突，结束排课
2025-06-23 13:06:25 【2025-07-02 19:00】时间冲突，结束排课
2025-06-23 13:06:25 为【053-Nicole Campbell 】执行排课完成
```

3.异常排课列表展示，包括 老师 id，老师编号-老师名字，会员 id，会员名称，课节时间

按照以下两个维度筛选切换：

有排课失败的老师

有排课失败的会员

4.其他建议？

#### 其他相关设置（二期）

##### 对所有老师的限制

是否允许老师在「老师端」自主操作固定时间占位表：

- 增加课节位置 默认是
- 删除空课节位置 默认是
- 删除已被会员占位位置 默认否

##### 对具体某个老师的性化设置

1. 支持管理员对某个老师设置固定位是否会员可见

   1. 可一键设置该老师所有固定位可见/不可见
   2. 可对该老师某个固定位设置可见/不可见

2. 支持管理员对某个老师设置是否开放固定课约课模式

### 补充

需要一个或多个操作记录表，用来提供明细，界定客服纠纷

1. 临时课预约记录，包括 预约老师，上课时间，使用会员卡，扣卡，操作时间，操作人，是否允许会员取消，备注，预约教材

   操作人可以为会员或者管理员，默认留空表示会员自己操作，否则为操作人（某管理员）id

1. 临时课取消预约记录，包括 取消预约操作时间，取消预约会员卡，老师，操作人，上课时间

   操作人可以为会员或者管理员，默认留空表示会员自己操作，否则为操作人（某管理员）id

1. 上课记录，包括 预约老师，上课时间，使用会员卡，扣除金额（元），扣除卡次，预约备注

1. 对会员固定位表操作的记录表，包括 老师，星期，时间，操作人，操作时间

1. 对老师的固定时间占位表的操作记录，包括 操作时间，操作人，操作（占位/取消占位），学生，星期，时间

   操作人可以为会员或者管理员

1. 管理员能查看会员和管理员对某个老师某个固定位的操作，比如占位（锁定），取消占位（锁定），余额不足取消占位

   （对固定位的操作需要记录到一个表里）

其中会员相关记录表里，操作人表字段，默认留空表示会员自己操作，否则为操作人（某管理员）id

## 1v1 已排课表

```
预约老师[编号-姓名] 老师id 上课时间(精确到分) 会员id	使用的会员卡 排课类型（固定|非固定）价格	预约备注 所选教材
```

也就是老师已经排好课的表，代表老师已经打开的所有可预约具体时间段的表，包含：

- 每一条记录代表某个老师，某天的某个时间段的某节课
- 包括被预定的会员（可选），是否可预定状态
- 是否对外开放
- 排课类型 （是否是固定课排课）
- 会员是否可取消

参考结构

```json
{
                "id": 20505168,
                "teacher": "007-Mike",
                "courseTime": "2025-06-28 18:30",
                "card": "【1000元送35元】全场通用充值卡",
                "type": 1,
                "bigType": 2,
                "usedCount": 56.0,
                "createTime": "2025-06-09 12:59:54",
                "operator": "M-糖糖米",
                "remark": "",
                "memberNoCancel": 0,
                "bookId": 0,
                "book": null
            },
            {
                "id": 20505200,
                "teacher": "008-Yvonne ",
                "courseTime": "2025-06-27 18:30",
                "card": "【1000元送35元】全场通用充值卡",
                "type": 1,
                "bigType": 2,
                "usedCount": 56.0,
                "createTime": "2025-06-09 13:00:06",
                "operator": "M-糖糖米",
                "remark": "",
                "memberNoCancel": 0,
                "bookId": 0,
                "book": null
            },
```
