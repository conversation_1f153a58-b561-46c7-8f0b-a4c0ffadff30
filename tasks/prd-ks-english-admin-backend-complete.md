# KS English Admin Backend - 完整产品需求文档 (PRD)

## 1. 项目概述

### 1.1 产品定位

KS English Admin Backend 是一个基于 **FastAPI + SQLModel + PostgreSQL** 的 SaaS 多租户在线网约课系统后端，为外教课机构提供完整的管理解决方案。

### 1.2 核心价值主张

- **SaaS 模式**：支持多个教育培训机构独立使用，快速部署
- **智能排课**：自动化固定课表排课，减少人工调度成本
- **多端支持**：同时服务租户 CMS 管理后台和会员 H5/小程序前端
- **数据安全**：多租户架构确保机构间数据完全隔离

### 1.3 目标用户

- **主要用户**：中小型外教课机构（租户）
- **最终用户**：机构管理员、代理人员、销售人员、学员（会员）、教师

## 2. 项目目标

### 2.1 业务目标

- 支持 2000+ 并发用户和 2000+ 租户机构
- 实现学员自主预约课程，提升运营效率
- 通过智能排课系统减少 80% 人工调度成本
- 为机构提供完整的会员管理教师管理和财务管理解决方案

### 2.2 技术目标

- 构建高性能、可扩展的多租户架构
- 实现完整的 API 文档和测试覆盖
- 确保数据安全和系统稳定性
- 支持快速迭代和功能扩展

## 3. 用户故事

### 3.1 租户管理员

- 作为机构管理员，我希望能够管理机构的所有用户、学员（会员）、教师和课程，以便统一运营管理
- 作为机构管理员，我希望能够设置自动排课规则，以便减少人工调度工作量
- 作为机构管理员，我希望能够查看详细的数据统计报表，以便了解机构运营状况

### 3.2 教师

- 作为教师，我希望能够设置我的固定时间占位表，以便学员可以预约我的固定课程
- 作为教师，我希望能够开放临时课程时间段，以便增加授课机会
- 作为教师，我希望能够查看我的课程安排和学员名单，以便做好教学准备

### 3.3 学员（会员）

- 作为学员，我希望能够查看教师的可约时间并预约课程，以便安排我的学习计划
- 作为学员，我希望能够锁定教师的固定课程位置，以便保证长期稳定的学习时间
- 作为学员，我希望能够查看我的课程记录和会员卡余额，以便管理我的学习进度

### 3.4 代理/销售人员

- 作为代理人员，我希望能够生成邀请码，邀请别人扫码注册会员，归属我名下，可以查看邀请的会员消费充值上课信息（简单的信息列表即可），以便跟踪客户发展情况，和知晓我的代理收入
- 作为销售人员，我希望能够生成邀请码，邀请别人扫码注册会员，归属我名下，简单查看我邀请的人数，上课数，好知晓我能获得的次卡奖励（奖励到我自己绑定到的会员名下）

补充说明：
代理人员是用来发展大量学员的，学员表里会标记对应代理人 id 和 name，会根据发展学员的上课情况给结算工资
销售人员只是少量推荐别的会员来上课，会给销售人员绑定的会员账号奖励次卡（也就是说实际中的销售人员通常来自会员）

## 4. 功能需求

### 4.1 迭代规划概述

#### 第一期：核心基础功能（已完成 + 教师课程管理）

- ✅ 多租户基础架构
- ✅ 用户认证和授权系统
- ✅ 租户管理（CRUD）
- ✅ 用户管理（CRUD）
- ✅ 会员管理（CRUD）
- ✅ 标签管理系统
- ✅ 教师管理模块
- ✅ 教师固定时间占位表
- ✅ 课程系统配置模块
- ✅ 会员固定课位锁定模块
- ✅ 已排课表模块（基础课程管理）
- 🚧 会员卡管理系统

#### 第二期：智能排课和预约系统

- 🚧 固定课表自动排课算法
- 🚧 排课任务管理系统
- 🚧 排课日志系统
- 🚧 定时排课调度器
- 🚧 高级数据统计和报表

#### 第三期：完善功能和运营支持

- 🚧 支付集成（微信支付）
- 🚧 消息通知系统（微信公众号、短信）
- 🚧 文件上传管理
- 🚧 高级数据统计报表
- 🚧 系统监控和日志

## 5. 第一期功能需求详述

### 5.1 教师管理模块

#### 5.1.1 教师基础信息管理

**需求编号**: REQ-T001
**描述**: 系统必须支持教师基础信息的完整管理
**功能要求**:

1. 教师信息 CRUD 操作（创建、读取、更新、删除）
2. 教师信息包含：头像、姓名、性别、单节价格、手机号、邮箱、角色、教师分类、标签、微信绑定状态、会员端展示状态
3. 支持教师标签管理，可多标签分类：需要有一个标签库，教师标签选自标签库，标签库可增删标签，标签可以有类别，比如 教材类，教学风格类
4. 支持按多维度筛选教师：日程、预定量、空位、教材、标签、时间段、区域（欧美/菲/南非）、单节价格

#### 5.1.2 教师固定时间占位表

**需求编号**: REQ-T002
**描述**: 教师可以设置固定的可约时间表，供会员预约固定课程
**功能要求**:

1. **时间段设置**：

   - 教师可设置周一到周日的固定时间占位表
   - 时间段采用 TIME 类型，格式为 HH:MM（如 18:00、21:30）
   - 课节时长可配置，默认 25 分钟，支持 5 的倍数设置
   - 课节间隔可配置，默认 5 分钟

2. **时间段管理**：

   - 每个教师每天可开放任意数量的时间段
   - 支持批量设置和单独调整时间段
   - 时间段状态管理：开放/关闭、对会员可见/不可见
   - 支持个性化课节时长设置（预留功能）

3. **数据完整性**：
   - 同一教师同一时间段唯一性约束
   - 支持时间段的创建、更新、删除操作
   - 删除时间段需考虑已有会员锁定的处理

#### 5.1.3 会员固定课位锁定管理

**需求编号**: REQ-T003
**描述**: 会员可以锁定教师的固定时间段，用于自动排课
**功能要求**:

1. **锁定机制**：

   - 会员可锁定教师开放的固定时间段
   - 基于 teacher_fixed_slots 表的直接关联设计
   - 同一时间段只能被一个会员锁定
   - 锁定状态：激活/暂停/取消

2. **数据关联**：

   - 通过 teacher_fixed_slot_id 直接关联教师时间段
   - 保留 teacher_id、weekday、start_time 作为冗余字段便于查询
   - 确保数据一致性和查询性能的平衡

3. **业务规则**：
   - 会员锁定前需检查教师时间段可用性
   - 支持会员主动取消锁定（删除记录）
   - 支持管理员/系统强制取消锁定（更新状态）
   - 锁定操作需记录完整的操作日志

### 5.2 课程管理系统

#### 5.2.1 已排课表管理

**需求编号**: REQ-C001
**描述**: 管理所有具体的课程安排，支持固定课和临时课
**功能要求**:

1. **课程基础信息**：

   - 课程时间精确到分钟：TIMESTAMP 格式 YYYY-MM-DD HH:MM
   - 课程时长：默认 25 分钟，支持配置
   - 课程类型：fixed（固定课）/temporary（临时课）
   - 课程价格：存储教师单价（整数，单位：元）

2. **课程状态管理**：

   - available：可预约（空课）
   - booked：已预约
   - completed：已完成
   - teacher_no_show：教师缺席
   - member_no_show：会员缺席
   - 注意：无 cancelled 状态，取消后变回 available

3. **预约信息**：
   - 会员卡信息：存储 member_card_id 和 member_card_name
   - 预约备注：booking_remark
   - 教材信息：material_name（第一期使用字符串）
   - 可见性控制：is_visible_to_member

#### 5.2.2 临时课程管理

**需求编号**: REQ-C002
**描述**: 教师和管理员可以开放临时课程供会员预约
**功能要求**:

1. **临时课程创建**：

   - 教师/管理员可开放任意时间的临时课程
   - 直接创建到 scheduled_classes 表，状态为 available
   - 支持批量创建和单独创建
   - 可设置会员不可见（管理员专用）

2. **冲突检测**：
   - 创建前检测教师时间冲突
   - 检测教师固定时间段开放状态
   - 避免重复时间段的课程创建

#### 5.2.3 系统配置管理

**需求编号**: REQ-C003
**描述**: 提供灵活的课程系统配置管理
**功能要求**:

1. **基础配置**：

   - 默认课节时长和间隔时间
   - 预约限制：提前天数、截止时间、操作时间窗口
   - 教师权限：自主增删课时、取消预约、确认机制

2. **排课配置**：
   - 自动排课开关和触发时间
   - 排课周数和冲突处理策略
   - 余额不足处理策略

### 5.3 会员卡管理系统

#### 5.3.1 会员卡模板管理

**需求编号**: REQ-M001
**描述**: 管理员可以创建和管理各种类型的会员卡模板
**功能要求**:

1. **卡片类型**：

   - 次卡-有期限：按次数计费，设置有效期
   - 次卡-无期限：按次数计费，永久有效
   - 储值卡-有期限：按金额计费，设置有效期
   - 储值卡-无期限：按金额计费，永久有效

2. **模板配置**：

   - 卡片名称、售卖价格、可用余额
   - 有效期设置（天数）
   - 续费配置：是否支持重复购买
   - 储值卡支持：多档续费奖励设置：续费价格、奖励金额、充值天数

3. **模板管理**：
   - 模板 CRUD 操作
   - 模板状态管理（激活/停用）
   - 模板使用统计

#### 5.3.2 会员卡实例管理

**需求编号**: REQ-M002
**描述**: 管理会员持有的具体会员卡实例
**功能要求**:

1. **卡片创建**：

   - 新会员默认创建余额为 0 的 30 天储值卡
   - 每个会员有且只有一个储值卡
   - 每个会员可以有多个次卡
   - 基于模板创建会员卡实例

2. **卡片信息**：

   - 卡号（唯一标识）
   - 当前余额、总充值金额、总消费金额
   - 卡片状态：激活/冻结/过期/注销
   - 过期时间（有期限卡片）

3. **卡片管理**：
   - 卡片状态变更
   - 卡片信息查询和统计
   - 卡片使用记录追踪

#### 5.3.3 充值管理

**需求编号**: REQ-M003
**描述**: 支持会员卡的充值操作和记录管理
**功能要求**:

1. **充值操作**：

   - 支持管理员为会员充值
   - 充值金额验证和限制
   - 充值后余额实时更新
   - 支持充值奖励机制

2. **充值记录**：
   - 记录充值金额、支付方式、操作人
   - 记录奖励金额和充值天数
   - 充值状态跟踪
   - 充值凭证和备注

#### 5.3.4 扣费和消费管理

扣费和消费记录表可以和充值表共用一个MemberCardOperation

**需求编号**: REQ-M004
**描述**: 课程预约时的自动扣费和消费记录管理
**功能要求**:

1. **扣费机制**：

   - 课程预约时自动验证余额
   - 余额充足时立即扣费
   - 扣费失败时阻止预约
   - 支持次卡和储值卡不同扣费逻辑

2. **消费记录**：

   - 记录每次消费的课程、金额、会员卡
   - 消费类型：课程扣费、退费、调整
   - 消费状态和描述信息
   - 与课程记录的关联

3. **余额管理**：
   - 实时余额计算和更新
   - 余额不足提醒
   - 余额变动历史记录
   - 余额异常监控

## 6. 第二期功能需求详述

### 6.1 固定课表自动排课系统

#### 6.1.1 自动排课触发机制

**需求编号**: REQ-S001
**描述**: 系统按照预设规则自动执行固定课程排课
**功能要求**:

1. 默认触发时间：每月 22 日下午 14:00 自动执行
2. 支持手动触发排课任务
3. 排课任务执行日志记录
4. 排课失败重试机制

#### 6.1.2 排课算法规则

**需求编号**: REQ-S002
**描述**: 按照业务规则执行智能排课
**功能要求**:

1. **排课维度和优先级**：

   - 以教师为主维度进行排课
   - 教师优先级：欧美和南非教师优先，其次菲教
   - 教师排序：按编号从大到小排序

2. **排课流程**：

   - 按会员分组处理，计算总费用
   - 检查会员卡余额是否充足
   - 生成课程记录的同时立即扣费
   - 余额不足时根据配置跳过或终止

3. **冲突检测**：

   - 教师时间冲突检测
   - 会员时间冲突检测
   - 教师固定位开放状态检测

4. **排课周期**：
   - 默认生成未来 4 周的课程安排
   - 支持自定义排课周数
   - 从指定周一开始排课

#### 6.1.3 排课任务管理

**需求编号**: REQ-S003
**描述**: 完整的排课任务执行和监控体系
**功能要求**:

1. **任务记录**：

   - 记录每次排课任务的参数和结果
   - 支持手动和自动排课任务类型
   - 任务状态：pending/running/completed/failed

2. **详细日志**：

   - 记录排课过程中的所有操作
   - 包含 INFO、WARN、ERROR 级别日志
   - 记录异常情况和错误详情

3. **结果统计**：
   - 统计参与排课的教师数量
   - 统计成功/失败的教师数量
   - 统计生成的课程总数

### 6.2 操作记录和审计系统

#### 6.2.1 课程操作记录

**需求编号**: REQ-A001
**描述**: 记录所有课程相关的操作，支持纠纷处理和数据分析
**功能要求**:

1. **操作类型覆盖**：

   - create_slot：创建课时（教师/管理员开放）
   - book：预约
   - cancel：取消预约
   - reschedule：改期
   - complete：完成
   - no_show_member：会员缺席
   - no_show_teacher：教师缺席

2. **记录内容**：

   - 操作前后状态变更
   - 课程基本信息（时间、类型、价格）
   - 会员卡扣费信息（金额、次数）
   - 教材信息
   - 操作原因和备注

3. **操作人追踪**：
   - 操作人 ID 和显示名称
   - 操作人类型：member/teacher/admin
   - 操作时间精确到秒

#### 6.2.2 固定位操作记录

**需求编号**: REQ-A002
**描述**: 分别记录教师固定时间占位和会员锁定的操作历史
**功能要求**:

1. **教师固定时间占位操作记录**：

   - create/update/enable/disable/delete/visibility_change
   - 记录可用性和可见性状态变更
   - 操作人类型：teacher/admin

2. **会员固定位锁定操作记录**：

   - lock/unlock/pause/resume/admin_remove/system_remove/batch_remove
   - 记录锁定状态变更和会员余额信息
   - 操作人类型：member/admin/system

3. **关联关系记录**：
   - 记录 teacher_fixed_slot_id 确保数据关联
   - 保留时间信息作为冗余字段便于查询

#### 6.2.3 数据归档策略

**需求编号**: REQ-A003
**描述**: 操作记录的长期保存和归档管理
**功能要求**:

1. **保留策略**：

   - 主表保留 2-3 年的操作记录
   - 超期数据自动归档到历史表
   - 支持按需查询历史归档数据

2. **归档机制**：
   - 定期执行归档任务
   - 三张操作记录表分别归档
   - 归档后删除主表中的历史数据

## 7. 数据库设计要求

### 7.1 多租户架构

- 采用共享数据库+租户 ID 隔离模式
- 所有业务表包含 tenant_id 字段
- 使用 PostgreSQL 行级安全策略(RLS)确保数据隔离

### 7.2 核心数据表关系

```
租户(tenants) 1:N 用户(users)
租户(tenants) 1:N 会员(members)
租户(tenants) 1:N 教师(teachers)
教师(teachers) 1:N 固定时间占位表(teacher_fixed_slots)
教师(teachers) 1:N 已排课表(scheduled_classes)
会员(members) 1:N 已排课表(scheduled_classes)
会员(members) 1:N 会员卡(member_cards)
会员(members) 1:N 消费记录(consumption_records)
固定时间占位表(teacher_fixed_slots) 1:1 固定课位锁定(member_fixed_slot_locks)
```

**重要设计决策**：

- member_fixed_slot_locks 通过 teacher_fixed_slot_id 直接关联 teacher_fixed_slots
- 保留 teacher_id、weekday、start_time 作为冗余字段便于查询
- 确保数据一致性和查询性能的平衡

### 7.3 关键表结构设计

#### 7.3.1 系统配置表(course_system_configs)

- 课程系统的所有配置参数，支持配置驱动的业务逻辑
- 包含课节时长、预约限制、教师权限、排课规则等配置

#### 7.3.2 教师固定时间占位表(teacher_fixed_slots)

- id, tenant_id, teacher_id, weekday(1-7), start_time(TIME 类型)
- duration_minutes, is_available, is_visible_to_members
- 时间格式采用 TIME 类型，便于查询和显示

#### 7.3.3 会员固定课位锁定表(member_fixed_slot_locks)

- id, tenant_id, member_id, teacher_fixed_slot_id(外键)
- teacher_id, weekday, start_time(冗余字段)
- status(active/paused/cancelled), locked_at
- 通过 teacher_fixed_slot_id 确保数据一致性

#### 7.3.4 已排课表(scheduled_classes)

- id, tenant_id, teacher_id, member_id, class_datetime(TIMESTAMP)
- class_type(fixed/temporary), price(INTEGER), status
- member_card_id, member_card_name, booking_remark, material_name
- is_visible_to_member, operator_name
- 状态：available/booked/completed/teacher_no_show/member_no_show

#### 7.3.5 排课任务管理(schedule_tasks & schedule_logs)

- 完整的排课任务记录和详细日志系统
- 支持手动和自动排课任务类型
- 记录排课参数、执行状态、结果统计

#### 7.3.6 操作记录表(分离设计)

- scheduled_class_operation_logs：课程操作记录
- teacher_fixed_slot_operation_logs：教师时间段操作记录
- member_fixed_lock_operation_logs：会员锁定操作记录
- 完整的操作审计，支持纠纷处理和数据分析

### 7.4 设计原则

#### 7.4.1 时间字段设计

- 时间段使用 TIME 类型：便于查询和显示
- 课程时间使用 TIMESTAMP 类型：精确到分钟
- 价格使用 INTEGER 类型：避免浮点数精度问题

#### 7.4.2 关联关系设计

- 直接外键关联 + 冗余字段：平衡数据一致性和查询性能
- 检查约束：确保冗余字段与关联表一致
- 唯一约束：防止业务逻辑冲突

#### 7.4.3 状态管理设计

- 明确的状态定义和流转规则
- 无 cancelled 状态：取消后变回 available
- 分离的 no_show 状态：teacher_no_show/member_no_show

#### 7.4.4 操作记录设计

- 分离设计：不同业务操作使用独立的记录表
- 完整追踪：操作人、时间、原因、状态变更
- 归档策略：2-3 年数据保留，支持历史数据归档

## 8. 非功能需求

### 8.1 性能要求

- 支持 2000 并发用户访问
- API 响应时间容忍度较高，优先开发效率
- 数据库查询优化，关键接口响应时间<2 秒

### 8.2 安全要求

- JWT token 认证机制
- 密码 bcrypt 加密存储
- 多租户数据完全隔离
- API 访问日志记录

### 8.3 可扩展性要求

- 模块化架构设计，支持功能独立扩展
- 数据库设计支持水平扩展
- API 设计遵循 RESTful 规范

## 9. 技术约束

### 9.1 技术栈

- 后端框架：FastAPI
- 数据模型：SQLModel
- 数据库：PostgreSQL 12+
- 认证：JWT
- 文档：OpenAPI/Swagger 自动生成

### 9.2 开发环境

- Python 3.9+
- 开发工具：支持热重载
- 测试：pytest + 覆盖率报告
- 代码质量：类型检查 + 代码规范

## 10. 成功指标

### 10.1 功能指标

- 第一期：完成教师管理和基础课程管理模块
- 第二期：实现自动排课功能，排课成功率>95%
- 第三期：完整功能上线，支持完整业务流程

### 10.2 技术指标

- API 文档覆盖率 100%
- 单元测试覆盖率>80%
- 系统可用性>99%
- 数据一致性 100%

## 11. 第三期功能需求详述

### 11.1 支付集成系统

#### 11.1.1 微信支付集成

**需求编号**: REQ-P001
**描述**: 集成微信支付支持会员在线充值
**功能要求**:

1. 支持微信支付 API 集成
2. 充值金额实时到账会员卡
3. 支付状态跟踪和异常处理
4. 支付记录和财务对账

#### 11.1.2 管理员手动充值

**需求编号**: REQ-P002
**描述**: 管理员可为会员手动充值
**功能要求**:

1. 管理员可选择会员进行充值操作
2. 支持充值金额和赠送金额设置
3. 充值原因和备注记录
4. 充值操作审计日志

### 11.2 消息通知系统

#### 11.2.1 微信公众号通知

**需求编号**: REQ-N001
**描述**: 通过微信公众号发送课程相关通知
**功能要求**:

1. 预约成功提醒
2. 上课前提醒（可配置提前时间）
3. 取消课程通知
4. 会员卡余额不足提醒

#### 11.2.2 短信通知

**需求编号**: REQ-N002
**描述**: 重要通知通过短信发送
**功能要求**:

1. 课程预约确认短信
2. 课程取消通知短信
3. 紧急通知短信
4. 短信发送状态跟踪

### 11.3 文件上传管理

#### 11.3.1 文件上传服务

**需求编号**: REQ-F001
**描述**: 提供安全的文件上传和管理服务
**功能要求**:

1. 支持教师头像上传
2. 支持会员头像上传
3. 支持教材文件上传
4. 文件类型和大小限制
5. 文件安全扫描和存储

### 11.4 高级数据统计报表

#### 11.4.1 收入统计报表

**需求编号**: REQ-R001
**描述**: 提供详细的收入统计分析
**功能要求**:

1. 收入概览：今日、本周、本月、上月收入对比
2. 收入来源分析：充值收入、课程消费收入
3. 教师收入统计：按教师维度的收入分析
4. 收入趋势图表展示

#### 11.4.2 课程统计报表

**需求编号**: REQ-R002
**描述**: 提供课程相关的统计分析
**功能要求**:

1. 上课情况概览：上课会员数、排课数统计
2. 课程出勤率统计：按时间维度的出勤分析
3. 教师课时统计：教师工作量和效率分析
4. 课程取消率分析

#### 11.4.3 会员消费统计

**需求编号**: REQ-R003
**描述**: 提供会员消费行为分析
**功能要求**:

1. 会员消费概览：活跃会员数、消费金额统计
2. 会员消费排行：高价值会员识别
3. 会员留存分析：新增、活跃、流失会员统计
4. 会员卡使用情况分析

#### 11.4.4 报表导出功能

**需求编号**: REQ-R004
**描述**: 支持报表数据导出
**功能要求**:

1. 支持 Excel 格式导出
2. 支持 PDF 格式导出
3. 支持自定义时间范围导出
4. 导出权限控制

## 12. 接口设计规范

### 12.1 API 设计原则

- 遵循 RESTful API 设计规范
- 统一的响应格式：BaseResponse、DataResponse、PageResponse、ErrorResponse
- 完整的错误码体系和错误处理
- OpenAPI/Swagger 自动文档生成

### 12.2 认证和授权

- JWT Token 认证机制
- 基于角色的权限控制(RBAC)
- 租户级别的数据隔离
- API 访问频率限制

### 12.3 数据验证

- 使用 Pydantic 模型进行请求数据验证
- 统一的数据格式和类型检查
- 自定义验证规则支持
- 详细的验证错误信息

## 13. 部署和运维要求

### 13.1 部署环境

- 支持 Docker 容器化部署
- 支持云服务器部署
- 数据库连接池管理
- 环境配置管理

### 13.2 监控和日志

- 应用性能监控(APM)
- 数据库性能监控
- 错误日志收集和分析
- 业务操作审计日志

### 13.3 备份和恢复

- 数据库定期备份
- 备份数据验证
- 灾难恢复预案
- 数据迁移方案

## 14. 风险评估

### 14.1 技术风险

- **数据库性能风险**: 随着租户和数据量增长，查询性能可能下降
  - 缓解措施：索引优化、查询优化、读写分离
- **并发处理风险**: 高并发场景下的数据一致性问题
  - 缓解措施：数据库锁机制、事务管理、异步处理

### 14.2 业务风险

- **排课冲突风险**: 自动排课可能产生时间冲突
  - 缓解措施：排课算法优化、冲突检测机制、手动调整功能
- **支付安全风险**: 支付过程中的安全问题
  - 缓解措施：支付加密、订单验证、异常监控

## 15. 迭代计划

### 15.1 第一期开发计划（6-8 周）

**目标**: 完成教师管理、基础课程管理和会员卡系统

- 周 1-2：标签管理和教师管理模块开发
- 周 3-4：固定时间占位表和会员锁定功能开发
- 周 5-6：基础课程管理和已排课表开发
- 周 7：会员卡管理系统开发
- 周 8：第一期集成测试和交付准备

### 15.2 第二期开发计划（6-8 周）

**目标**: 实现智能排课和自动化系统

- 周 1-2：自动排课算法开发
- 周 3-4：排课任务管理和日志系统开发
- 周 5-6：定时调度器和高级功能开发
- 周 7-8：系统集成测试和性能优化

### 15.3 第三期开发计划（4-6 周）

**目标**: 完善支付、通知和统计功能

- 周 1-2：支付集成和文件上传
- 周 3-4：消息通知系统
- 周 5-6：数据统计报表和系统优化

## 16. 验收标准

### 16.1 功能验收标准

- 所有功能需求 100%实现
- 核心业务流程端到端测试通过
- 用户角色权限验证通过
- 数据一致性验证通过

### 16.2 性能验收标准

- 支持 2000 并发用户访问
- 核心 API 响应时间<2 秒
- 数据库查询性能满足要求
- 系统稳定性测试通过

### 16.3 安全验收标准

- 多租户数据隔离验证通过
- 用户认证和授权测试通过
- 数据加密和传输安全验证
- 安全漏洞扫描通过

## 17. 待确认问题

1. 教师端 H5 的具体交互流程需要详细设计
2. 消息通知的具体触发场景和模板需要确定
3. 文件上传的存储方案（本地存储/云存储）需要确定
4. 数据统计报表的具体展示样式和交互需要 UI 设计支持

---

**文档版本**: v1.0
**创建日期**: 2025-06-27
**最后更新**: 2025-06-27
**负责人**: 产品团队
**审核人**: 技术团队
