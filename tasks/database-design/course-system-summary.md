# 核心课程模块 - 设计总结

## 🎯 设计决策总结

### 1. 时间格式选择 ⏰
**决策**: 使用 `TIME` 类型
**理由**: 查询友好、显示直观、类型安全
```sql
start_time TIME NOT NULL, -- "18:00", "21:30"
```

### 2. 课节时长配置 📏
**决策**: 全局统一，预留个性化支持
- 系统配置表中设置默认值
- 教师固定位表中可覆盖默认值（NULL时使用默认）
```sql
-- 系统配置
default_slot_duration_minutes INTEGER DEFAULT 25,

-- 教师固定位
duration_minutes INTEGER, -- NULL时使用系统默认值
```

### 3. 价格字段类型 💰
**决策**: 使用整数类型
**理由**: 最小单位1元，避免浮点数精度问题
```sql
price INTEGER, -- 教师单价（整数，单位：元）
```

### 4. 排课扣费时机 💳
**决策**: 立即扣费
**流程**: 
1. 按会员分组，按教师计算总费用
2. 检查余额是否充足
3. 生成课程记录的同时扣费
4. 余额不足时根据配置跳过或终止

### 5. 冲突检测范围 🔍
**检测项目**:
- ✅ 教师时间冲突
- ✅ 会员时间冲突  
- ✅ 教师固定位开放状态

### 6. 教材管理方案 📚
**第一期**: 完全使用字符串字段
**第二期**: 预留 `material_id` 用于关联教材表
```sql
material_id INTEGER, -- 预留教材ID（第二期使用）
material_name VARCHAR(100), -- 教材名称（第一期主要使用）
```

### 7. 操作记录保留策略 📝
**决策**: 保留2-3年，支持归档
**实现**:
- 主表保留近期记录（2年内）
- 归档表保存历史记录
- 定期归档脚本

### 8. 表关联关系优化 🔗
**决策**: member_fixed_slot_locks 直接关联 teacher_fixed_slots
**优势**:
- 数据一致性：外键约束确保数据完整性
- 查询简化：避免多字段关联查询
- 业务逻辑清晰：明确依赖关系
- 冗余字段：保留便于查询的字段

## 🏗️ 核心表结构

### 主要业务表
1. **course_system_configs** - 系统配置表
2. **teacher_fixed_slots** - 教师固定时间占位表
3. **member_fixed_slot_locks** - 会员固定课位锁定表
4. **scheduled_classes** - 已排课表（核心）
5. **schedule_tasks** - 排课任务记录表
6. **schedule_logs** - 排课详细日志表

### 操作记录表
1. **scheduled_class_operation_logs** - 已排课表操作记录
2. **teacher_fixed_slot_operation_logs** - 教师固定时间占位表操作记录
3. **member_fixed_lock_operation_logs** - 会员固定位锁定表操作记录

## 🔄 核心业务流程

### 直接约课流程
1. 教师/管理员开放具体时间段 → `scheduled_classes` (status='available')
2. 会员预约 → 更新状态为 'booked'，扣费，记录操作日志
3. 课程完成/缺席 → 更新最终状态

### 固定课表约课流程
1. 教师开放固定时间位 → `teacher_fixed_slots`
2. 会员锁定时间位 → `member_fixed_slot_locks`
3. 系统/管理员触发排课 → 批量生成 `scheduled_classes`，批量扣费
4. 记录排课任务和详细日志

### 排课算法优先级
1. **教师优先级**: 欧美/南非 > 菲教
2. **教师排序**: 编号从大到小
3. **会员处理**: 按会员分组，检查余额，批量处理

## 📊 状态管理

### scheduled_classes 状态
- `available`: 可预约（空课）
- `booked`: 已预约
- `completed`: 已完成
- `teacher_no_show`: 教师缺席
- `member_no_show`: 会员缺席
- **注意**: 无 `cancelled` 状态，取消后变回 `available`，删除对应一个独立软删除字段

### member_fixed_slot_locks 状态
- `active`: 激活锁定，参与排课
- `paused`: 暂停锁定，跳过排课
- `cancelled`: 取消锁定

## 🔧 配置驱动设计

### 系统级配置
- 课节时长、间隔时间
- 预约限制（提前天数、截止时间）
- 教师权限控制
- 排课规则配置

### 排课配置
- 默认排课周数
- 冲突处理策略
- 余额不足处理策略
- 自动排课时间设置

## 📈 性能优化

### 关键索引
```sql
-- 支持会员端筛选查询
CREATE INDEX idx_scheduled_classes_visible_time 
ON scheduled_classes(is_visible_to_member, status, class_datetime);

-- 支持排课冲突检测
CREATE INDEX idx_scheduled_classes_conflict 
ON scheduled_classes(teacher_id, member_id, class_datetime, status);

-- 支持固定位查询
CREATE INDEX idx_teacher_fixed_slots_available 
ON teacher_fixed_slots(teacher_id, is_available, is_visible_to_members);
```

### 查询优化
- 按租户分区查询
- 时间范围限制
- 状态过滤优先

## 🚀 扩展性考虑

### 第二期功能预留
- 教材表关联 (`material_id`)
- 个性化课节时长 (`duration_minutes`)
- 更复杂的权限控制
- 更多的排课策略

### 数据归档
- 三张操作记录表分别归档
- 历史课程数据归档
- 保持主表性能
- 支持2-3年数据保留策略

## ✅ 设计验证

### 功能覆盖
- ✅ 直接约课（临时约课）
- ✅ 固定课表约课
- ✅ 自动排课系统
- ✅ 完整的操作记录（分离设计）
- ✅ 灵活的配置管理
- ✅ 冲突检测和处理
- ✅ 扣费和余额管理

### 性能考虑
- ✅ 合理的索引设计
- ✅ 分离设计避免复杂查询
- ✅ 归档策略保持性能

### 可维护性
- ✅ 清晰的表结构和关系
- ✅ 完整的操作审计
- ✅ 配置驱动的灵活性
- ✅ 详细的排课日志

这个设计能够满足你的所有需求，同时保持了良好的扩展性和可维护性！
