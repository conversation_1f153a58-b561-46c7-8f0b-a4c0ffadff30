# 核心关系图 (Core Relationships)

## 概述
展示最重要的6个业务实体和它们之间的核心关系，用于快速理解整体数据库架构。

## eraser.io 代码

```
// === 核心实体关系图 ===
// 只展示主键、外键和核心业务字段

tenants [icon: building] {
  id int pk
  name string
  code string unique
  status enum
  created_at timestamp
}

users [icon: user] {
  id int pk
  tenant_id int
  username string unique
  role enum
  status enum
  created_by int
}

members [icon: users] {
  id int pk
  tenant_id int
  name string
  phone string
  member_status enum
  sales_id int
  agent_id int
  created_by int
}

teachers [icon: graduation-cap] {
  id int pk
  tenant_id int
  name string
  teacher_category enum
  price_per_class int
  status enum
  created_by int
}

scheduled_classes [icon: clock] {
  id int pk
  tenant_id int
  teacher_id int
  member_id int
  class_datetime timestamp
  class_type enum
  price int
  member_card_id int
  status enum
  is_visible_to_member boolean
}

member_cards [icon: wallet] {
  id int pk
  tenant_id int
  member_id int
  card_type enum
  balance decimal
  status enum
}

// === 核心关系 ===
users.tenant_id > tenants.id
users.created_by > users.id
members.tenant_id > tenants.id
members.sales_id > users.id
members.agent_id > users.id
members.created_by > users.id
teachers.tenant_id > tenants.id
teachers.created_by > users.id
scheduled_classes.tenant_id > tenants.id
scheduled_classes.teacher_id > teachers.id
scheduled_classes.member_id > members.id
scheduled_classes.member_card_id > member_cards.id
member_cards.tenant_id > tenants.id
member_cards.member_id > members.id
```

## 关系说明

### 租户关系
- 所有业务实体都通过 `tenant_id` 关联到租户
- 实现多租户数据隔离

### 用户关系
- `users.created_by` 自引用，记录创建者
- `members.sales_id/agent_id` 关联到用户，建立归属关系

### 业务关系
- `scheduled_classes` 连接教师和会员，是核心业务实体
- `member_cards` 管理会员的财务账户

### 审计关系
- 重要实体都有 `created_by` 字段，追踪操作者

## 使用说明
1. 复制上述代码到 eraser.io
2. 自动生成可视化关系图
3. 用于项目介绍和架构讨论
