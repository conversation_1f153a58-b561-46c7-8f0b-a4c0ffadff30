# 教师管理模块 (Teacher Management)

## 概述
教师信息管理、标签系统、分类管理模块。

## eraser.io 代码

```
// === 教师管理模块 ===

teachers [icon: graduation-cap] {
  id int pk
  tenant_id int
  name string
  teacher_category enum
  region enum
  price_per_class int
  status enum
  created_by int
}

tag_categories [icon: folder] {
  id int pk
  tenant_id int
  name string
  created_by int
}

tags [icon: tag] {
  id int pk
  tenant_id int
  category_id int
  name string
  status enum
  created_by int
}

teacher_tags [icon: link] {
  id int pk
  teacher_id int
  tag_id int
  created_by int
}

// === 关系 ===
teachers.tenant_id > tenants.id
teachers.created_by > users.id
tag_categories.tenant_id > tenants.id
tag_categories.created_by > users.id
tags.tenant_id > tenants.id
tags.category_id > tag_categories.id
tags.created_by > users.id
teacher_tags.teacher_id > teachers.id
teacher_tags.tag_id > tags.id
teacher_tags.created_by > users.id
```

## 字段说明

### teachers 表
- **id**: 主键
- **tenant_id**: 租户ID
- **name**: 教师姓名
- **teacher_category**: 教师分类
  - `european`: 欧美教师
  - `south_african`: 南非教师
  - `filipino`: 菲律宾教师
  - `chinese`: 中教
  - `other`: 其他
- **region**: 教师区域
  - `europe`: 欧洲
  - `north_america`: 北美
  - `south_africa`: 南非
  - `philippines`: 菲律宾
  - `china`: 中国
  - `other`: 其他
- **price_per_class**: 单节课价格（整数，单位：元）
- **status**: 教师状态
  - `pending`: 待审核
  - `active`: 激活
  - `inactive`: 停用
  - `on_leave`: 请假
- **created_by**: 创建者ID

### tag_categories 表
- **id**: 主键
- **tenant_id**: 租户ID
- **name**: 分类名称（如：教材类、教学风格类）
- **created_by**: 创建者ID

### tags 表
- **id**: 主键
- **tenant_id**: 租户ID
- **category_id**: 标签分类ID
- **name**: 标签名称
- **status**: 标签状态
  - `active`: 激活
  - `inactive`: 停用
- **created_by**: 创建者ID

### teacher_tags 表
- **id**: 主键
- **teacher_id**: 教师ID
- **tag_id**: 标签ID
- **created_by**: 创建者ID

## 业务规则

### 教师管理
- 教师按分类和区域管理
- 支持多维度筛选（分类、区域、价格、标签）
- 排课优先级：欧美/南非 > 菲律宾

### 标签系统
- 标签分类管理（教材类、教学风格类等）
- 教师可关联多个标签
- 标签支持启用/停用

### 价格管理
- 单节课价格以整数存储（单位：元）
- 支持按价格范围筛选

## 扩展字段（完整版本包含）
- 基础信息：gender, avatar, phone, email
- 微信信息：wechat_bound, wechat_openid, wechat_unionid
- 显示设置：show_to_members
- 教学信息：introduction, teaching_experience, specialties, certifications
- 排课设置：priority_level
- 备注：notes

## 索引设计
- `teachers(tenant_id, id)` - 租户教师查询
- `teachers(tenant_id, status)` - 状态筛选
- `teachers(tenant_id, teacher_category, region)` - 分类区域筛选
- `teachers(tenant_id, price_per_class)` - 价格筛选
- `tag_categories(tenant_id, name)` - 分类查询
- `tags(tenant_id, category_id)` - 标签查询
- `teacher_tags(teacher_id)` - 教师标签
- `teacher_tags(tag_id)` - 标签教师
