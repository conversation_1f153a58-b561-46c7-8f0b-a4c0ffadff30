# 全局管理模块 (Global Management)

## 概述
租户管理、系统配置、使用统计、系统管理员模块。

## eraser.io 代码

```
// === 全局管理模块 ===

tenants [icon: building] {
  id int pk
  name string
  code string unique
  status enum
  plan_type enum
  max_teachers int
  max_members int
  total_users int
  total_members int
  total_classes int
  total_revenue decimal
  created_at timestamp
  created_by string
}

tenant_plan_templates [icon: package] {
  id int pk
  plan_code string unique
  plan_name string
  max_teachers int
  max_members int
  max_storage_gb int
  monthly_price decimal
  yearly_price decimal
  is_active boolean
}

system_configs [icon: settings] {
  id int pk
  tenant_id int
  config_key string unique
  config_value string
  config_type enum
  is_global boolean
}

system_admins [icon: user-shield] {
  id int pk
  username string unique
  email string unique
  role enum
  status enum
  last_login_at timestamp
  created_by int
}

tenant_usage_stats [icon: bar-chart] {
  id int pk
  tenant_id int
  stat_date date
  total_classes int
  completed_classes int
  active_teachers int
  active_members int
  total_revenue decimal
}

// === 关系 ===
system_configs.tenant_id > tenants.id
system_admins.created_by > system_admins.id
tenant_usage_stats.tenant_id > tenants.id
```

## 字段说明

### tenants 表
- **id**: 主键
- **name**: 机构名称
- **code**: 机构代码（唯一）
- **status**: 租户状态
  - `trial`: 试用
  - `active`: 激活
  - `suspended`: 暂停
  - `cancelled`: 注销
- **plan_type**: 套餐类型
  - `trial`: 试用版
  - `basic`: 基础版
  - `standard`: 标准版
  - `premium`: 高级版
- **max_teachers**: 最大教师数量
- **max_members**: 最大会员数量
- **total_xxx**: 统计信息
- **created_by**: 创建者（系统管理员用户名）

### tenant_plan_templates 表
- **id**: 主键
- **plan_code**: 套餐代码（唯一）
- **plan_name**: 套餐名称
- **max_teachers**: 最大教师数
- **max_members**: 最大会员数
- **max_storage_gb**: 最大存储空间(GB)
- **monthly_price**: 月付价格
- **yearly_price**: 年付价格
- **is_active**: 是否激活

### system_configs 表
- **id**: 主键
- **tenant_id**: 租户ID（null表示全局配置）
- **config_key**: 配置键（唯一）
- **config_value**: 配置值
- **config_type**: 配置类型
  - `string`: 字符串
  - `number`: 数字
  - `boolean`: 布尔值
  - `json`: JSON对象
- **is_global**: 是否全局配置

### system_admins 表
- **id**: 主键
- **username**: 用户名（全局唯一）
- **email**: 邮箱（全局唯一）
- **role**: 管理员角色
  - `super_admin`: 超级管理员
  - `admin`: 管理员
  - `billing_admin`: 计费管理员
  - `support`: 技术支持
- **status**: 管理员状态
  - `active`: 激活
  - `inactive`: 停用
  - `locked`: 锁定
- **last_login_at**: 最后登录时间
- **created_by**: 创建者ID

### tenant_usage_stats 表
- **id**: 主键
- **tenant_id**: 租户ID
- **stat_date**: 统计日期
- **total_classes**: 总课程数
- **completed_classes**: 完成课程数
- **active_teachers**: 活跃教师数
- **active_members**: 活跃会员数
- **total_revenue**: 总收入

## 业务规则

### 租户管理
- 租户代码全局唯一
- 支持试用期管理
- 资源限制控制（教师数、会员数、存储空间）

### 套餐管理
- 预定义套餐模板
- 支持月付/年付定价
- 套餐升级/降级

### 系统配置
- 支持全局和租户级配置
- 配置类型化管理
- 动态配置更新

### 使用统计
- 按日统计租户使用量
- 支持计费数据生成
- 性能监控数据

## 扩展字段（完整版本包含）
- **tenants**: display_name, domain, subdomain, contact_info, settings, features
- **system_configs**: description
- **system_admins**: real_name, permissions, login_count, failed_login_attempts
- **tenant_usage_stats**: api_calls, storage_used_mb, billing_amount

## 索引设计
- `tenants(code)` - 代码查询
- `tenants(status)` - 状态筛选
- `system_configs(config_key)` - 配置查询
- `system_configs(tenant_id, config_key)` - 租户配置
- `system_admins(username)` - 登录查询
- `tenant_usage_stats(tenant_id, stat_date)` - 统计查询
