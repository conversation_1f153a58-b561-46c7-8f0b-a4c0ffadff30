# 数据库设计文档

## 📋 概述

本目录包含了 KS English Admin Backend 项目的数据库设计文档，采用 eraser.io 格式，便于可视化展示和团队协作。

## 📁 文件结构

- `core-relationships.md` - 核心关系图（6个主要实体）
- `module-user-auth.md` - 用户权限模块详细设计
- `module-member-management.md` - 会员管理模块详细设计
- `module-teacher-management.md` - 教师管理模块详细设计
- `module-scheduling-system.md` - 课程排课模块详细设计
- `module-financial-management.md` - 财务管理模块详细设计
- `module-global-management.md` - 全局管理模块详细设计

## 🎯 设计原则

### 字段选择原则
1. **必须包含**：
   - 主键 (pk)
   - 外键 (关系字段)
   - 核心业务字段 (如 name, status, balance)

2. **可以省略**：
   - 审计字段 (created_at, updated_at 除非特别重要)
   - 详细描述字段 (description, notes)
   - JSON 字段 (settings, features)
   - 统计字段 (total_xxx, count_xxx)

3. **保留重要的业务字段**：
   - 状态字段 (status, member_status)
   - 分类字段 (role, teacher_category)
   - 金额字段 (price, balance)

### 多租户架构
- **架构模式**: 共享数据库 + 行级安全策略(RLS)
- **数据隔离**: 通过 `tenant_id` 字段实现租户间数据隔离
- **技术栈**: PostgreSQL + SQLModel + FastAPI

## 🔧 使用方法

1. **查看核心关系**: 先看 `core-relationships.md` 了解整体架构
2. **深入模块**: 根据需要查看具体模块的详细设计
3. **eraser.io 使用**: 复制文件内容到 eraser.io 生成可视化图表

## 📊 核心实体关系

```
tenants (租户)
├── users (用户)
├── members (会员)
├── teachers (教师)
├── scheduled_classes (已排课)
└── member_cards (会员卡)
```

## 🔄 业务流程

### 排课流程
1. 教师设置固定时间占位 → `teacher_fixed_slots`
2. 会员锁定固定课位 → `member_fixed_slot_locks`
3. 系统自动排课 → `scheduled_classes`
4. 课程消费扣费 → `consumption_records`

### 会员卡流程
1. 创建卡片模板 → `member_card_templates`
2. 发放会员卡 → `member_cards`
3. 充值操作 → `recharge_records`
4. 课程消费 → `consumption_records`

---

**创建时间**: 2025-06-28
**维护人**: 开发团队
**版本**: v1.0
