# 用户权限模块 (User & Auth)

## 概述
用户管理、认证、会话管理模块，支持多角色权限控制。

## eraser.io 代码

```
// === 用户权限模块 ===

users [icon: user] {
  id int pk
  tenant_id int
  username string unique
  email string
  role enum
  status enum
  created_by int
  created_at timestamp
}

user_sessions [icon: key] {
  id int pk
  user_id int
  session_token string unique
  expires_at timestamp
}

// === 关系 ===
users.tenant_id > tenants.id
users.created_by > users.id
user_sessions.user_id > users.id
```

## 字段说明

### users 表
- **id**: 主键
- **tenant_id**: 租户ID，super_admin 为 null
- **username**: 全局唯一用户名
- **email**: 租户内唯一邮箱
- **role**: 用户角色
  - `super_admin`: 超级管理员（跨租户）
  - `admin`: 租户管理员
  - `agent`: 代理人员
  - `sale`: 销售人员
- **status**: 用户状态
  - `active`: 激活
  - `inactive`: 停用
  - `locked`: 锁定
- **created_by**: 创建者ID（自引用）

### user_sessions 表
- **id**: 主键
- **user_id**: 用户ID
- **session_token**: 会话令牌（唯一）
- **expires_at**: 过期时间

## 业务规则

### 用户创建
- super_admin 可以创建任何角色用户
- admin 只能创建本租户内的用户
- 用户名全局唯一，邮箱租户内唯一

### 会话管理
- 每次登录创建新会话
- 支持多设备同时登录
- 会话过期自动清理

### 权限控制
- 基于角色的权限控制(RBAC)
- 租户级别数据隔离
- 操作审计追踪

## 索引设计
- `users(tenant_id, id)` - 租户用户查询
- `users(username)` - 登录查询
- `users(tenant_id, email)` - 邮箱查询
- `user_sessions(session_token)` - 会话验证
- `user_sessions(user_id)` - 用户会话查询
