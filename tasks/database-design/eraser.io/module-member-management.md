# 会员管理模块 (Member Management)

## 概述
会员信息管理、归属关系、统计数据模块。

## eraser.io 代码

```
// === 会员管理模块 ===

members [icon: users] {
  id int pk
  tenant_id int
  name string
  phone string
  member_status enum
  sales_id int
  agent_id int
  total_classes int
  total_spent decimal
  created_by int
  created_at timestamp
}

// === 关系 ===
members.tenant_id > tenants.id
members.sales_id > users.id
members.agent_id > users.id
members.created_by > users.id
```

## 字段说明

### members 表
- **id**: 主键
- **tenant_id**: 租户ID
- **name**: 会员姓名
- **phone**: 手机号（租户内唯一）
- **member_status**: 会员状态
  - `active`: 活跃
  - `silent`: 沉默
  - `frozen`: 冻结
  - `cancelled`: 注销
- **sales_id**: 销售人员ID
- **agent_id**: 代理人员ID
- **total_classes**: 总上课数（统计字段）
- **total_spent**: 总消费金额（统计字段）
- **created_by**: 创建者ID

## 业务规则

### 会员注册
- 支持同手机号多账户（不同租户）
- 新会员默认为试用会员
- 自动分配销售/代理人员

### 归属关系
- 每个会员可分配给销售人员
- 每个会员可分配给代理人员
- 支持归属关系变更

### 状态管理
- 活跃会员可正常使用所有功能
- 沉默会员限制部分功能
- 冻结会员暂停所有服务
- 注销会员保留历史数据

### 微信集成
- 支持微信小程序/公众号登录
- openid 全局唯一
- 支持微信信息同步

## 扩展字段（完整版本包含）
- 基础信息：email, gender, birthday, avatar_url
- 微信信息：wechat_openid, wechat_unionid, wechat_nickname
- 会员属性：member_type, source_channel
- 地址信息：address, city, province, country
- 学习信息：level, learning_goals, preferred_time
- 统计信息：completed_classes, cancelled_classes, no_show_classes
- 评价信息：avg_rating, rating_count
- 时间信息：last_class_at, last_login_at, registered_at

## 索引设计
- `members(tenant_id, id)` - 租户会员查询
- `members(tenant_id, phone)` - 手机号查询
- `members(tenant_id, member_status)` - 状态筛选
- `members(sales_id)` - 销售人员会员
- `members(agent_id)` - 代理人员会员
