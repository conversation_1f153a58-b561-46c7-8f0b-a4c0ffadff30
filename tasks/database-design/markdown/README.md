# 数据库设计 - Markdown 版本

## 📋 概述

本目录包含与 eraser.io 格式一一对应的 Markdown 版本数据库设计文档，便于快速查阅和版本控制。

## 📁 文件对应关系

| eraser.io 文件 | Markdown 文件 | 说明 |
|----------------|---------------|------|
| `core-relationships.md` | `core-relationships-md.md` | 核心关系图 |
| `module-user-auth.md` | `module-user-auth-md.md` | 用户权限模块 |
| `module-member-management.md` | `module-member-management-md.md` | 会员管理模块 |
| `module-teacher-management.md` | `module-teacher-management-md.md` | 教师管理模块 |
| `module-scheduling-system.md` | `module-scheduling-system-md.md` | 课程排课模块 |
| `module-financial-management.md` | `module-financial-management-md.md` | 财务管理模块 |
| `module-global-management.md` | `module-global-management-md.md` | 全局管理模块 |

## 🎯 使用场景

### eraser.io 版本
- 可视化展示
- 团队讨论
- 架构演示
- 在线协作

### Markdown 版本
- 快速查阅字段
- 代码开发参考
- 文档记录
- 版本控制

## 🔄 维护原则

1. **保持同步**：两个版本内容必须一致
2. **结构对应**：表结构和关系完全对应
3. **字段一致**：字段名称和类型保持一致
4. **注释同步**：业务说明和注释同步更新

## 📝 格式说明

### 表格格式
```markdown
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int | PK | 主键 |
| name | string | NOT NULL | 名称 |
```

### 关系格式
```markdown
- `users.tenant_id` → `tenants.id` (N:1)
- `members.sales_id` → `users.id` (N:1)
```

### 枚举格式
```markdown
**status** (状态):
- `active`: 激活
- `inactive`: 停用
- `locked`: 锁定
```

---

**维护说明**: 当 eraser.io 版本更新时，请同步更新对应的 Markdown 版本
