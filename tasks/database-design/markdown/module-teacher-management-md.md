# 教师管理模块 - Markdown 版本

> 对应文件: `../module-teacher-management.md`

## 模块概述
教师信息管理、标签系统、分类管理模块。

## 表结构定义

### teachers (教师表)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| tenant_id | int | FK, NOT NULL | - | 租户ID |
| name | string | NOT NULL | - | 教师姓名 |
| teacher_category | enum | NOT NULL | - | 教师分类 |
| region | enum | NOT NULL | - | 教师区域 |
| price_per_class | int | NOT NULL | 0 | 单节课价格(元) |
| status | enum | NOT NULL | 'pending' | 教师状态 |
| created_by | int | FK | NULL | 创建者ID |

**外键关系**:
- `tenant_id` → `tenants.id`
- `created_by` → `users.id`

**枚举定义**:

**teacher_category** (教师分类):
- `european`: 欧美教师
- `south_african`: 南非教师
- `filipino`: 菲律宾教师
- `chinese`: 中教
- `other`: 其他

**region** (教师区域):
- `europe`: 欧洲
- `north_america`: 北美
- `south_africa`: 南非
- `philippines`: 菲律宾
- `china`: 中国
- `other`: 其他

**status** (教师状态):
- `pending`: 待审核
- `active`: 激活
- `inactive`: 停用
- `on_leave`: 请假

### tag_categories (标签分类表)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| tenant_id | int | FK, NOT NULL | - | 租户ID |
| name | string | NOT NULL | - | 分类名称 |
| created_by | int | FK | NULL | 创建者ID |

**外键关系**:
- `tenant_id` → `tenants.id`
- `created_by` → `users.id`

**分类示例**:
- 教材类: 新概念、剑桥、牛津等
- 教学风格类: 活泼、严谨、幽默等
- 专业特长类: 口语、语法、写作等

### tags (标签表)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| tenant_id | int | FK, NOT NULL | - | 租户ID |
| category_id | int | FK, NOT NULL | - | 标签分类ID |
| name | string | NOT NULL | - | 标签名称 |
| status | enum | NOT NULL | 'active' | 标签状态 |
| created_by | int | FK | NULL | 创建者ID |

**外键关系**:
- `tenant_id` → `tenants.id`
- `category_id` → `tag_categories.id`
- `created_by` → `users.id`

**status** (标签状态):
- `active`: 激活
- `inactive`: 停用

### teacher_tags (教师标签关联表)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| teacher_id | int | FK, NOT NULL | - | 教师ID |
| tag_id | int | FK, NOT NULL | - | 标签ID |
| created_by | int | FK | NULL | 创建者ID |

**外键关系**:
- `teacher_id` → `teachers.id`
- `tag_id` → `tags.id`
- `created_by` → `users.id`

**唯一约束**:
- `(teacher_id, tag_id)` - 教师-标签组合唯一

## 业务规则

### 教师管理规则
1. **分类管理**:
   - 教师按分类和区域管理
   - 支持多维度筛选（分类、区域、价格、标签）
   - 排课优先级：欧美/南非 > 菲律宾

2. **价格管理**:
   - 单节课价格以整数存储（单位：元）
   - 支持按价格范围筛选
   - 价格可以为0（免费试听等）

3. **状态管理**:
   - 新教师默认为待审核状态
   - 只有激活状态的教师可以排课
   - 请假状态暂停排课但保留数据

### 标签系统规则
1. **分类管理**:
   - 标签必须归属于某个分类
   - 分类名称在租户内唯一
   - 支持分类的启用/停用

2. **标签管理**:
   - 标签名称在同分类下唯一
   - 支持标签的启用/停用
   - 停用的标签不影响已有关联

3. **关联管理**:
   - 教师可关联多个标签
   - 同一教师不能重复关联同一标签
   - 支持批量添加/删除标签

### 筛选和排序规则
1. **多维度筛选**:
   - 按分类筛选：`teacher_category`
   - 按区域筛选：`region`
   - 按价格筛选：`price_per_class`
   - 按标签筛选：通过 `teacher_tags` 关联
   - 按状态筛选：`status`

2. **排序规则**:
   - 默认按创建时间倒序
   - 支持按价格排序
   - 支持按优先级排序（业务逻辑）

## 扩展字段说明

### teachers 表完整版本
```sql
-- 基础信息扩展
gender              enum           -- 性别 (male/female/other)
avatar              varchar(500)   -- 头像URL
phone               varchar(20)    -- 手机号
email               varchar(100)   -- 邮箱

-- 微信信息
wechat_bound        boolean        -- 是否绑定微信
wechat_openid       varchar(100)   -- 微信OpenID
wechat_unionid      varchar(100)   -- 微信UnionID

-- 显示设置
show_to_members     boolean        -- 是否对会员端展示

-- 教学信息
introduction        text           -- 教师介绍
teaching_experience int            -- 教学经验(年)
specialties         json           -- 专业特长数组
certifications      json           -- 资质证书数组

-- 排课设置
priority_level      int            -- 排课优先级(数字越大优先级越高)

-- 备注
notes               text           -- 备注信息
```

### tag_categories 表完整版本
```sql
description         varchar(200)   -- 分类描述
sort_order          int            -- 排序顺序
```

### tags 表完整版本
```sql
description         varchar(200)   -- 标签描述
```

## 索引设计

### 主要索引
```sql
-- 教师表索引
CREATE INDEX idx_teachers_tenant_id ON teachers(tenant_id, id);
CREATE INDEX idx_teachers_status ON teachers(tenant_id, status);
CREATE INDEX idx_teachers_category_region ON teachers(tenant_id, teacher_category, region);
CREATE INDEX idx_teachers_price ON teachers(tenant_id, price_per_class);
CREATE INDEX idx_teachers_created_by ON teachers(created_by);

-- 标签分类表索引
CREATE UNIQUE INDEX idx_tag_categories_name ON tag_categories(tenant_id, name);
CREATE INDEX idx_tag_categories_tenant ON tag_categories(tenant_id, id);

-- 标签表索引
CREATE UNIQUE INDEX idx_tags_category_name ON tags(tenant_id, category_id, name);
CREATE INDEX idx_tags_category ON tags(tenant_id, category_id);
CREATE INDEX idx_tags_status ON tags(tenant_id, status);

-- 教师标签关联表索引
CREATE UNIQUE INDEX idx_teacher_tags_unique ON teacher_tags(teacher_id, tag_id);
CREATE INDEX idx_teacher_tags_teacher ON teacher_tags(teacher_id);
CREATE INDEX idx_teacher_tags_tag ON teacher_tags(tag_id);
```

## 常用查询模式

### 教师查询
```sql
-- 按条件筛选教师
SELECT t.id, t.name, t.teacher_category, t.region, t.price_per_class, t.status
FROM teachers t
WHERE t.tenant_id = ? 
  AND t.status = 'active'
  AND t.teacher_category IN ('european', 'south_african')
  AND t.price_per_class BETWEEN ? AND ?
ORDER BY t.price_per_class ASC;

-- 按标签筛选教师
SELECT DISTINCT t.id, t.name, t.teacher_category, t.price_per_class
FROM teachers t
JOIN teacher_tags tt ON t.id = tt.teacher_id
JOIN tags tg ON tt.tag_id = tg.id
WHERE t.tenant_id = ? 
  AND t.status = 'active'
  AND tg.name IN ('新概念', '口语专家')
  AND tg.status = 'active';
```

### 标签管理
```sql
-- 获取分类及其标签
SELECT tc.id as category_id, tc.name as category_name,
       t.id as tag_id, t.name as tag_name, t.status
FROM tag_categories tc
LEFT JOIN tags t ON tc.id = t.category_id
WHERE tc.tenant_id = ?
ORDER BY tc.name, t.name;

-- 获取教师的所有标签
SELECT tc.name as category_name, t.name as tag_name
FROM teacher_tags tt
JOIN tags t ON tt.tag_id = t.id
JOIN tag_categories tc ON t.category_id = tc.id
WHERE tt.teacher_id = ?
ORDER BY tc.name, t.name;
```

### 业务操作
```sql
-- 为教师添加标签
INSERT INTO teacher_tags (teacher_id, tag_id, created_by)
VALUES (?, ?, ?)
ON DUPLICATE KEY UPDATE created_by = VALUES(created_by);

-- 批量删除教师标签
DELETE FROM teacher_tags 
WHERE teacher_id = ? AND tag_id IN (?, ?, ?);

-- 更新教师状态
UPDATE teachers 
SET status = ?, updated_at = NOW() 
WHERE id = ? AND tenant_id = ?;
```
