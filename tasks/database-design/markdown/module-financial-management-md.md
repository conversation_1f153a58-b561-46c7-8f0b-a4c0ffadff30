# 财务管理模块 - Markdown 版本

> 对应文件: `../module-financial-management.md`

## 模块概述

会员卡管理、充值记录、消费记录、财务统计模块。

## 表结构定义

### member_card_templates (会员卡模板表)

| 字段名     | 类型    | 约束         | 默认值         | 说明         |
| ---------- | ------- | ------------ | -------------- | ------------ |
| id         | int     | PK           | AUTO_INCREMENT | 主键         |
| tenant_id  | int     | FK, NOT NULL | -              | 租户 ID      |
| name       | string  | NOT NULL     | -              | 卡片模板名称 |
| card_type  | enum    | NOT NULL     | -              | 卡片类型     |
| sale_price | decimal | NOT NULL     | 0.00           | 售卖价格     |
| created_by | int     | FK           | NULL           | 创建者 ID    |

**外键关系**:

- `tenant_id` → `tenants.id`
- `created_by` → `users.id`

**枚举定义**:

**card_type** (卡片类型):

- `times_limited`: 次卡-有期限
- `times_unlimited`: 次卡-无期限
- `value_limited`: 储值卡-有期限
- `value_unlimited`: 储值卡-无期限

### member_cards (会员卡表)

| 字段名      | 类型    | 约束         | 默认值         | 说明     |
| ----------- | ------- | ------------ | -------------- | -------- |
| id          | int     | PK           | AUTO_INCREMENT | 主键     |
| tenant_id   | int     | FK, NOT NULL | -              | 租户 ID  |
| member_id   | int     | FK, NOT NULL | -              | 会员 ID  |
| template_id | int     | FK           | NULL           | 模板 ID  |
| card_type   | enum    | NOT NULL     | -              | 卡片类型 |
| balance     | decimal | NOT NULL     | 0.00           | 当前余额 |
| status      | enum    | NOT NULL     | 'active'       | 卡片状态 |

**外键关系**:

- `tenant_id` → `tenants.id`
- `member_id` → `members.id`
- `template_id` → `member_card_templates.id`

**status** (卡片状态):

- `active`: 激活
- `frozen`: 冻结
- `expired`: 过期
- `cancelled`: 注销

### recharge_records (充值记录表)

| 字段名         | 类型    | 约束         | 默认值         | 说明      |
| -------------- | ------- | ------------ | -------------- | --------- |
| id             | int     | PK           | AUTO_INCREMENT | 主键      |
| tenant_id      | int     | FK, NOT NULL | -              | 租户 ID   |
| member_id      | int     | FK, NOT NULL | -              | 会员 ID   |
| member_card_id | int     | FK, NOT NULL | -              | 会员卡 ID |
| amount         | decimal | NOT NULL     | -              | 充值金额  |
| operated_by    | int     | FK           | NULL           | 操作人 ID |

**外键关系**:

- `tenant_id` → `tenants.id`
- `member_id` → `members.id`
- `member_card_id` → `member_cards.id`
- `operated_by` → `users.id`

### consumption_records (消费记录表)

| 字段名             | 类型    | 约束         | 默认值         | 说明      |
| ------------------ | ------- | ------------ | -------------- | --------- |
| id                 | int     | PK           | AUTO_INCREMENT | 主键      |
| tenant_id          | int     | FK, NOT NULL | -              | 租户 ID   |
| member_id          | int     | FK, NOT NULL | -              | 会员 ID   |
| member_card_id     | int     | FK, NOT NULL | -              | 会员卡 ID |
| scheduled_class_id | int     | FK           | NULL           | 课程 ID   |
| amount             | decimal | NOT NULL     | -              | 消费金额  |

**外键关系**:

- `tenant_id` → `tenants.id`
- `member_id` → `members.id`
- `member_card_id` → `member_cards.id`
- `scheduled_class_id` → `scheduled_classes.id`

## 业务规则

### 会员卡模板规则

1. **模板管理**:

   - 管理员创建卡片模板
   - 设置售价、可用余额、有效期
   - 支持线上购买、代理专售等配置

2. **卡片类型**:

   - **次卡**: 按次数计费，每次课程扣除固定次数
   - **储值卡**: 按金额计费，每次课程扣除实际费用
   - **有期限**: 设置有效期，过期后不可使用
   - **无期限**: 永久有效，直到余额用完

3. **模板配置**:
   - 售卖价格：会员购买时的价格
   - 可用余额：实际可用的金额或次数
   - 有效期：卡片的使用期限

### 会员卡实例规则

1. **卡片发放**:

   - 新会员默认生成储值卡（余额 0，有效期 30 天）
   - 每个会员仅有一个储值卡，可有多个次卡
   - 支持基于模板创建或手动创建

2. **余额管理**:

   - 储值卡：余额为金额，支持小数
   - 次卡：余额为次数，通常为整数
   - 余额不能为负数

3. **状态管理**:
   - `active`: 正常使用
   - `frozen`: 冻结状态，不可使用但保留余额
   - `expired`: 过期状态，不可使用
   - `cancelled`: 注销状态，不可恢复

### 充值管理规则

1. **充值方式**:

   - 微信支付：在线自动充值
   - 手动充值：管理员操作充值
   - 支持充值奖励和赠送金额

2. **充值流程**:

   - 选择会员卡
   - 输入充值金额
   - 选择支付方式
   - 确认充值并更新余额

3. **数据完整性**:
   - 记录操作人（手动充值）
   - 记录充值原因和备注
   - 支持充值撤销（特殊情况）

### 消费管理规则

1. **消费触发**:

   - 课程完成后自动扣费
   - 支持手动调整消费记录
   - 支持多种消费类型

2. **扣费逻辑**:

   - 储值卡：扣除课程实际费用
   - 次卡：扣除固定次数（通常为 1 次）
   - 余额不足时禁止消费

3. **消费记录**:
   - 关联具体课程（如果是课程消费）
   - 记录消费金额和类型
   - 支持消费退款

## 扩展字段说明

### member_card_templates 表完整版本

```sql
available_balance       decimal(10,2)  -- 可用余额
validity_days          int            -- 有效期(天)
is_agent_exclusive     boolean        -- 是否代理专售
allow_repeat_purchase  boolean        -- 是否允许重复购买
allow_renewal   boolean        -- 是否支持续费
description            text           -- 模板描述
```

### member_cards 表完整版本

```sql
card_number            varchar(50)    -- 卡号 (唯一)
total_recharged        decimal(10,2)  -- 总充值金额
total_consumed         decimal(10,2)  -- 总消费金额
expires_at             timestamp      -- 过期时间
```

### recharge_records 表完整版本

```sql
bonus_amount           decimal(10,2)  -- 赠送金额
payment_method         enum           -- 支付方式 (wechat/alipay/manual)
payment_status         enum           -- 支付状态 (pending/completed/failed)
transaction_id         varchar(100)   -- 交易ID
notes                  text           -- 备注
```

### consumption_records 表完整版本

```sql
consumption_type       enum           -- 消费类型 (class_fee/material_fee/other)
description            text           -- 消费描述
status                 enum           -- 消费状态 (pending/completed/refunded)
```

## 索引设计

### 主要索引

```sql
-- 会员卡模板表
CREATE INDEX idx_member_card_templates_tenant ON member_card_templates(tenant_id, is_active);
CREATE INDEX idx_member_card_templates_type ON member_card_templates(tenant_id, card_type);

-- 会员卡表
CREATE INDEX idx_member_cards_tenant_member ON member_cards(tenant_id, member_id);
CREATE INDEX idx_member_cards_status ON member_cards(tenant_id, status);
CREATE INDEX idx_member_cards_template ON member_cards(template_id);

-- 充值记录表
CREATE INDEX idx_recharge_records_tenant_member ON recharge_records(tenant_id, member_id);
CREATE INDEX idx_recharge_records_card ON recharge_records(member_card_id);
CREATE INDEX idx_recharge_records_operator ON recharge_records(operated_by);
CREATE INDEX idx_recharge_records_date ON recharge_records(tenant_id, created_at);

-- 消费记录表
CREATE INDEX idx_consumption_records_tenant_member ON consumption_records(tenant_id, member_id);
CREATE INDEX idx_consumption_records_card ON consumption_records(member_card_id);
CREATE INDEX idx_consumption_records_class ON consumption_records(scheduled_class_id);
CREATE INDEX idx_consumption_records_date ON consumption_records(tenant_id, created_at);
```

## 常用查询模式

### 会员卡查询

```sql
-- 获取会员的所有卡片
SELECT mc.id, mc.card_type, mc.balance, mc.status,
       mct.name as template_name
FROM member_cards mc
LEFT JOIN member_card_templates mct ON mc.template_id = mct.id
WHERE mc.member_id = ? AND mc.tenant_id = ?
ORDER BY mc.created_at DESC;

-- 检查卡片余额
SELECT balance, status
FROM member_cards
WHERE id = ? AND member_id = ? AND tenant_id = ?;
```

### 财务统计

```sql
-- 会员充值统计
SELECT DATE(created_at) as date,
       COUNT(*) as recharge_count,
       SUM(amount) as total_amount
FROM recharge_records
WHERE tenant_id = ?
  AND created_at BETWEEN ? AND ?
GROUP BY DATE(created_at)
ORDER BY date;

-- 会员消费统计
SELECT DATE(created_at) as date,
       COUNT(*) as consumption_count,
       SUM(amount) as total_amount
FROM consumption_records
WHERE tenant_id = ?
  AND created_at BETWEEN ? AND ?
GROUP BY DATE(created_at)
ORDER BY date;
```

### 业务操作

```sql
-- 充值操作
BEGIN;
INSERT INTO recharge_records (tenant_id, member_id, member_card_id, amount, operated_by)
VALUES (?, ?, ?, ?, ?);
UPDATE member_cards
SET balance = balance + ?, total_recharged = total_recharged + ?, updated_at = NOW()
WHERE id = ?;
COMMIT;

-- 消费操作
BEGIN;
INSERT INTO consumption_records (tenant_id, member_id, member_card_id, scheduled_class_id, amount)
VALUES (?, ?, ?, ?, ?);
UPDATE member_cards
SET balance = balance - ?, total_consumed = total_consumed + ?, updated_at = NOW()
WHERE id = ? AND balance >= ?;
COMMIT;
```
