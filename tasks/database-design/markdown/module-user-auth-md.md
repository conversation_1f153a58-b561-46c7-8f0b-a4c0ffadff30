# 用户权限模块 - Markdown 版本

> 对应文件: `../module-user-auth.md`

## 模块概述
用户管理、认证、会话管理模块，支持多角色权限控制。

## 表结构定义

### users (用户表)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| tenant_id | int | FK | NULL | 租户ID (super_admin为null) |
| username | string | UNIQUE, NOT NULL | - | 用户名 |
| email | string | - | NULL | 邮箱 |
| role | enum | NOT NULL | - | 用户角色 |
| status | enum | NOT NULL | 'active' | 用户状态 |
| created_by | int | FK | NULL | 创建者ID |
| created_at | timestamp | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**外键关系**:
- `tenant_id` → `tenants.id`
- `created_by` → `users.id` (自引用)

**枚举定义**:

**role** (用户角色):
- `super_admin`: 超级管理员（跨租户）
- `admin`: 租户管理员
- `agent`: 代理人员
- `sale`: 销售人员

**status** (用户状态):
- `active`: 激活
- `inactive`: 停用
- `locked`: 锁定

### user_sessions (用户会话表)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| user_id | int | FK, NOT NULL | - | 用户ID |
| session_token | string | UNIQUE, NOT NULL | - | 会话令牌 |
| expires_at | timestamp | NOT NULL | - | 过期时间 |

**外键关系**:
- `user_id` → `users.id`

## 业务规则

### 用户创建规则
1. **权限控制**:
   - `super_admin` 可以创建任何角色用户
   - `admin` 只能创建本租户内的用户
   - 其他角色不能创建用户

2. **唯一性约束**:
   - 用户名全局唯一
   - 邮箱租户内唯一（允许跨租户重复）
   - 手机号租户内唯一

3. **数据完整性**:
   - `super_admin` 的 `tenant_id` 为 `NULL`
   - 其他角色必须有 `tenant_id`
   - `created_by` 记录创建者，支持审计追踪

### 会话管理规则
1. **会话创建**:
   - 每次登录创建新会话
   - 支持多设备同时登录
   - 会话令牌全局唯一

2. **会话过期**:
   - 会话有明确的过期时间
   - 过期会话自动失效
   - 支持主动注销

3. **安全控制**:
   - 会话令牌随机生成
   - 支持会话刷新
   - 异常登录检测

### 权限控制规则
1. **基于角色的权限控制(RBAC)**:
   - 每个用户有明确的角色
   - 角色决定可访问的功能
   - 支持角色权限扩展

2. **租户级别数据隔离**:
   - 普通用户只能访问本租户数据
   - `super_admin` 可以跨租户访问
   - 严格的数据边界控制

3. **操作审计**:
   - 重要操作记录创建者
   - 支持操作历史追踪
   - 便于安全审计

## 索引设计

### 主要索引
```sql
-- 用户表索引
CREATE INDEX idx_users_tenant_id ON users(tenant_id, id);
CREATE UNIQUE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_tenant_email ON users(tenant_id, email);
CREATE INDEX idx_users_role ON users(tenant_id, role);
CREATE INDEX idx_users_status ON users(tenant_id, status);

-- 会话表索引
CREATE UNIQUE INDEX idx_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_sessions_expires ON user_sessions(expires_at);
```

### 索引说明
- `idx_users_tenant_id`: 租户用户查询优化
- `idx_users_username`: 登录查询优化
- `idx_users_tenant_email`: 邮箱查询优化
- `idx_sessions_token`: 会话验证优化
- `idx_sessions_expires`: 过期会话清理优化

## 常用查询模式

### 用户认证
```sql
-- 用户登录验证
SELECT id, tenant_id, role, status 
FROM users 
WHERE username = ? AND password_hash = ?;

-- 会话验证
SELECT u.id, u.tenant_id, u.role, u.status
FROM users u
JOIN user_sessions s ON u.id = s.user_id
WHERE s.session_token = ? AND s.expires_at > NOW();
```

### 权限检查
```sql
-- 检查用户是否属于指定租户
SELECT COUNT(*) 
FROM users 
WHERE id = ? AND (tenant_id = ? OR role = 'super_admin');

-- 获取用户权限信息
SELECT role, status, tenant_id 
FROM users 
WHERE id = ?;
```

### 会话管理
```sql
-- 创建新会话
INSERT INTO user_sessions (user_id, session_token, expires_at) 
VALUES (?, ?, ?);

-- 清理过期会话
DELETE FROM user_sessions 
WHERE expires_at < NOW();
```
