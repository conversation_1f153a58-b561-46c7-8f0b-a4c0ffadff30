# 课程排课模块 - Markdown 版本

> 对应文件: `../eraser.io/module-scheduling-system.md`

## 模块概述
固定时间占位、课程安排、锁定管理、自动排课系统，包含完整的配置管理和操作记录。

## 表结构定义

### course_system_configs (课程系统配置表)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| tenant_id | int | FK, NOT NULL | - | 租户ID |
| default_slot_duration_minutes | int | NOT NULL | 25 | 默认课节时长(分钟) |
| default_slot_interval_minutes | int | NOT NULL | 5 | 默认课节间隔(分钟) |
| temp_booking_enabled | boolean | NOT NULL | true | 是否启用临时约课 |
| fixed_booking_enabled | boolean | NOT NULL | true | 是否启用固定课表约课 |
| auto_schedule_enabled | boolean | NOT NULL | true | 是否启用自动排课 |
| auto_schedule_day | int | NOT NULL | 22 | 自动排课日期(每月几号) |
| auto_schedule_time | time | NOT NULL | '14:00' | 自动排课时间 |
| max_advance_days | int | - | NULL | 会员最多可预约x天后的课程 |
| booking_deadline_hours | int | - | NULL | 预约截止时间：上课前x小时 |
| cancel_deadline_hours | int | - | NULL | 取消截止时间：上课前x小时 |
| teacher_can_add_slots | boolean | NOT NULL | true | 教师是否可自主增加课时 |
| teacher_can_delete_empty_slots | boolean | NOT NULL | true | 教师是否可删除空课时 |
| default_schedule_weeks | int | NOT NULL | 4 | 默认排课周数 |
| created_at | timestamp | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| created_by | int | FK | NULL | 创建者ID |

**外键关系**:
- `tenant_id` → `tenants.id`
- `created_by` → `users.id`

### teacher_fixed_slots (教师固定时间占位表)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| tenant_id | int | FK, NOT NULL | - | 租户ID |
| teacher_id | int | FK, NOT NULL | - | 教师ID |
| weekday | enum | NOT NULL | - | 星期几(1-7) |
| time_slot | enum | NOT NULL | - | 时间段(HH:MM) |
| is_available | boolean | NOT NULL | true | 是否可用 |

**外键关系**:
- `tenant_id` → `tenants.id`
- `teacher_id` → `teachers.id`

**唯一约束**:
- `(teacher_id, weekday, time_slot)` - 教师时间段唯一

**枚举定义**:

**weekday** (星期):
- `1`: 星期一
- `2`: 星期二
- `3`: 星期三
- `4`: 星期四
- `5`: 星期五
- `6`: 星期六
- `7`: 星期日

**time_slot** (时间段 - 30分钟间隔):
- `00:00`, `00:30`, `01:00`, ... `23:30`
- 共48个时间段，每天最多可开放48个slot

### scheduled_classes (已排课表)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| tenant_id | int | FK, NOT NULL | - | 租户ID |
| teacher_id | int | FK, NOT NULL | - | 教师ID |
| member_id | int | FK, NOT NULL | - | 会员ID |
| course_template_id | int | FK | NULL | 课程模板ID |
| class_datetime | timestamp | NOT NULL | - | 课程时间 |
| class_type | enum | NOT NULL | 'fixed' | 课程类型 |
| status | enum | NOT NULL | 'scheduled' | 课程状态 |

**外键关系**:
- `tenant_id` → `tenants.id`
- `teacher_id` → `teachers.id`
- `member_id` → `members.id`
- `course_template_id` → `course_templates.id`

**枚举定义**:

**class_type** (课程类型):
- `fixed`: 固定课程
- `temporary`: 临时课程

**status** (课程状态):
- `available`: 可预约（空课）
- `booked`: 已预约
- `completed`: 已完成
- `teacher_no_show`: 教师缺席
- `member_no_show`: 会员缺席

### schedule_tasks (排课任务记录表)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| tenant_id | int | FK, NOT NULL | - | 租户ID |
| task_name | string | - | NULL | 任务名称 |
| task_type | enum | NOT NULL | 'manual' | 任务类型 |
| teacher_ids | json | - | NULL | 参与排课的教师ID列表 |
| from_date | date | - | NULL | 开始排课日期（周一） |
| schedule_weeks | int | - | NULL | 排课周数 |
| status | enum | NOT NULL | 'pending' | 执行状态 |
| total_teachers | int | NOT NULL | 0 | 总教师数 |
| success_teachers | int | NOT NULL | 0 | 成功教师数 |
| total_classes | int | NOT NULL | 0 | 总生成课程数 |
| started_at | timestamp | - | NULL | 开始时间 |
| completed_at | timestamp | - | NULL | 完成时间 |
| created_by | int | FK | NULL | 创建者ID |

**task_type** (任务类型):
- `manual`: 手动排课
- `auto`: 自动排课

**status** (执行状态):
- `pending`: 待执行
- `running`: 执行中
- `completed`: 已完成
- `failed`: 失败

### schedule_logs (排课详细日志表)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| tenant_id | int | FK, NOT NULL | - | 租户ID |
| task_id | int | FK, NOT NULL | - | 排课任务ID |
| log_level | string | - | NULL | 日志级别 |
| log_message | text | - | NULL | 日志内容 |
| teacher_id | int | FK | NULL | 相关教师ID |
| member_id | int | FK | NULL | 相关会员ID |
| class_datetime | timestamp | - | NULL | 相关课程时间 |
| error_code | string | - | NULL | 错误代码 |
| created_at | timestamp | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**log_level** (日志级别):
- `INFO`: 信息
- `WARN`: 警告
- `ERROR`: 错误

## 操作记录表

### scheduled_class_operation_logs (已排课表操作记录)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| tenant_id | int | FK, NOT NULL | - | 租户ID |
| scheduled_class_id | int | FK, NOT NULL | - | 已排课记录ID |
| member_id | int | FK | NULL | 会员ID |
| teacher_id | int | FK, NOT NULL | - | 教师ID |
| operation_type | enum | NOT NULL | - | 操作类型 |
| old_status | string | - | NULL | 操作前状态 |
| new_status | string | - | NULL | 操作后状态 |
| class_datetime | timestamp | - | NULL | 课程时间 |
| member_card_id | int | FK | NULL | 会员卡ID |
| deducted_amount | int | - | NULL | 扣除金额（元） |
| operation_reason | text | - | NULL | 操作原因 |
| operated_by | int | FK | NULL | 操作人ID |
| operator_name | string | - | NULL | 操作人姓名 |
| operator_type | enum | - | NULL | 操作人类型 |
| operation_time | timestamp | NOT NULL | CURRENT_TIMESTAMP | 操作时间 |

**operation_type** (操作类型):
- `create_slot`: 创建课时
- `book`: 预约
- `cancel`: 取消预约
- `reschedule`: 改期
- `complete`: 完成
- `no_show_member`: 会员缺席
- `no_show_teacher`: 教师缺席

**operator_type** (操作人类型):
- `member`: 会员
- `teacher`: 教师
- `admin`: 管理员

### teacher_fixed_slot_operation_logs (教师固定时间占位操作记录)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| tenant_id | int | FK, NOT NULL | - | 租户ID |
| teacher_fixed_slot_id | int | FK | NULL | 教师固定时间段ID |
| teacher_id | int | FK, NOT NULL | - | 教师ID |
| weekday | int | NOT NULL | - | 星期几 |
| start_time | time | NOT NULL | - | 开始时间 |
| operation_type | enum | NOT NULL | - | 操作类型 |
| old_is_available | boolean | - | NULL | 操作前可用状态 |
| new_is_available | boolean | - | NULL | 操作后可用状态 |
| operation_reason | text | - | NULL | 操作原因 |
| operated_by | int | FK | NULL | 操作人ID |
| operator_name | string | - | NULL | 操作人姓名 |
| operator_type | enum | - | NULL | 操作人类型 |
| operation_time | timestamp | NOT NULL | CURRENT_TIMESTAMP | 操作时间 |

**operation_type** (操作类型):
- `create`: 创建时间段
- `update`: 更新时间段
- `enable`: 开放时间段
- `disable`: 关闭时间段
- `delete`: 删除时间段
- `visibility_change`: 修改可见性

### member_fixed_lock_operation_logs (会员固定位锁定操作记录)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| tenant_id | int | FK, NOT NULL | - | 租户ID |
| member_fixed_slot_lock_id | int | FK | NULL | 会员锁定记录ID |
| teacher_fixed_slot_id | int | FK, NOT NULL | - | 教师时间段ID |
| member_id | int | FK, NOT NULL | - | 会员ID |
| teacher_id | int | FK, NOT NULL | - | 教师ID |
| weekday | int | NOT NULL | - | 星期几 |
| start_time | time | NOT NULL | - | 开始时间 |
| operation_type | enum | NOT NULL | - | 操作类型 |
| old_status | string | - | NULL | 操作前状态 |
| new_status | string | - | NULL | 操作后状态 |
| member_balance | decimal | - | NULL | 操作时会员余额 |
| operation_reason | text | - | NULL | 操作原因 |
| operated_by | int | FK | NULL | 操作人ID |
| operator_name | string | - | NULL | 操作人姓名 |
| operator_type | enum | - | NULL | 操作人类型 |
| operation_time | timestamp | NOT NULL | CURRENT_TIMESTAMP | 操作时间 |

**operation_type** (操作类型):
- `lock`: 会员锁定时间位
- `unlock`: 会员取消锁定
- `pause`: 暂停锁定
- `resume`: 恢复锁定
- `admin_remove`: 管理员移除锁定
- `system_remove`: 系统自动移除锁定
- `batch_remove`: 批量移除（排课时）

**operator_type** (操作人类型):
- `member`: 会员
- `admin`: 管理员
- `system`: 系统

### member_fixed_slot_locks (会员固定课位锁定表)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| tenant_id | int | FK, NOT NULL | - | 租户ID |
| member_id | int | FK, NOT NULL | - | 会员ID |
| teacher_fixed_slot_id | int | FK, NOT NULL | - | 教师固定时间段ID |
| teacher_id | int | FK, NOT NULL | - | 教师ID（冗余字段） |
| weekday | int | NOT NULL | - | 星期几(1-7)（冗余字段） |
| start_time | time | NOT NULL | - | 开始时间（冗余字段） |
| status | enum | NOT NULL | 'active' | 锁定状态 |
| locked_at | timestamp | NOT NULL | CURRENT_TIMESTAMP | 锁定时间 |
| created_by | int | FK | NULL | 创建者ID |

**外键关系**:
- `tenant_id` → `tenants.id`
- `member_id` → `members.id`
- `teacher_fixed_slot_id` → `teacher_fixed_slots.id`
- `created_by` → `users.id`

**唯一约束**:
- `UNIQUE(teacher_fixed_slot_id)` - 同一时间段只能被一个会员锁定

**检查约束**:
- 确保冗余字段与关联表一致

**status** (锁定状态):
- `active`: 激活
- `paused`: 暂停
- `cancelled`: 取消

### course_templates (课程模板表)

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | PK | AUTO_INCREMENT | 主键 |
| tenant_id | int | FK, NOT NULL | - | 租户ID |
| name | string | NOT NULL | - | 课程名称 |
| duration_minutes | int | NOT NULL | 25 | 课程时长(分钟) |
| price | decimal | NOT NULL | 0.00 | 课程价格 |
| category | string | NOT NULL | - | 课程分类 |
| level | string | NOT NULL | - | 课程级别 |
| is_active | boolean | NOT NULL | true | 是否激活 |
| created_by | int | FK | NULL | 创建者ID |

**外键关系**:
- `tenant_id` → `tenants.id`
- `created_by` → `users.id`

## 业务规则

### 固定时间占位规则
1. **时间设置**:
   - 教师可设置周一到周日的固定时间表
   - 时间段以30分钟为单位（00:00-23:30）
   - 每个时间段可独立开放/关闭

2. **可用性管理**:
   - `is_available = true` 表示该时间段可被预约
   - `is_available = false` 表示该时间段不可用
   - 支持批量设置和单独调整

3. **唯一性约束**:
   - 同一教师在同一时间段只能有一条记录
   - 避免时间冲突

### 固定课位锁定规则
1. **锁定机制**:
   - 会员可锁定教师的固定时间段
   - 锁定后该时间段为会员专属
   - 自动排课时优先生成锁定会员的课程

2. **状态管理**:
   - `active`: 正常锁定，参与自动排课
   - `paused`: 暂停锁定，跳过自动排课
   - `cancelled`: 取消锁定，释放时间段

3. **冲突处理**:
   - 同一时间段只能被一个会员锁定
   - 锁定前需检查时间段可用性

### 自动排课规则
1. **触发机制**:
   - 默认每月22日下午14:00自动执行
   - 支持手动触发排课任务
   - 排课任务执行日志记录

2. **排课算法**:
   - 按教师优先级排课（欧美/南非 > 菲律宾）
   - 按教师编号从大到小排序
   - 为锁定固定位的会员生成具体课程安排
   - 生成未来4周的课程安排

3. **冲突检测**:
   - 检查教师时间可用性
   - 检查会员时间冲突
   - 检查会员卡余额充足性

### 课程管理规则
1. **时间精度**:
   - 课程时间精确到分钟：YYYY-MM-DD HH:MM
   - 标准课程时长25分钟，休息间隔5分钟
   - 支持自定义课程时长

2. **状态流转**:
   ```
   scheduled → completed
   scheduled → cancelled
   scheduled → no_show
   ```

3. **业务约束**:
   - 课程开始前可以取消
   - 课程结束后更新为完成或缺席
   - 取消的课程可以重新安排

## 索引设计

### 主要索引
```sql
-- 教师固定时间占位表
CREATE UNIQUE INDEX idx_teacher_fixed_slots_unique ON teacher_fixed_slots(teacher_id, weekday, time_slot);
CREATE INDEX idx_teacher_fixed_slots_teacher ON teacher_fixed_slots(teacher_id);
CREATE INDEX idx_teacher_fixed_slots_tenant ON teacher_fixed_slots(tenant_id);
CREATE INDEX idx_teacher_fixed_slots_available ON teacher_fixed_slots(is_available);

-- 已排课表
CREATE INDEX idx_scheduled_classes_tenant_datetime ON scheduled_classes(tenant_id, class_datetime);
CREATE INDEX idx_scheduled_classes_teacher_datetime ON scheduled_classes(teacher_id, class_datetime);
CREATE INDEX idx_scheduled_classes_member_datetime ON scheduled_classes(member_id, class_datetime);
CREATE INDEX idx_scheduled_classes_status ON scheduled_classes(tenant_id, status);

-- 会员固定课位锁定表
CREATE INDEX idx_member_fixed_locks_member ON member_fixed_slot_locks(member_id, status);
CREATE INDEX idx_member_fixed_locks_teacher ON member_fixed_slot_locks(teacher_id, weekday);
CREATE INDEX idx_member_fixed_locks_tenant ON member_fixed_slot_locks(tenant_id);

-- 课程模板表
CREATE INDEX idx_course_templates_tenant ON course_templates(tenant_id, is_active);
CREATE INDEX idx_course_templates_category ON course_templates(tenant_id, category);
```

## 常用查询模式

### 时间占位查询
```sql
-- 获取教师的固定时间表
SELECT weekday, time_slot, is_available
FROM teacher_fixed_slots
WHERE teacher_id = ?
ORDER BY weekday, time_slot;

-- 查找可用的教师时间段
SELECT t.id, t.name, tfs.weekday, tfs.time_slot
FROM teachers t
JOIN teacher_fixed_slots tfs ON t.id = tfs.teacher_id
WHERE t.tenant_id = ? 
  AND t.status = 'active'
  AND tfs.is_available = true
  AND tfs.weekday = ?
ORDER BY t.teacher_category, t.priority_level DESC;
```

### 排课查询
```sql
-- 获取指定时间范围的课程
SELECT sc.id, sc.class_datetime, sc.status,
       t.name as teacher_name, m.name as member_name
FROM scheduled_classes sc
JOIN teachers t ON sc.teacher_id = t.id
JOIN members m ON sc.member_id = m.id
WHERE sc.tenant_id = ?
  AND sc.class_datetime BETWEEN ? AND ?
ORDER BY sc.class_datetime;

-- 检查时间冲突
SELECT COUNT(*) as conflict_count
FROM scheduled_classes
WHERE teacher_id = ? 
  AND class_datetime = ?
  AND status IN ('scheduled', 'completed');
```

### 锁定管理
```sql
-- 获取会员的固定课位锁定
SELECT mfsl.weekday, mfsl.time_slot, mfsl.status,
       t.name as teacher_name
FROM member_fixed_slot_locks mfsl
JOIN teachers t ON mfsl.teacher_id = t.id
WHERE mfsl.member_id = ? AND mfsl.status = 'active'
ORDER BY mfsl.weekday, mfsl.time_slot;

-- 检查锁定冲突
SELECT COUNT(*) as lock_count
FROM member_fixed_slot_locks
WHERE teacher_id = ? 
  AND weekday = ? 
  AND time_slot = ?
  AND status = 'active';
```
