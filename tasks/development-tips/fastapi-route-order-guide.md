# FastAPI 路由顺序指南

## 🚨 核心问题

FastAPI **按定义顺序**匹配路由，错误的顺序会导致路由永远不被匹配。

### 常见错误

```python
# ❌ 错误：参数路径在前，固定路径永远不会被匹配
@router.post("/{id}/status")      # 会拦截 /batch/status
@router.post("/batch/status")     # 永远不会被匹配 ❌
```

### 正确顺序

```python
# ✅ 正确：固定路径在前
@router.post("/batch/status")     # 先匹配具体路径 ✅
@router.post("/{id}/status")      # 再匹配参数路径
```

## 📋 标准路由顺序

```python
# 1. 固定路径（最具体）
@router.get("/health")
@router.get("/stats")

# 2. 批量操作路径
@router.post("/batch/create")
@router.post("/batch/update")
@router.post("/batch/status")

# 3. 功能性路径
@router.get("/search")
@router.get("/available-slots")

# 4. 参数路径 + 固定后缀
@router.post("/{id}/status")
@router.post("/{id}/activate")
@router.post("/{id}/delete")

# 5. 纯参数路径（最不具体）
@router.get("/{id}")
```

## 🔧 检查工具

使用路由检查工具验证顺序：

```bash
python tasks/development-tips/simple-route-checker.py your_router.py
```

## 建议路由组织顺序

- 固定路径（如 `/health`, `/stats`）
- 批量操作（如 `/batch/create`, `/batch/update`）
- 功能性路径（如 `/search`, `/available-slots`）
- 带参数的具体路径（如 `/{id}/status`, `/{id}/activate`）
- 纯参数路径（如 `/{id}`）

## 📝 记忆口诀

**"固定优先，参数靠后"**

- 固定路径 > 批量操作 > 功能路径 > 参数路径
