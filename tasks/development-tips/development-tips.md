# 开发经验教训与最佳实践

## 概述

本文档总结了在开发过程中遇到的常见问题和最佳实践，旨在提高开发效率，避免重复犯错。

## 重要！！

1. 时区总是使用本地时区 datetime.now()

## 0. exception 相关

1. 不直接使用 HTTPException：应该使用项目的统一异常类
2. router 层尽量不要抛异常，而总是尽可能在 service 层抛出业务异常

## 1. 测试用例中的创建者模式

### ❌ 错误做法

```python
def test_create_entity(self, test_session: Session, created_tenant):
    service = EntityService(test_session, created_tenant["id"])
    entity = service.create_entity(entity_data, created_by=1)  # 硬编码ID
```

### ✅ 正确做法

```python
def test_create_entity(self, test_session: Session, created_admin_user):
    service = EntityService(test_session, created_admin_user["tenant_id"])
    entity = service.create_entity(entity_data, created_by=created_admin_user["id"])
```

**原因**：业务实体的创建者应该是用户，而非租户。tenant 信息应该从 user 中获取。

## 2. API 路由定义顺序

### ❌ 错误做法

```python
@router.get("/{id}")
def get_entity(id: int): ...

@router.get("/active")  # 这个路由永远不会被匹配到
def get_active_entities(): ...
```

### ✅ 正确做法

```python
@router.get("/active")  # 具体路径优先
def get_entity(id: int): ...

@router.get("/{id}")    # 通用路径在后
def get_entity(id: int): ...
```

**原因**：FastAPI 按定义顺序匹配路由，`/active` 会被 `/{id}` 误匹配为 `id="active"`。

## 3. Service 类初始化中的 RLS 上下文设置

### ❌ 错误做法

```python
class EntityService:
    def __init__(self, session: Session, tenant_id: int):
        self.session = session
        self.tenant_id = tenant_id
        # 忘记设置RLS上下文
```

### ✅ 正确做法

```python
class EntityService:
    def __init__(self, session: Session, tenant_id: Optional[int] = None):
        self.session = session
        self.tenant_id = tenant_id
        # 设置RLS上下文
        if tenant_id is not None:
            self.session.execute(text(f"SET app.current_tenant_id = '{tenant_id}'"))
        else:
            # 抛异常
```

**原因**：多租户系统需要设置行级安全(RLS)上下文，确保数据隔离。

## 4. 异常处理类的正确实现

### ❌ 错误做法

```python
# 错误：直接继承APIException
class EntityNotFoundError(APIException):
    def __init__(self, entity_id: int = None):
        message = f"实体不存在 (ID: {entity_id})"
        super().__init__(message=message, error_code="ENTITY_NOT_FOUND")

# 错误：没有定义专用错误码枚举
class EntityBusinessException(APIException):
    @classmethod
    def duplicate_name(cls, name: str):
        return cls(message=f"名称已存在: {name}", error_code="DUPLICATE_NAME")
```

### ✅ 正确做法

```python
# 1. 定义专用错误码枚举
class EntityErrorCode(BaseErrorCode):
    """实体模块错误码"""
    NAME_EXISTS = "ENTITY_NAME_EXISTS"
    CODE_EXISTS = "ENTITY_CODE_EXISTS"
    STATUS_INVALID = "ENTITY_STATUS_INVALID"

# 2. NotFound类继承NotFoundError
class EntityNotFoundError(NotFoundError):
    """实体不存在异常"""
    def __init__(self, entity_id: Optional[int] = None):
        if entity_id:
            super().__init__(f"ID为 {entity_id} 的实体")
        else:
            super().__init__("实体")

# 3. 业务异常类继承BusinessException
class EntityBusinessException(BusinessException):
    """实体业务异常"""

    @classmethod
    def name_already_exists(cls, name: str):
        return cls(
            f"名称 '{name}' 已存在",
            EntityErrorCode.NAME_EXISTS,
            ErrorLevel.WARNING
        )

    @classmethod
    def general_error(cls, message: str):
        return cls(message)  # 使用默认错误码
```

**原因**：遵循项目的异常处理架构，确保错误码的一致性和客户端的正确处理。

## 5. Pydantic 版本兼容性

### ❌ 错误做法

```python
from pydantic import BaseModel, Field, validator

class EntityQuery(BaseModel):
    sort_order: str = Field("desc", regex="^(asc|desc)$")

    @validator('name')
    def validate_name(cls, v):
        return v.strip()
```

### ✅ 正确做法

```python
from pydantic import BaseModel, Field, field_validator

class EntityQuery(BaseModel):
    sort_order: str = Field("desc", pattern="^(asc|desc)$")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        return v.strip()
```

**原因**：Pydantic V2 API 变更，`regex` → `pattern`，`@validator` → `@field_validator`。

## 6. FastAPI 查询参数类型处理

### ❌ 错误做法

```python
@router.post("/config/field")
def update_config_field(
    field_name: str,
    field_value: str,  # 所有查询参数都是字符串
    session: Session = Depends(get_session)
):
    # 这里field_value总是字符串，即使传入数字也会变成"75"
    service.update_field(field_name, field_value)
```

### ✅ 正确做法

```python
from typing import Union
from pydantic import BaseModel

class UpdateFieldRequest(BaseModel):
    field_name: str
    field_value: Union[str, int, float, bool]

@router.post("/config/field")
def update_config_field(
    request: UpdateFieldRequest,  # 使用请求体而不是查询参数
    session: Session = Depends(get_session)
):
    # 现在field_value保持原始类型
    service.update_field(request.field_name, request.field_value)
```

**原因**：FastAPI 的查询参数默认是字符串类型，需要复杂类型时应使用请求体。

## 7. 数据库模型注册

### ❌ 错误做法

```python
# 创建了新模型但忘记注册
# app/features/entities/models.py
class Entity(SQLModel, table=True):
    ...

# app/db/base.py - 忘记导入新模型
def create_db_and_tables():
    from app.features.users.models import User
    # 忘记导入 Entity 模型
```

### ✅ 正确做法

```python
# app/db/base.py
def create_db_and_tables():
    # 导入所有模型以确保它们被注册
    from app.features.users.models import User
    from app.features.entities.models import Entity  # 新模型必须导入

    SQLModel.metadata.create_all(engine)
```

**原因**：SQLModel 需要导入才能创建对应的数据库表，忘记导入会导致表不存在。

## 8. 测试 fixture 的依赖关系

### ❌ 错误做法

```python
@pytest.fixture
def created_entity(test_session: Session, created_tenant):
    # 错误：直接依赖created_tenant
    pass
```

### ✅ 正确做法

```python
@pytest.fixture
def created_entity(test_session: Session, created_admin_user):
    # 正确：依赖created_admin_user，从中获取tenant信息
    tenant_id = created_admin_user["tenant_id"]
    pass
```

**原因**：保持 fixture 依赖关系的一致性，遵循"创建者是 user"的原则。

## 9. API 响应格式一致性

### ❌ 错误做法

```python
# 不同接口返回不同格式
return {"data": entity}
return entity
return {"result": entity, "status": "ok"}
```

### ✅ 正确做法

```python
# 使用统一的响应格式
from app.api.common.responses import success_response, page_response

return success_response(entity, "创建成功")
return page_response(entities, total, page, size, "获取列表成功")
```

**原因**：保持 API 响应格式的一致性，便于前端统一处理。

## 10. 数据库会话管理

### ❌ 错误做法

```python
def create_entity(self, entity_data):
    entity = Entity(**entity_data.dict())
    self.session.add(entity)
    # 忘记commit
    return entity
```

### ✅ 正确做法

```python
def create_entity(self, entity_data):
    entity = Entity(**entity_data.dict())
    self.session.add(entity)
    self.session.commit()
    self.session.refresh(entity)  # 获取数据库生成的字段
    return entity
```

**原因**：确保数据正确保存到数据库，并获取数据库生成的字段值。

## 11. 测试用例中的 404 错误调试

### ❌ 错误做法

```python
def test_update_config_success(self, client, admin_token):
    # 没有先创建配置就直接更新
    response = client.post("/api/v1/courses/config/update", ...)
    # 得到404错误，但不知道是路由问题还是数据问题
```

### ✅ 正确做法

```python
def test_update_config_success(self, client, admin_token, default_course_config):
    # 使用fixture先创建默认配置
    response = client.post("/api/v1/courses/config/update", ...)
    # 现在能正确测试更新功能
```

**原因**：404 错误可能是路由不存在，也可能是业务逻辑抛出 NotFoundError，需要先排除数据问题。

## 12. 测试 fixture 的命名和依赖

### ❌ 错误做法

```python
def test_multi_tenant_isolation(self, client, admin_token, second_tenant_admin_token):
    # 使用不存在的fixture名称
```

### ✅ 正确做法

```python
# 先创建对应的fixture
@pytest.fixture
def second_tenant_admin_token(client, created_second_tenant_admin):
    # 实现获取第二个租户管理员token的逻辑
    pass

def test_multi_tenant_isolation(self, client, admin_token, second_tenant_admin_token):
    # 现在可以正常使用
```

**原因**：测试用例依赖的 fixture 必须存在，需要按需创建缺失的 fixture。

## 13. 服务层方法返回值一致性

### ❌ 错误做法

```python
def validate_config_consistency(self) -> dict:
    # 返回复杂的字典结构
    return {"valid": True, "errors": []}

# 路由中需要额外处理
result = service.validate_config_consistency()
return success_response({"is_valid": result["valid"]}, "验证完成")
```

### ✅ 正确做法

```python
def validate_config_consistency(self) -> bool:
    # 返回简单的布尔值
    return True

# 路由中直接使用
is_valid = service.validate_config_consistency()
return success_response({"is_valid": is_valid}, "验证完成")
```

**原因**：服务层应该返回简单、一致的数据类型，复杂的响应格式化应该在路由层处理。

## 14. FastAPI 路由匹配顺序问题

### ❌ 错误做法

```python
# 路由定义顺序错误
@router.post("/{lock_id}/status")
def update_lock_status(lock_id: int): ...

@router.post("/batch/status")  # 永远不会被匹配到
def batch_update_status(): ...
```

### ✅ 正确做法

```python
# 具体路径优先于参数路径
@router.post("/batch/status")   # 具体路径在前
def batch_update_status(): ...

@router.post("/{lock_id}/status")  # 参数路径在后
def update_lock_status(lock_id: int): ...
```

**原因**：FastAPI 按定义顺序匹配路由，`/batch/status` 会被 `/{lock_id}/status` 误匹配为 `lock_id="batch"`，导致类型验证错误。

**错误信息示例**：

```
'Input should be a valid integer, unable to parse string as an integer', 'input': 'batch'
```

**避免技巧**：

1. **使用路由检查工具**：

   ```bash
   python tasks/development-tips/simple-route-checker.py app/features/members/fixed_lock_router.py
   ```

2. **按优先级组织路由**：

   ```python
   # 1. 固定路径（最具体）
   @router.get("/health")
   @router.get("/stats")

   # 2. 批量操作路径
   @router.post("/batch/status")
   @router.post("/batch/update")

   # 3. 功能性路径
   @router.get("/available-slots")
   @router.get("/search")

   # 4. 带参数的具体路径
   @router.post("/{id}/status")
   @router.post("/{id}/activate")

   # 5. 纯参数路径（最不具体）
   @router.get("/{id}")
   ```

3. **使用路由分组**：

   ```python
   # 创建子路由器
   batch_router = APIRouter(prefix="/batch")
   @batch_router.post("/status")
   @batch_router.post("/update")

   # 包含到主路由器
   main_router.include_router(batch_router)
   ```

4. **使用明确的路径前缀**：
   ```python
   # ✅ 使用明确前缀避免冲突
   @router.post("/operations/batch-status")
   @router.post("/resources/{id}/status")
   ```

## 15. 数据库枚举类型与整数比较问题

### ❌ 错误做法

```python
# 直接将整数列表与枚举字段比较
if query.weekdays:
    conditions.append(TeacherFixedSlot.weekday.in_(query.weekdays))  # query.weekdays 是 [1,2,3]
```

### ✅ 正确做法

```python
# 将整数转换为枚举类型
if query.weekdays:
    from app.features.teachers.fixed_slots_models import Weekday
    weekday_enums = [Weekday(weekday) for weekday in query.weekdays]
    conditions.append(TeacherFixedSlot.weekday.in_(weekday_enums))
```

**原因**：PostgreSQL 不能直接比较枚举类型和整数类型，需要在查询前进行类型转换。

**错误信息示例**：

```
operator does not exist: weekday = integer
```

## 总结

遵循这些最佳实践可以：

1. 减少开发过程中的常见错误
2. 保持代码的一致性和可维护性
3. 提高开发效率
4. 确保系统的稳定性和安全性

在开发新功能时，请参考现有模块的实现模式，保持项目架构的一致性。
