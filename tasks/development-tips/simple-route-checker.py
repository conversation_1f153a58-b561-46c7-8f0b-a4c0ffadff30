#!/usr/bin/env python3
"""
简化版 FastAPI 路由顺序检查工具
"""

import re
import sys
from typing import List, <PERSON><PERSON>


def extract_routes_from_file(file_path: str) -> List[Tuple[str, str, int]]:
    """从文件中提取路由信息 (method, path, line_number)"""
    routes = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用更宽松的正则表达式匹配路由
        pattern = r'@router\.(get|post|put|delete|patch)\s*\(\s*["\']([^"\']+)["\']'
        matches = re.finditer(pattern, content, re.MULTILINE)
        
        for match in matches:
            method = match.group(1).upper()
            path = match.group(2)
            # 计算行号
            line_number = content[:match.start()].count('\n') + 1
            routes.append((method, path, line_number))
        
        return routes
    
    except Exception as e:
        print(f"读取文件出错: {e}")
        return []


def check_route_conflicts(routes: List[Tuple[str, str, int]]) -> List[dict]:
    """检查路由冲突"""
    conflicts = []
    
    for i, (method1, path1, line1) in enumerate(routes):
        for j, (method2, path2, line2) in enumerate(routes[i+1:], i+1):
            # 只检查相同HTTP方法的路由
            if method1 != method2:
                continue
            
            # 检查常见的冲突模式
            if is_conflicting_routes(path1, path2):
                conflicts.append({
                    'earlier_route': {'method': method1, 'path': path1, 'line': line1},
                    'later_route': {'method': method2, 'path': path2, 'line': line2},
                    'issue': f"路由 '{path2}' 可能永远不会被匹配，因为会被 '{path1}' 拦截"
                })
    
    return conflicts


def is_conflicting_routes(earlier_path: str, later_path: str) -> bool:
    """检查两个路由是否冲突"""
    # 常见冲突模式检查
    
    # 1. 参数路径遮蔽固定路径
    # 例如: /{id} 会遮蔽 /active
    if '{' in earlier_path and '{' not in later_path:
        # 将参数路径转换为正则模式进行简单检查
        pattern = re.sub(r'\{[^}]+\}', r'[^/]+', earlier_path)
        pattern = f"^{pattern}$"
        try:
            if re.match(pattern, later_path):
                return True
        except:
            pass
    
    # 2. 检查 /batch/xxx 被 /{id}/xxx 遮蔽的情况
    if '/{' in earlier_path and '/batch/' in later_path:
        # 提取路径结构
        earlier_parts = earlier_path.split('/')
        later_parts = later_path.split('/')
        
        if len(earlier_parts) == len(later_parts):
            # 检查是否结构相同，只是参数位置不同
            for i, (e_part, l_part) in enumerate(zip(earlier_parts, later_parts)):
                if e_part.startswith('{') and l_part == 'batch':
                    return True
    
    return False


def get_optimization_suggestions(routes: List[Tuple[str, str, int]]) -> List[str]:
    """获取优化建议"""
    suggestions = []
    
    # 按HTTP方法分组
    method_groups = {}
    for method, path, line in routes:
        if method not in method_groups:
            method_groups[method] = []
        method_groups[method].append((path, line))
    
    for method, paths_lines in method_groups.items():
        # 检查是否有参数路径在固定路径之前
        for i, (path, line) in enumerate(paths_lines):
            if '{' in path:
                # 检查后面是否还有固定路径
                for later_path, later_line in paths_lines[i+1:]:
                    if '{' not in later_path:
                        suggestions.append(
                            f"建议将 {method} '{later_path}' (第{later_line}行) 移到 '{path}' (第{line}行) 之前"
                        )
    
    return suggestions


def main():
    if len(sys.argv) != 2:
        print("用法: python simple-route-checker.py <router_file.py>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    routes = extract_routes_from_file(file_path)
    
    if not routes:
        print("未找到路由定义")
        return
    
    print(f"=== 路由分析: {file_path} ===")
    print(f"总路由数: {len(routes)}")
    print()
    
    # 检查冲突
    conflicts = check_route_conflicts(routes)
    if conflicts:
        print("🚨 发现潜在路由冲突:")
        for i, conflict in enumerate(conflicts, 1):
            print(f"{i}. {conflict['issue']}")
            print(f"   早定义的路由: {conflict['earlier_route']['method']} {conflict['earlier_route']['path']} (第{conflict['earlier_route']['line']}行)")
            print(f"   晚定义的路由: {conflict['later_route']['method']} {conflict['later_route']['path']} (第{conflict['later_route']['line']}行)")
            print()
    else:
        print("✅ 未发现明显的路由冲突")
    
    # 获取建议
    suggestions = get_optimization_suggestions(routes)
    if suggestions:
        print("💡 优化建议:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"{i}. {suggestion}")
        print()
    
    # 显示路由列表
    print("📋 路由列表 (按定义顺序):")
    for i, (method, path, line) in enumerate(routes, 1):
        specificity = "参数路径" if '{' in path else "固定路径"
        print(f"{i:2}. {method:4} {path:30} (第{line:3}行) [{specificity}]")


if __name__ == "__main__":
    main() 