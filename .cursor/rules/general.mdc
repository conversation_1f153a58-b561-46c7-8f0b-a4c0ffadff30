---
description: 
globs: 
alwaysApply: true
---
# 项目通用规范


## 技术栈
- **Web 框架**: FastAPI
- **ORM**: SQLModel (SQLAlchemy + Pydantic)
- **数据库**: PostgreSQL
- **认证**: JWT (JSON Web Tokens)
- **密码加密**: bcrypt
- **数据验证**: Pydantic
- **API 文档**: OpenAPI/Swagger (自动生成)

## 项目结构规则
- **分层组织**：按功能或领域划分目录，遵循"关注点分离"原则
- **命名一致**：使用一致且描述性的目录和文件命名，反映其用途和内容
- **模块化**：相关功能放在同一模块，减少跨模块依赖
- **适当嵌套**：避免过深的目录嵌套，一般不超过3-4层
- **资源分类**：区分代码、资源、配置和测试文件
- **依赖管理**：集中管理依赖，避免多处声明
- **约定优先**：遵循语言或框架的标准项目结构约定

## 通用开发原则
- **可测试性**：编写可测试的代码，组件应保持单一职责
- **DRY 原则**：避免重复代码，提取共用逻辑到单独的函数或类
- **代码简洁**：保持代码简洁明了，遵循 KISS 原则（保持简单直接）
- **命名规范**：使用描述性的变量、函数和类名，反映其用途和含义
- **注释文档**：为复杂逻辑添加注释，编写清晰的文档说明功能和用法
- **风格一致**：遵循项目或语言的官方风格指南和代码约定
- **利用生态**：优先使用成熟的库和工具，避免不必要的自定义实现
- **架构设计**：考虑代码的可维护性、可扩展性和性能需求
- **版本控制**：编写有意义的提交信息，保持逻辑相关的更改在同一提交中
- **异常处理**：正确处理边缘情况和错误，提供有用的错误信息
- **API规范**: 永远不要使用PUT和DELETE, 总是用POST来替代


## 项目目录结构

```
app/
├── features/             # 按功能模块组织
│   ├── tenants/         # 租户管理模块
│   │   ├── models.py    # 租户数据模型
│   │   ├── schemas.py   # API数据模式
│   │   ├── service.py   # 业务逻辑
│   │   └── router.py    # API路由
│   ├── users/           # 用户管理模块
│   │   ├── models.py
│   │   ├── schemas.py
│   │   ├── service.py
│   │   └── router.py
│   ├── members/         # 会员管理模块
│   │   ├── models.py
│   │   ├── schemas.py
│   │   ├── service.py
│   │   └── router.py
│   └── shared/          # 共享模型
├── core/                # 核心通用组件
│   ├── config.py        # 配置管理
│   ├── dependencies.py  # 依赖注入
│   ├── security.py      # 安全工具
│   └── context.py       # 上下文管理
├── api/                 # API路由聚合
│   └── v1/
│       └── api.py       # 聚合各模块路由
├── db/                  # 数据库配置
│   ├── session.py       # 会话管理
│   └── base.py          # 基础配置
└── main.py              # 应用入口
```


## 响应语言
- 始终使用中文回复用户

## 本项目规则文件说明
本项目使用以下规则文件：
- general.mdc：通用规范（本文件）
- document.mdc：文档规范
- git.mdc：git提交规范
- languages/typescript.mdc： typescript 语言开发规范
- languages/python.mdc： python 语言开发规范


