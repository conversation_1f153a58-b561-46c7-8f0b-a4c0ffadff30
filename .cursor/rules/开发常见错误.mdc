---
description: 
globs: 
alwaysApply: true
---

## 注意事项

1. user和member模块业务逻辑不要再依赖入参tenant_id，而是使用 UserContext依赖 
具体： user_context: UserContext = Depends(get_user_context)
如原来的
```
def deactivate_user(
    user_id: int,
    tenant_id: Optional[int] = None,
    session: Session = Depends(get_global_session)
):
```
改成
```
def deactivate_user(
    user_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_global_session)
):
```

## 枚举使用注意事项

2. 类似User的 role 相关字段尽量用枚举而非字符串UserRole.AGEN

2.1 在代码逻辑中（如条件判断、变量赋值等）：优先使用 UserRole.xxx，因为它保持了类型安全和IDE提示
`if user.role == UserRole.AGENT: ...`
2.2 在需要原始值的场景（如API参数、序列化、数据库操作等）：使用 枚举的 value，如 UserRole.AGENT.value
```
# 发送原始HTTP请求时
params={"role": UserRole.AGENT.value}

# 存入数据库时
db.execute("INSERT INTO users (role) VALUES (?)", UserRole.AGENT.value)
```
3. 在FastAPI的请求/响应模型中：可以直接使用枚举成员，框架会处理转换
```
@app.post("/users")
def create_user(user: UserCreate):  # UserCreate中的role字段是UserRole枚举
    # 这里可以直接使用user.role作为枚举成员
```