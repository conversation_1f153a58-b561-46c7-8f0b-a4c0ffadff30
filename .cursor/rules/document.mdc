---
description: 
globs: *.md
alwaysApply: false
---
# 文档规范

## 通用要求
- 所有文档使用Markdown格式
- 使用简洁、清晰的语言
- 文档内容应保持最新
- 避免拼写和语法错误
- 使用中文作为主要语言

## 目录结构

- `docs/`：存放详细文档
  - `需求分析/`：存放需求相关文档
    - `业务需求.md`：业务背景、目标、范围
    - `功能需求.md`：核心功能列表及描述
    - `非功能需求.md`：性能、安全等要求
  - `概要设计/`：存放系统设计文档
    - `架构设计.md`：系统架构图及说明
    - `数据库设计.md`：ER图及表结构
  - `详细设计/`：存放模块设计文档
    - `模块1/`：按模块划分
      - `设计说明.md`：模块功能、接口、流程
      - `接口文档.md`：API定义
    - `模块2/`：同上
  - `开发计划/`：存放项目计划
    - `里程碑.md`：关键时间节点
    - `迭代计划.md`：迭代周期及目标
  - `测试方案/`：存放测试相关文档
    - `测试计划.md`：测试范围、策略
    - `测试用例.md`：功能测试用例
- `dev-log/`: 存放开发阶段迭代记录
    - `sprint-{n}/`: 每个迭代周期的记录
        - `goals.md`: 迭代目标
        - `progress.md`: 每日进展
        - `retrospective.md`: 迭代回顾
    - `features/`: 按功能点记录
        - `feature-{name}/`: 每个功能开发记录
        - `design.md`: 设计思路
        - `progress.md`: 开发进展
        - `issues.md`: 遇到的问题


## README.md 内容规范
- 项目名称和简短描述
- 技术栈说明
- 项目结构说明
- 使用说明
- 许可证信息

## 版本记录规范
- 使用 `CHANGELOG.md` 记录版本变更
- 遵循语义化版本（Semantic Versioning）规范
- 每个版本应包含：新增功能、修复问题、破坏性变更

## 文档内容组织
- 从整体到局部，从简单到复杂
- 重要信息放在前面
- 相关内容应当放在一起
- 使用小标题和列表增强可读性
- 避免过长段落，保持内容简洁

## 代码示例规范
- 提供完整可运行的示例
- 代码应当简洁且易于理解
- 添加适当的注释解释关键部分
- 说明代码的预期输出或行为
- 更新示例以匹配最新API






