---
description: 
globs: 
alwaysApply: false
---

1. 总是按照规则 [process-task-list.mdc](mdc:.cursor/rules/process-task-list.mdc) 来执行任务  
2. 总是从 [tasks-prd-ks-english-admin-backend-complete.md](mdc:tasks/tasks-prd-ks-english-admin-backend-complete.md) 读取任务
3. 具体设计和表结构参考 [final-course-system-design.md](mdc:tasks/database-design/final-course-system-design.md) ，课程相关详细设计参考 [course-system-summary.md](mdc:tasks/database-design/course-system-summary.md)
4. 编码注意事项 [development-tips.md](mdc:tasks/development-tips/development-tips.md)
5. 测试用例规范 [QUICK_TEST_GUIDE.md](mdc:tests/QUICK_TEST_GUIDE.md) 
6. 路由规范和tips [fastapi-route-order-guide.md](mdc:tasks/development-tips/fastapi-route-order-guide.md) 
7. 任务git规则
7.1 提交git时候，title带上任务编号
7.2 如果任务列表有3级，则每次开始执行一个2级子任务时，先git开一个分支，然后再开始执行任务或者3级子任务，每当执行完毕整个该2级子任务并通过测试时，提交commit后，等待用户确认后，再合并分支
example： 
task 6.1 
下面有 6.1.1，6.1.2，6.1.3
则开始执行6.1时候开一个分支


## 好的实践总结
1. 渐进式开发
先实现核心功能，再完善边界情况
每完成一个子任务就提交，便于问题定位和回滚
2. 测试驱动修复
发现问题时先写测试用例复现
修复后确保测试通过，避免回归
3. 事务边界清晰
相关操作（扣费+预约）放在同一事务中
提供不同粒度的方法支持事务控制
4. 异常处理统一
使用项目统一的 BusinessException
避免直接抛出 Exception

## 接下来任务的建议
1. 提前确认需求：对于复杂业务逻辑，先与需求方确认细节
2. 测试先行：修改业务逻辑前先确保测试数据的一致性
3. 事务设计：涉及多表操作时，提前设计好事务边界
4. 渐进实现：复杂功能分步实现，每步都要能独立测试
5. 异常处理：统一使用项目规范的异常处理机制