---
description: 
globs: 
alwaysApply: false
---
## 目标

指导 AI 助手基于现有产品需求文档（PRD）创建详细的、分步骤的 Markdown 格式任务列表。该任务列表应引导开发人员完成实现工作。

## 输出

- **格式**：Markdown（`.md`）
- **位置**：`/tasks/`
- **文件名**：`tasks-[prd-file-name].md`（例如：`tasks-prd-user-profile-editing.md`）

## 流程

1. **接收 PRD 引用**：用户向 AI 指定特定的 PRD 文件
2. **分析 PRD**：AI 读取并分析指定 PRD 的功能需求、用户故事及其他部分
3. **阶段 1：生成父任务**：基于 PRD 分析，创建文件并生成实现该功能所需的主要高级任务。自行判断使用多少个高级任务，可能约为 5 - 10 个。以指定格式向用户展示这些任务（暂不包含子任务）。告知用户："我已根据 PRD 生成高级任务。准备好生成子任务了吗？回复 'Go' 继续。"
4. **等待确认**：暂停并等待用户回复 "Go"
5. **阶段 2：生成子任务**：用户确认后，将每个父任务分解为更小的、可执行的子任务，这些子任务是完成父任务所必需的。确保子任务与父任务逻辑连贯，并涵盖 PRD 中隐含的实现细节
6. **识别相关文件**：根据任务和 PRD，识别需要创建或修改的潜在文件。在`相关文件`部分列出这些文件，若适用则包括相应的测试文件
7. **生成最终输出**：将父任务、子任务、相关文件和注释合并为最终的 Markdown 结构
8. **保存任务列表**：将生成的文档保存在`/tasks/`目录中，文件名为`tasks-[prd-file-name].md`，其中`[prd-file-name]`与输入 PRD 文件的基名匹配（例如：若输入为`prd-user-profile-editing.md`，则输出为`tasks-prd-user-profile-editing.md`）

## 输出格式

生成的任务列表尽量遵循以下结构：

~~~markdown
## 模块化架构

```
app/
├── features/             # 按功能模块组织
│   ├── tenants/         # 租户管理模块
│   │   ├── models.py    # 租户数据模型
│   │   ├── schemas.py   # API数据模式
│   │   ├── service.py   # 业务逻辑
│   │   └── router.py    # API路由
│   ├── users/           # 用户管理模块
│   │   ├── models.py
│   │   ├── schemas.py
│   │   ├── service.py
│   │   └── router.py
│   ├── members/         # 会员管理模块
│   │   ├── models.py
│   │   ├── schemas.py
│   │   ├── service.py
│   │   └── router.py
│   └── shared/          # 共享模型
├── core/                # 核心通用组件
│   ├── config.py        # 配置管理
│   ├── dependencies.py  # 依赖注入
│   ├── security.py      # 安全工具
│   └── context.py       # 上下文管理
├── api/                 # API路由聚合
│   └── v1/
│       └── api.py       # 聚合各模块路由
├── db/                  # 数据库配置
│   ├── session.py       # 会话管理
│   └── base.py          # 基础配置
└── main.py              # 应用入口
```


## 测试目录结构
分层的测试架构，按照测试类型和职责进行组织：

```
tests/
├── conftest.py                    # 全局配置和基础fixtures
├── fixtures/                     # 分类的fixture文件
│   ├── database.py               # 数据库相关fixtures
│   ├── client.py                 # API客户端fixtures
│   ├── auth.py                   # 认证相关fixtures （尚未创建文件，看需要是否创建）
│   └── business/                 # 业务数据fixtures
│       ├── tenant.py             # 租户相关fixtures
│       ├── user.py               # 用户相关fixtures
│       └── member.py             # 会员相关fixtures
├── unit/                         # 单元测试
│   ├── services/                 # 服务层测试
│   │   ├── test_tenant_service.py # 租户相关service层测试
│   │   ├── test_user_service.py   # 用户相关service层测试
│   │   └── test_member_service.py # 会员相关service层测试
│   └── utils/                    # 工具函数测试
│       └── test_security.py      # （尚未创建文件，看需要是否创建）
├── integration/                  # 集成测试
│   ├── api/                      # API集成测试
│   │   ├── v1/
│   │   │   ├── test_tenant_api.py # 租户相关api层测试
│   │   │   ├── test_user_api.py   # 用户相关api层测试
│   │   │   ├── test_member_api.py # 会员相关api层测试
│   │   └── xxx.py           # API测试专用fixtures （未创建文件，看需要是否创建）
├── e2e/                          # 端到端测试 日常开发不用实现，小版本封版时候统一做
│   ├── scenarios/
│   │   ├── test_manual_scenarios.py
│   └── conftest.py               # E2E测试专用fixtures （未创建文件，看需要是否创建）
└── performance/                  # 性能测试（暂不实现）
    ├── test_api_performance.py   # （暂不实现，未创建文件）
    └── conftest.py               # （暂不实现，未创建文件）
```

## 任务

- [ ] 1.0 父任务标题
  - [ ] 1.1 [子任务描述1.1] 
  - [ ] 1.2 [子任务描述1.2]
- [ ] 2.0 父任务标题
  - [ ] 2.1 [子任务描述2.1]
- [ ] 3.0 父任务标题（若纯为结构性或配置性任务，可能不需要子任务）
- [ ] 4.0 父任务标题（若任务很复杂，可以考虑拆分三级任务）
  - [ ] 4.1 [子任务描述4.1]
     - [ ] 4.11 [子任务描述4.11]
~~~



## 交互模型

该流程明确要求在生成父任务后暂停，以获取用户确认（"Go"），然后再继续生成详细的子任务。这确保在深入细节之前，高级计划与用户期望一致。

## 目标受众

假定任务列表的主要读者是将实现该功能的**初级开发人员**。